# Transaction Reference Code Implementation

This document provides step-by-step instructions for implementing company-specific transaction reference codes in the FinancialTracker system, following the same pattern as partners, loans, and customers.

## Overview

Transaction reference codes are unique identifiers for transactions within a company, following the same pattern as collection IDs, agent reference codes, loan reference codes, customer reference codes, and partner reference codes. The format is:

```
[COMPANY_PREFIX]-T-[SEQUENTIAL_NUMBER]
```

For example:
- "GS-T-001" for the first transaction in "GOVINDARAJI S" company
- "CS-T-001" for the first transaction in "Cloud Stier" company

The implementation consists of:
1. Adding a new column to the database through a migration
2. Setting a default empty value for existing records
3. Using the existing `getCompanyName` function for company prefix generation
4. Implementing the sequential number generation
5. Updating the transaction creation process to generate and store reference codes
6. Updating the UI to display the reference codes

## Implementation Details

### 1. Database Schema Changes

#### Step 1: Create SQL Migration
Create a migration file `migrations/009_add_transaction_reference_code.sql` with the following SQL:

```sql
-- Migration to add transaction_reference_code column to transactions table
-- This column will store company-specific transaction identifiers

-- Add transaction_reference_code column to transactions table
ALTER TABLE "transactions"
  ADD COLUMN IF NOT EXISTS "transaction_reference_code" TEXT;

-- Create an index for faster lookups by transaction_reference_code
CREATE INDEX IF NOT EXISTS idx_transactions_reference_code ON transactions(transaction_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN transactions.transaction_reference_code IS 'Company-specific transaction identifier string (e.g., GS-T-001) that is unique within each company';
```

#### Step 2: Create Migration Runner Script
Create a script `run-transaction-reference-migration.js` to run the migration:

```javascript
// Script to run the transaction reference code migration
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function runMigration() {
  console.log('Starting transaction reference code migration...');

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', '009_add_transaction_reference_code.sql');
    console.log(`Reading migration file from: ${migrationPath}`);

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    console.log('Executing migration...');
    await pool.query(migrationSQL);

    // Verify the migration
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'transactions' AND column_name = 'transaction_reference_code'
    `);

    if (columnCheck.rows.length > 0) {
      console.log('✅ Migration verified successfully!');
      console.log('✅ transaction_reference_code column added to transactions table');
    } else {
      console.log('❌ Migration verification failed: transaction_reference_code column not found');
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the migration
runMigration();
```

#### Step 3: Update Schema Definition
Update the transactions table definition in `shared/schema.ts`:

```typescript
// Transactions
export const transactions = pgTable('transactions', {
  id: serial('id').primaryKey(),
  account_id: integer('account_id').references(() => accounts.id, { onDelete: 'cascade' }).notNull(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'set null' }),
  transaction_date: timestamp('transaction_date').notNull(),
  transaction_type: transactionTypeEnum('transaction_type').notNull(),
  amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
  description: text('description').notNull(),
  reference_type: text('reference_type'),
  reference_id: integer('reference_id'),
  transaction_reference_code: text('transaction_reference_code'), // Add this line
  created_by: integer('created_by').references(() => users.id, { onDelete: 'set null' }),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

Also update the insert schema to include the new field:

```typescript
export const insertTransactionSchema = createInsertSchema(transactions)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    transaction_reference_code: z.string().optional()
  });
```

#### Step 4: Update Migration Runner
Update the `run-neon-migrations.js` file to include the new migration:

```javascript
// Define migrations to run in order
const migrations = [
  // ... existing migrations ...
  {
    name: '009_add_transaction_reference_code',
    path: path.join(__dirname, 'migrations', '009_add_transaction_reference_code.sql'),
    verification: async (pool) => {
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'transactions' AND column_name = 'transaction_reference_code'
      `);
      return result.rows.length > 0;
    }
  }
];
```

### 2. Setting Default Value for Existing Records

After adding the column, we need to set a default empty value for all existing transaction records.

#### Step 5: Create Script to Update Existing Records
Create a script `update-transaction-reference-codes.js` to update all existing transactions with an empty value:

```javascript
// Script to update all existing transactions to have an empty value in the transaction_reference_code column
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function updateTransactionReferenceCodes() {
  console.log('Starting update of transaction_reference_code values...');

  try {
    // First, check if there are any transactions in the database
    const countResult = await pool.query(`
      SELECT COUNT(*) FROM transactions
    `);

    const transactionCount = parseInt(countResult.rows[0].count);
    console.log(`Found ${transactionCount} transactions in the database`);

    if (transactionCount === 0) {
      console.log('No transactions to update. Exiting.');
      return;
    }

    // Update all transactions to have an empty value in the transaction_reference_code column
    const updateResult = await pool.query(`
      UPDATE transactions
      SET transaction_reference_code = ''
      WHERE transaction_reference_code IS NULL
      RETURNING id
    `);

    const updatedCount = updateResult.rows.length;
    console.log(`Updated ${updatedCount} transactions with empty transaction_reference_code`);

    // Verify the update
    const verifyResult = await pool.query(`
      SELECT COUNT(*) FROM transactions WHERE transaction_reference_code = ''
    `);

    const verifiedCount = parseInt(verifyResult.rows[0].count);
    console.log(`Verified ${verifiedCount} transactions now have empty transaction_reference_code`);

    console.log('Update completed successfully!');
  } catch (error) {
    console.error('Error updating transaction reference codes:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the update
updateTransactionReferenceCodes();
```

### 3. Company Prefix Generation

The company prefix generation is already implemented in the existing `getCompanyName` function from `server/routes.ts`. This function generates prefixes like:
- "GOVINDARAJI S" → "GS"
- "Cloud Stier" → "CS"

For transactions, we'll use the format: `[COMPANY_PREFIX]-T-[SEQUENTIAL_NUMBER]`
- The "T" indicates it's a transaction reference code
- This distinguishes it from other reference types (loans: L, collections: no suffix, etc.)

### 4. Sequential Number Generation

#### Step 6: Implement getHighestTransactionSerial Method
Add the `getHighestTransactionSerial` method to the `TransactionStorage` class in `server/storage/financial/transaction.storage.ts`:

```typescript
/**
 * Get the highest serial number for a given company and prefix (e.g., 'GS-T-')
 * Returns the highest serial as a number, or 0 if none found.
 * Only considers transactions from the specific company.
 */
async getHighestTransactionSerial(companyId: number, prefix: string): Promise<number> {
  try {
    // Find the max serial for this company and prefix
    // transaction_reference_code is like 'GS-T-001', 'GS-T-002', ...
    const result = await db.select({ maxString: sql`MAX(${transactions.transaction_reference_code})` })
      .from(transactions)
      .where(
        and(
          eq(transactions.company_id, companyId),
          like(transactions.transaction_reference_code, `${prefix}%`)
        )
      );
    const maxString = result[0]?.maxString as string | undefined;
    if (!maxString) return 0;

    // Extract the serial part (e.g., 'GS-T-012' => 12)
    const match = maxString.match(/^(.*-T-)(\d{3})$/);
    if (match) {
      return parseInt(match[2], 10);
    }
    return 0;
  } catch (error) {
    errorLogger.logError(`Error getting highest transaction serial for company ${companyId} and prefix ${prefix}`, 'transaction-serial', error as Error);
    return 0;
  }
}
```

Add the necessary imports at the top of the file:

```typescript
import { db } from '../../db';
import { eq, and, like, sql } from 'drizzle-orm';
import { transactions } from '@shared/schema';
import { Transaction, InsertTransaction } from '@shared/schema';
import errorLogger from '../../utils/errorLogger';
```

### 5. Transaction Creation Process

#### Step 7: Update Transaction Creation in Financial Management
Update the `createJournalEntry` function in `server/financialManagement.ts` to generate transaction reference codes:

```typescript
// Generate company-specific transaction reference codes for each transaction
const companyPrefix = await getCompanyName(journalEntry.company_id);
console.log(`Generated company prefix for transaction reference codes: ${companyPrefix}`);

// Get the transaction storage instance
const transactionStorage = new TransactionStorage();

// Create transactions with reference codes
const createdTransactions: Transaction[] = [];

for (const [key, entry] of Object.entries(journalEntry.entries)) {
  // Get the highest existing transaction reference code for this company
  const highestSerial = await transactionStorage.getHighestTransactionSerial(
    journalEntry.company_id,
    `${companyPrefix}-T-`
  );
  const nextSerial = highestSerial + 1;
  const serialString = nextSerial.toString().padStart(3, '0');
  const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

  console.log(`Generated transaction reference code: ${transactionReferenceCode} for company ${journalEntry.company_id}`);

  // Create transaction with reference code
  const transactionData: InsertTransaction = {
    account_id: entry.account_id,
    company_id: journalEntry.company_id,
    branch_id: journalEntry.branch_id,
    transaction_date: journalEntry.transaction_date,
    transaction_type: key.startsWith('debit') ? 'debit' : 'credit',
    amount: entry.amount,
    description: journalEntry.description,
    reference_type: journalEntry.reference_type,
    reference_id: journalEntry.reference_id,
    transaction_reference_code: transactionReferenceCode,
    created_by: journalEntry.created_by
  };

  const transaction = await transactionStorage.createTransaction(transactionData);
  console.log(`Transaction created with ID: ${transaction.id} and reference code: ${transaction.transaction_reference_code}`);

  createdTransactions.push(transaction);
}

return createdTransactions;
```

#### Step 8: Add Import for TransactionStorage
Add the import for the TransactionStorage class at the top of the financialManagement.ts file:

```typescript
import { TransactionStorage } from "./storage/financial/transaction.storage";
```

#### Step 9: Update Individual Transaction Creation
For individual transaction creation (if any), update the transaction creation endpoint in `server/routes.ts`:

```typescript
// Generate company-specific transaction reference code
const companyId = result.data.company_id;

// Get company name for transaction reference code prefix
const companyPrefix = await getCompanyName(companyId);
console.log(`Generated company prefix for transaction reference code: ${companyPrefix}`);

// Get the highest existing transaction reference code for this company
const transactionStorage = new TransactionStorage();
const highestSerial = await transactionStorage.getHighestTransactionSerial(companyId, `${companyPrefix}-T-`);
const nextSerial = highestSerial + 1;
const serialString = nextSerial.toString().padStart(3, '0');
const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

console.log(`Generated transaction reference code: ${transactionReferenceCode} for company ${companyId}`);

// Add the reference code to the transaction data
const transactionDataWithReferenceCode = {
  ...result.data,
  transaction_reference_code: transactionReferenceCode
};

console.log(`Creating transaction with reference code "${transactionReferenceCode}"`);
const transaction = await storage.createTransaction(transactionDataWithReferenceCode);
console.log(`Transaction created with ID: ${transaction.id} for company ${transaction.company_id} and reference code: ${transaction.transaction_reference_code}`);
```

### 6. API Endpoint for Updating Reference Codes

#### Step 10: Add API Endpoint for Updating Transaction Reference Codes
Add an API endpoint to update transaction reference codes programmatically:

```typescript
// Update all transaction reference codes to company-specific format
app.post('/api/transactions/update-reference-codes', authMiddleware, requireRole(['saas_admin', 'company_admin']), async (req: AuthRequest, res: Response) => {
  try {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get company ID from request or user context
    const companyId = req.body.company_id || req.user.company_id;

    if (!companyId) {
      return res.status(400).json({ message: 'Company ID is required' });
    }

    // Ensure user has access to this company
    if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
      const userCompanies = await storage.getUserCompanies(req.user.id);
      const hasAccess = userCompanies.some(uc =>
        uc.company_id === companyId ||
        (uc.company && uc.company.id === companyId)
      );

      if (!hasAccess) {
        console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
        return res.status(403).json({ message: 'Access denied to this company' });
      }
    }

    // Get all transactions for the company
    const companyTransactions = await storage.getTransactionsByCompany(companyId);
    console.log(`Found ${companyTransactions.length} transactions for company ${companyId}`);

    if (companyTransactions.length === 0) {
      return res.status(200).json({
        message: 'No transactions found for this company',
        updated: 0
      });
    }

    // Get company prefix
    const companyPrefix = await getCompanyName(companyId);
    const transactionStorage = new TransactionStorage();

    // Update each transaction with a company-specific reference code
    let updatedCount = 0;
    const errors = [];

    for (const transaction of companyTransactions) {
      try {
        // Only update transactions that don't already have a reference code or have an empty reference code
        if (!transaction.transaction_reference_code || transaction.transaction_reference_code.trim() === '') {
          // Get the highest existing transaction reference code for this company
          const highestSerial = await transactionStorage.getHighestTransactionSerial(companyId, `${companyPrefix}-T-`);
          const nextSerial = highestSerial + 1;
          const serialString = nextSerial.toString().padStart(3, '0');
          const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

          await storage.updateTransaction(
            transaction.id,
            companyId,
            { transaction_reference_code: transactionReferenceCode }
          );

          updatedCount++;
        }
      } catch (error) {
        console.error(`Error updating transaction ID ${transaction.id}:`, error);
        errors.push({
          transactionId: transaction.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return res.status(200).json({
      message: `Updated ${updatedCount} transactions with company-specific reference codes`,
      totalTransactions: companyTransactions.length,
      updatedTransactions: updatedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Error updating transaction reference codes:', error);
    return res.status(500).json({
      message: 'Server error',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});
```

### 7. UI Updates

#### Step 11: Update Transaction Display Components
Update the transaction list and detail components to display the transaction reference codes:

**Transaction List Component (`client/src/pages/financial/transactions/index.tsx`):**
```typescript
// Add transaction_reference_code to the Transaction interface
interface Transaction {
  id: number;
  // ... other fields ...
  transaction_reference_code?: string;
}

// Update the table columns to show reference code instead of ID
{
  accessorKey: "transaction_reference_code",
  header: "Reference",
  cell: ({ row }) => {
    const transaction = row.original;
    return (
      <div className="font-medium">
        {transaction.transaction_reference_code || `T-${transaction.id}`}
      </div>
    );
  },
}
```

**Transaction Detail Component (`client/src/pages/financial/transactions/[id].tsx`):**
```typescript
// Update the Transaction interface to include transaction_reference_code
interface Transaction {
  // ... existing fields ...
  transaction_reference_code?: string;
}

// Update the display to show transaction reference code
<div className="grid grid-cols-2 gap-2">
  <div className="text-sm font-medium text-gray-500">Transaction Reference</div>
  <div className="font-medium">
    {transaction.transaction_reference_code || `T-${transaction.id}`}
  </div>
</div>
```

#### Step 12: Update Transaction Creation Forms
Update any transaction creation forms to handle the new reference code field:

```typescript
// The reference code will be auto-generated, so no form input needed
// But ensure the API response includes the generated reference code
// and display it to the user after creation
```

### 8. Testing

#### Step 13: Test the Implementation

**Test Case 1: Create a New Transaction**
1. Create a new transaction (through loan creation, collection payment, etc.)
2. **Expected:**
   - Transaction should automatically get a company-specific reference code (e.g., "GS-T-001")
   - Creating another transaction for the same company should increment the serial (e.g., "GS-T-002")
   - Creating a transaction for a different company should start with 001 (e.g., "CS-T-001")

**Test Case 2: Update Existing Transactions**
1. Use the update-reference-codes endpoint: `POST /api/transactions/update-reference-codes`
2. **Expected:**
   - All transactions without reference codes should get company-specific ones
   - The reference codes should follow the company-specific format and sequential numbering
   - Existing reference codes should remain unchanged

**Test Case 3: Transaction Display**
1. Navigate to Financial → Transactions page
2. Click on any transaction to view details
3. **Expected:**
   - Transaction list shows reference codes instead of internal IDs
   - Transaction details show the proper reference code (e.g., "GS-T-001")
   - Reference codes are consistent with company prefix settings

### 9. Implementation Checklist

- [ ] **Step 1:** Create migration file `migrations/009_add_transaction_reference_code.sql`
- [ ] **Step 2:** Create migration runner script `run-transaction-reference-migration.js`
- [ ] **Step 3:** Update schema definition in `shared/schema.ts`
- [ ] **Step 4:** Update migration runner `run-neon-migrations.js`
- [ ] **Step 5:** Create script `update-transaction-reference-codes.js`
- [ ] **Step 6:** Implement `getHighestTransactionSerial` method in `TransactionStorage`
- [ ] **Step 7:** Update `createJournalEntry` function in `financialManagement.ts`
- [ ] **Step 8:** Add import for `TransactionStorage` in `financialManagement.ts`
- [ ] **Step 9:** Update individual transaction creation endpoint (if exists)
- [ ] **Step 10:** Add API endpoint for updating transaction reference codes
- [ ] **Step 11:** Update transaction display components
- [ ] **Step 12:** Update transaction creation forms (if needed)
- [ ] **Step 13:** Test the implementation

### 10. Benefits

1. **Consistent Reference System**: Transactions now follow the same reference code pattern as other entities
2. **Company-Specific Identification**: Each company has its own transaction numbering sequence
3. **Better Traceability**: Reference codes are more recognizable and easier to track than internal IDs
4. **Professional Appearance**: Displays proper business reference codes in transaction lists and details
5. **Audit Trail**: Clear transaction identification for accounting and compliance purposes

### 11. Reference Code Format Examples

- **Company "GOVINDARAJI S"**: GS-T-001, GS-T-002, GS-T-003, ...
- **Company "Cloud Stier"**: CS-T-001, CS-T-002, CS-T-003, ...
- **Company "ABC Corp"**: AC-T-001, AC-T-002, AC-T-003, ...

The "T" in the format clearly identifies these as transaction reference codes, distinguishing them from:
- Loans: GS-L-001, GS-L-002, ...
- Collections: GS-001, GS-002, ...
- Customers: GS-C-001, GS-C-002, ...
- Partners: GS-P-001, GS-P-002, ...
- Agents: GS-A-001, GS-A-002, ...

### 12. Status

📋 **Planned** - Ready for implementation following the established pattern from partners, loans, and customers reference code systems.