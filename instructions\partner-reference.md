# Partner Reference Code Implementation

This document provides step-by-step instructions for implementing company-specific partner reference codes in the FinancialTracker system.

## Overview

Partner reference codes are unique identifiers for partners within a company, following a similar pattern to collection IDs, agent reference codes, and loan reference codes. The format is:

```
[COMPANY_PREFIX]-[SEQUENTIAL_NUMBER]
```

For example:
- "GS-001" for the first partner in "GOVINDARAJI S" company
- "CS-001" for the first partner in "Cloud Stier" company

The implementation consists of:
1. Adding a new column to the database
2. Creating a function to generate company prefixes
3. Creating a function to determine the next sequential number
4. Updating the partner creation process to generate and store reference codes
5. Updating the UI to display the reference codes

## Implementation Details

### 1. Database Schema Changes

#### SQL Migration
Create a migration file (e.g., `migrations/008_add_partner_reference_code.sql`) with the following SQL:

```sql
-- Add partner_reference_code column to partners table
ALTER TABLE "partners" 
  ADD COLUMN IF NOT EXISTS "partner_reference_code" TEXT;

-- Create an index on partner_reference_code for faster lookups
CREATE INDEX IF NOT EXISTS idx_partners_reference_code ON partners(partner_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN partners.partner_reference_code IS 'Company-specific partner identifier string (e.g., GS-001) that is unique within each company';
```

#### Schema Definition Update
Update the partners table definition in `shared/schema.ts`:

```typescript
// Partners
export const partners = pgTable('partners', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  name: text('name').notNull(),
  type: partnerTypeEnum('type').notNull(),
  email: text('email'),
  phone: text('phone'),
  address: text('address'),
  website: text('website'),
  contact_person: text('contact_person'),
  investment_amount: numeric('investment_amount', { precision: 10, scale: 2 }),
  partnership_start_date: timestamp('partnership_start_date'),
  partnership_end_date: timestamp('partnership_end_date'),
  agreement_details: text('agreement_details'),
  notes: text('notes'),
  partner_reference_code: text('partner_reference_code'), // Add this line
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

Also update the insert schema to include the new field:

```typescript
export const insertPartnerSchema = createInsertSchema(partners)
  .omit({ id: true, created_at: true, updated_at: true })
  .extend({
    partner_reference_code: z.string().optional()
  });
```

#### Apply Migration
Run the migration using the provided script:

```bash
node run-partner-reference-migration.js
```

Or use the combined migration runner:

```bash
node run-neon-migrations.js
```

### 2. Company Prefix Generation

The company prefix is generated from the company name using the following rules:

- For a single word: First letter + Last letter (e.g., "Cloudstier" → "CR")
- For multiple words: First letter of first word + First letter of last word (e.g., "GOVINDARAJI S" → "GS")
- All letters are converted to uppercase for consistency

This functionality is already implemented in the `getCompanyName` function in `server/routes.ts` and can be reused for partner reference codes.

### 3. Sequential Number Generation

The sequential number is determined by finding the highest existing partner reference code for the company and incrementing it.

#### Implementation Steps:

1. Add the `getHighestPartnerSerial` method to the `PartnerStorage` class in `server/storage/partner.storage.ts`:

```typescript
async getHighestPartnerSerial(companyId: number, companyPrefix: string): Promise<number> {
  try {
    // Get all partners for this company
    const query = `
      SELECT * FROM partners
      WHERE company_id = $1
    `;
    
    const result = await pool.query(query, [companyId]);
    
    if (!result.rows || result.rows.length === 0) {
      return 0; // No partners found, start with 1
    }

    // Extract the numeric part from partner_reference_code strings and find the highest
    let highestNumber = 0;
    for (const partner of result.rows) {
      if (partner.partner_reference_code && partner.partner_reference_code.startsWith(companyPrefix)) {
        const parts = partner.partner_reference_code.split('-');
        if (parts.length === 2) {
          const numericPart = parseInt(parts[1], 10);
          if (!isNaN(numericPart) && numericPart > highestNumber) {
            highestNumber = numericPart;
          }
        }
      }
    }

    return highestNumber;
  } catch (error) {
    console.error('Error in getHighestPartnerSerial', error);
    return 0;
  }
}
```

## Complete Implementation Process

Follow these steps to implement the partner reference code feature:

### 1. Database Migration

1. Create a migration file `migrations/008_add_partner_reference_code.sql`:
   ```sql
   -- Add partner_reference_code column to partners table
   ALTER TABLE "partners" 
     ADD COLUMN IF NOT EXISTS "partner_reference_code" TEXT;

   -- Create an index on partner_reference_code for faster lookups
   CREATE INDEX IF NOT EXISTS idx_partners_reference_code ON partners(partner_reference_code);
   ```

2. Update the partners table schema in `shared/schema.ts` to include the new column:
   ```typescript
   partner_reference_code: text('partner_reference_code'),
   ```

3. Apply the migration using the provided script:
   ```bash
   node run-partner-reference-migration.js
   ```
