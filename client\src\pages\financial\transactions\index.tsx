import { useState, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { apiRequest, queryClient } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';
import { useToast } from '@/hooks/use-toast';
import { useAuth } from '@/lib/auth';
import { DEFAULT_COMPANY_ID, isValidCompanyId } from '@/config/companyConfig';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Spinner } from '@/components/ui/spinner';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Pagination,
  PaginationContent,
  PaginationEllipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

// Icons
import {
  Plus, Search, MoreHorizontal, Edit, Eye, RefreshCw,
  ArrowUpDown, Calendar as CalendarIcon, Filter
} from 'lucide-react';
import { format } from 'date-fns';

// Interface for Transaction type
interface Transaction {
  id: number;
  account_id: number;
  company_id: number;
  transaction_date: string;
  transaction_type: 'debit' | 'credit';
  amount: number;
  description: string;
  reference_type?: string;
  reference_id?: number;
  created_at: string;
  updated_at: string;
  // Additional fields from joins
  account?: {
    id: number;
    account_code: string;
    account_name: string;
    account_type: string;
  };
}

// Interface for paginated response
interface PaginatedResponse {
  transactions: Transaction[];
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
  };
}

// Helper function to format currency
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(amount);
};

// Helper function to format date
const formatDate = (dateString: string) => {
  return format(new Date(dateString), 'dd MMM yyyy');
};

// Helper function to get badge classes for transaction types
const getTransactionTypeBadge = (type: string) => {
  return type === 'credit' ? 'bg-green-100 text-green-800 hover:bg-green-100' : 'bg-red-100 text-red-800 hover:bg-red-100';
};

const TransactionsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const [selectedAccountType, setSelectedAccountType] = useState<string>("all_account_types");
  const [selectedTransactionType, setSelectedTransactionType] = useState<string>("all_transaction_types");
  const [selectedReferenceType, setSelectedReferenceType] = useState<string>("all_reference_types");
  const [isFixingTransactions, setIsFixingTransactions] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [, navigate] = useLocation();
  const { companyId } = useContextData();
  const { toast } = useToast();
  const { user } = useAuth();
  const isAdmin = user?.role === 'saas_admin' || user?.role === 'admin';

  // Log component mount with a unique ID to track duplicate instances
  useEffect(() => {
    const componentId = Math.random().toString(36).substring(2, 9);

    // Log detailed information about this component instance
    console.log(`[TransactionsPage ${componentId}] Component mounted with companyId:`, companyId);

    // Log warning if no company ID is available
    if (!companyId) {
      console.warn(`[TransactionsPage ${componentId}] WARNING: No company ID available. User needs to select a company.`, {
        userCompanyId: user?.company_id,
        // Get stack trace to identify where this component is being rendered
        stack: new Error().stack?.split('\n').slice(1, 5).join('\n')
      });
    }

    return () => {
      console.log(`[TransactionsPage ${componentId}] Component unmounted with companyId:`, companyId);
    };
  }, [companyId, user?.company_id]);

  // Fetch transactions with server-side pagination and filtering
  const {
    data,
    isLoading,
    isError,
    refetch
  } = useQuery<PaginatedResponse>({
    queryKey: [
      '/api/companies',
      companyId,
      'transactions',
      currentPage,
      itemsPerPage,
      dateRange,
      selectedAccountType,
      selectedTransactionType,
      selectedReferenceType,
      searchTerm
    ],
    queryFn: async () => {
      if (!companyId) {
        console.error('No company ID available for transactions API call');
        return { transactions: [], pagination: { page: 1, limit: 10, totalCount: 0, totalPages: 0 } };
      }

      // Log the API call with a timestamp to identify duplicate calls
      const callId = Math.random().toString(36).substring(2, 9);
      const timestamp = new Date().toISOString();
      console.log(`[API Call ${callId}] Making transactions API call at ${timestamp} with companyId:`, companyId);

      let url = `/api/companies/${companyId}/transactions`;
      const params = new URLSearchParams();

      // Add pagination parameters
      params.append('page', currentPage.toString());
      params.append('limit', itemsPerPage.toString());

      // Add date filters
      if (dateRange.from) {
        params.append('startDate', format(dateRange.from, 'yyyy-MM-dd'));
      }
      if (dateRange.to) {
        params.append('endDate', format(dateRange.to, 'yyyy-MM-dd'));
      }

      // Add other filters
      if (selectedAccountType !== "all_account_types") {
        params.append('accountType', selectedAccountType);
      }

      if (selectedTransactionType !== "all_transaction_types") {
        params.append('transactionType', selectedTransactionType);
      }

      if (selectedReferenceType !== "all_reference_types") {
        params.append('referenceType', selectedReferenceType);
      }

      // Add search term
      if (searchTerm) {
        params.append('search', searchTerm);
      }

      // Append query parameters to URL
      const queryParams = params.toString();
      if (queryParams) {
        url += `?${queryParams}`;
      }

      const response = await apiRequest('GET', url);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Extract transactions and pagination info
  const transactions = data?.transactions || [];
  const pagination = data?.pagination || { page: 1, limit: 10, totalCount: 0, totalPages: 0 };

  // Function to clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setDateRange({ from: undefined, to: undefined });
    setSelectedAccountType("all_account_types");
    setSelectedTransactionType("all_transaction_types");
    setSelectedReferenceType("all_reference_types");
    setCurrentPage(1);
  };

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader className="flex flex-col space-y-1.5 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-4">
          <div>
            <CardTitle className="text-2xl font-bold">Transactions</CardTitle>
            <CardDescription>
              View and manage all financial transactions
            </CardDescription>
          </div>
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={() => navigate('/financial/transactions/create')}
              className="w-full mt-3 sm:mt-0"
            >
              <Plus className="mr-2 h-4 w-4" />
              New Transaction
            </Button>
            {isAdmin && (
              <Button
                onClick={async () => {
                  try {
                    setIsFixingTransactions(true);
                    const res = await apiRequest('POST', `/api/companies/${companyId}/fix-collection-transactions`);

                    if (!res.ok) {
                      throw new Error(`Failed to fix transactions: ${res.status}`);
                    }

                    const data = await res.json();

                    toast({
                      title: "Collection transactions fixed",
                      description: data.message,
                      variant: "default"
                    });

                    // Reload transactions
                    queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'transactions'] });
                  } catch (error) {
                    console.error('Error fixing transactions:', error);
                    toast({
                      title: "Error",
                      description: error instanceof Error ? error.message : "Failed to fix transactions",
                      variant: "destructive"
                    });
                  } finally {
                    setIsFixingTransactions(false);
                  }
                }}
                variant="outline"
                className="w-full mt-3 sm:mt-0"
                disabled={isFixingTransactions}
              >
                {isFixingTransactions ? (
                  <>
                    <Spinner className="mr-2 h-4 w-4" />
                    Fixing...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Fix Collection Transactions
                  </>
                )}
              </Button>
            )}
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between mb-6 space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="relative w-full sm:w-1/3">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search transactions..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-wrap gap-2 items-center justify-end">
              {/* Date Range Picker */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button variant="outline" className="w-full sm:w-auto">
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "MMM d, yyyy")} -{" "}
                          {format(dateRange.to, "MMM d, yyyy")}
                        </>
                      ) : (
                        format(dateRange.from, "MMM d, yyyy")
                      )
                    ) : (
                      "Date Range"
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="end">
                  <Calendar
                    initialFocus
                    mode="range"
                    selected={dateRange}
                    onSelect={(range) => setDateRange(range || { from: undefined, to: undefined })}
                    numberOfMonths={2}
                  />
                  <div className="flex items-center justify-end p-2 border-t">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setDateRange({ from: undefined, to: undefined })}
                    >
                      Clear
                    </Button>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Account Type Filter */}
              <Select
                value={selectedAccountType}
                onValueChange={setSelectedAccountType}
              >
                <SelectTrigger className="w-full sm:w-auto">
                  <SelectValue placeholder="Account Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_account_types">All Account Types</SelectItem>
                  <SelectItem value="asset">Asset</SelectItem>
                  <SelectItem value="liability">Liability</SelectItem>
                  <SelectItem value="equity">Equity</SelectItem>
                  <SelectItem value="income">Income</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                </SelectContent>
              </Select>

              {/* Transaction Type Filter */}
              <Select
                value={selectedTransactionType}
                onValueChange={setSelectedTransactionType}
              >
                <SelectTrigger className="w-full sm:w-auto">
                  <SelectValue placeholder="Transaction Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_transaction_types">All Types</SelectItem>
                  <SelectItem value="debit">Debit</SelectItem>
                  <SelectItem value="credit">Credit</SelectItem>
                </SelectContent>
              </Select>

              {/* Reference Type Filter */}
              <Select
                value={selectedReferenceType}
                onValueChange={setSelectedReferenceType}
              >
                <SelectTrigger className="w-full sm:w-auto">
                  <SelectValue placeholder="Reference Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all_reference_types">All References</SelectItem>
                  <SelectItem value="loan">Loan</SelectItem>
                  <SelectItem value="collection">Collection</SelectItem>
                  <SelectItem value="expense">Expense</SelectItem>
                  <SelectItem value="investment">Investment</SelectItem>
                  <SelectItem value="adjustment">Adjustment</SelectItem>
                </SelectContent>
              </Select>

              {/* Clear Filters Button */}
              <Button
                variant="ghost"
                onClick={clearFilters}
                className="w-full sm:w-auto"
              >
                <Filter className="mr-2 h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>

          {isLoading ? (
            <div className="flex justify-center py-12">
              <Spinner className="h-8 w-8" />
            </div>
          ) : isError ? (
            <div className="text-center py-12">
              <p className="text-red-500 mb-4">Error loading transactions. Please try again.</p>
              <Button onClick={() => refetch()}>Retry</Button>
            </div>
          ) : transactions.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No transactions found matching your search criteria.</p>
              <Button onClick={clearFilters} variant="outline" className="mb-4 mr-2">
                <RefreshCw className="mr-2 h-4 w-4" />
                Clear Filters
              </Button>
              <Button onClick={() => navigate('/financial/transactions/create')}>
                <Plus className="mr-2 h-4 w-4" />
                Create New Transaction
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[120px]">Date</TableHead>
                    <TableHead className="w-[150px]">
                      <div className="flex items-center">
                        Account
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="w-[100px]">
                      <div className="flex items-center">
                        Type
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead className="w-[120px] text-right">Amount</TableHead>
                    <TableHead className="w-[100px]">Reference</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {transactions.map((transaction: Transaction) => (
                    <TableRow key={transaction.id}>
                      <TableCell className="font-medium">
                        {formatDate(transaction.transaction_date)}
                      </TableCell>
                      <TableCell>
                        {transaction.account ? (
                          <div>
                            <div className="font-medium">{transaction.account.account_name}</div>
                            <div className="text-xs text-gray-500">{transaction.account.account_code}</div>
                          </div>
                        ) : (
                          "Unknown Account"
                        )}
                      </TableCell>
                      <TableCell>{transaction.description}</TableCell>
                      <TableCell>
                        <Badge className={getTransactionTypeBadge(transaction.transaction_type)}>
                          {transaction.transaction_type.charAt(0).toUpperCase() + transaction.transaction_type.slice(1)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        {formatCurrency(transaction.amount)}
                      </TableCell>
                      <TableCell>
                        {transaction.reference_type ? (
                          <span className="text-xs">
                            {transaction.reference_type.replace('_', ' ')}
                          </span>
                        ) : (
                          <span className="text-xs text-gray-500">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => navigate(`/financial/transactions/${transaction.id}`)}>
                              <Eye className="mr-2 h-4 w-4" />
                              View
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => navigate(`/financial/transactions/${transaction.id}/edit`)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination Controls */}
              {pagination.totalPages > 1 && (
                <div className="mt-4 flex justify-center">
                  <Pagination>
                    <PaginationContent>
                      <PaginationItem>
                        <PaginationPrevious
                          onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                          disabled={currentPage === 1}
                          className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>

                      {/* First page */}
                      {currentPage > 2 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(1)}>1</PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis if needed */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Previous page if not the first */}
                      {currentPage > 1 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(currentPage - 1)}>
                            {currentPage - 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Current page */}
                      <PaginationItem>
                        <PaginationLink isActive>{currentPage}</PaginationLink>
                      </PaginationItem>

                      {/* Next page if not the last */}
                      {currentPage < pagination.totalPages && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(currentPage + 1)}>
                            {currentPage + 1}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      {/* Ellipsis if needed */}
                      {currentPage < pagination.totalPages - 2 && (
                        <PaginationItem>
                          <PaginationEllipsis />
                        </PaginationItem>
                      )}

                      {/* Last page */}
                      {currentPage < pagination.totalPages - 1 && (
                        <PaginationItem>
                          <PaginationLink onClick={() => setCurrentPage(pagination.totalPages)}>
                            {pagination.totalPages}
                          </PaginationLink>
                        </PaginationItem>
                      )}

                      <PaginationItem>
                        <PaginationNext
                          onClick={() => setCurrentPage(prev => Math.min(prev + 1, pagination.totalPages))}
                          disabled={currentPage === pagination.totalPages}
                          className={currentPage === pagination.totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
                        />
                      </PaginationItem>
                    </PaginationContent>
                  </Pagination>
                </div>
              )}

              <div className="mt-4 flex justify-between items-center">
                <div className="flex items-center gap-2">
                  <span className="text-sm text-gray-500">Items per page:</span>
                  <Select value={itemsPerPage.toString()} onValueChange={(value) => {
                    setItemsPerPage(parseInt(value));
                    setCurrentPage(1); // Reset to first page when changing items per page
                  }}>
                    <SelectTrigger className="w-16 h-8">
                      <SelectValue placeholder="10" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="10">10</SelectItem>
                      <SelectItem value="20">20</SelectItem>
                      <SelectItem value="50">50</SelectItem>
                      <SelectItem value="100">100</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="text-sm text-gray-500">
                  Showing {transactions.length} of {pagination.totalCount} transactions
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default TransactionsPage;