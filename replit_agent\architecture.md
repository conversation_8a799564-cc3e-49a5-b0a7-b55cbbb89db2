# TrackFina Architecture Overview

## 1. Overview

TrackFina is a SaaS loan management platform designed for financial institutions. It provides a comprehensive solution for creating, managing, and processing loans through an intuitive interface that works across various device types. The system employs a multi-tenant architecture to support multiple companies and branches with role-based access control.

The application is built as a full-stack JavaScript/TypeScript application with a clear separation between client and server components. It uses a modern React frontend and Node.js backend, with a PostgreSQL database for data persistence.

## 2. System Architecture

TrackFina follows a modern web application architecture with the following high-level components:

### 2.1 Frontend Architecture

- **Technology Stack**: React with TypeScript
- **Build System**: Vite for fast development and optimized production builds
- **State Management**: React Query for server state and context-based local state management
- **Styling**: Tailwind CSS with shadcn/ui component library
- **Routing**: Wouter (lightweight alternative to React Router)

The frontend is organized as a Single Page Application (SPA) with component-based architecture, following modern React patterns including hooks and context API for state management.

### 2.2 Backend Architecture

- **Technology Stack**: Node.js with Express and TypeScript
- **API Style**: RESTful API endpoints
- **Authentication**: JWT-based authentication with cookie storage
- **Database Access**: Drizzle ORM for type-safe database operations
- **Error Handling**: Centralized error logging and handling

The backend follows a layered architecture with clear separation between routes, business logic, and data access. It implements middleware patterns for cross-cutting concerns like authentication and error handling.

### 2.3 Database Architecture

- **Database System**: PostgreSQL (via NeonDB serverless)
- **ORM**: Drizzle ORM with TypeScript schema definitions
- **Migration Strategy**: Schema-driven migrations with drizzle-kit

The database schema is designed to support multi-tenancy with company isolation. It uses foreign keys and relations to maintain data integrity across related entities.

## 3. Key Components

### 3.1 Multi-tenancy System

TrackFina implements multi-tenancy at the data level, where multiple companies share the same application instance but have isolated data. Key aspects include:

- Company-based data isolation
- User-to-company associations allowing users to access multiple companies
- Branch management within companies
- Role-based access control across the tenant hierarchy

### 3.2 Authentication & Authorization

- **Authentication**: JWT tokens with HTTP-only cookies for secure storage
- **Authorization**: Role-based access control with the following roles:
  - `saas_admin`: System-level administrator
  - `reseller`: Partners who can onboard new companies
  - `company_admin`: Company-level administrator
  - `employee`: Regular company staff
  - `agent`: Field agents for loan collection
  - `customer`: Loan borrowers
  - `partner`: Business partners

### 3.3 Dynamic Form System

The system features a flexible form-building capability for loan templates:

- Custom form templates with configurable fields
- Field types including text, number, date, select, etc.
- Validation rules for form submissions
- Conditional logic for field visibility

### 3.4 Loan Management System

Core loan functionality includes:

- Loan creation with customizable templates
- Interest calculation (flat, reducing, compound)
- Payment scheduling and amortization
- Loan status tracking (active, overdue, completed)
- Collection management

### 3.5 Reporting and Analytics

- Dashboard with key metrics
- Collection analytics
- Agent performance tracking
- Customizable reports

## 4. Data Flow

### 4.1 Authentication Flow

1. User submits credentials (email/password)
2. Server validates credentials and generates JWT token
3. Token is stored in HTTP-only cookie and returned to client
4. Subsequent requests include the cookie automatically
5. Server middleware validates token and attaches user information to the request

### 4.2 Loan Creation Flow

1. User selects a loan template
2. Dynamic form is rendered based on template configuration
3. User fills in loan details (customer, amount, interest, etc.)
4. Form data is validated client-side and then submitted to server
5. Server performs additional validation and stores loan in database
6. Payment schedule is generated based on loan terms
7. Confirmation is returned to the client with loan details

### 4.3 Collection Flow

1. Agent views pending collections
2. Records payments against outstanding loan installments
3. System updates loan balances and status
4. Receipts can be generated for payments
5. Collection statistics are updated in real-time

## 5. External Dependencies

### 5.1 UI Components

- **Radix UI**: Low-level, accessible component primitives
- **shadcn/ui**: High-level component library built on Radix UI
- **Lucide Icons**: SVG icon library
- **react-helmet**: Document head management
- **react-hook-form**: Form state management and validation

### 5.2 State Management

- **@tanstack/react-query**: Server state management and caching
- **zustand**: Lightweight state management (used for specific features)

### 5.3 Database

- **@neondatabase/serverless**: PostgreSQL database provider
- **drizzle-orm**: TypeScript ORM for database access
- **drizzle-zod**: Schema validation for database operations

### 5.4 Authentication

- **jsonwebtoken**: JWT token generation and validation
- **bcrypt**: Password hashing
- **cookie-parser**: Cookie parsing middleware

## 6. Deployment Strategy

### 6.1 Infrastructure

The application is configured for deployment on Google Cloud (GCE) with the following components:

- **Frontend**: Served as static assets from the Node.js server
- **Backend**: Node.js application running on GCE
- **Database**: NeonDB (serverless PostgreSQL)

### 6.2 Build Process

1. Frontend is built using Vite (`npm run build`)
2. Backend is bundled using esbuild
3. Combined assets are packaged for deployment

### 6.3 Environment Configuration

- Development environment uses hot module replacement for fast iteration
- Production environment uses optimized builds with environment variables
- Database connections are configured via environment variables

### 6.4 Scalability Considerations

- The application uses a connection pool for database access
- Stateless design allows for horizontal scaling of the backend
- Frontend is delivered as static assets that can be CDN-cached

## 7. Security Considerations

### 7.1 Authentication Security

- Passwords are hashed using bcrypt
- JWTs are stored in HTTP-only cookies to prevent JavaScript access
- Token expiration is enforced

### 7.2 Data Protection

- Input validation on both client and server
- SQL injection protection via parameterized queries
- CSRF protection through tokens

### 7.3 Role-Based Access

- Endpoints are protected by role-based middleware
- UI components conditionally render based on user permissions
- Data queries filter results based on user's company access