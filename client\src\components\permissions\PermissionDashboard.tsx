import React, { useState, useEffect } from 'react';
import { PermissionMatrix } from './PermissionMatrix';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, Shield, Users, BarChart3, <PERSON>ertTriangle, CheckCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface PermissionDashboardProps {
  companyId?: number;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  permissions?: number[];
  parent_roles?: number[];
  child_roles?: number[];
  hierarchy_depth?: number;
  effective_permissions?: number[];
  inherited_permissions?: number[];
  temporary_permissions?: TemporaryPermission[];
}

interface TemporaryPermission {
  id: number;
  permission_id: number;
  granted_by: number;
  granted_at: string;
  expires_at: string;
  priority: 'low' | 'medium' | 'high' | 'emergency';
  reason?: string;
  is_active: boolean;
}

interface RoleHierarchy {
  role_id: number;
  parent_role_id: number;
  inheritance_type: 'inherit' | 'override' | 'deny';
}

interface PermissionCategory {
  category: string;
  metadata: {
    name: string;
    description: string;
    icon: string;
  };
  permissions: Array<{
    id: number;
    code: string;
    name: string;
    description: string;
    category: string;
  }>;
}

interface PermissionAnalytics {
  permission_distribution: Array<{
    category: string;
    code: string;
    name: string;
    role_count: number;
    user_count: number;
  }>;
  role_statistics: Array<{
    role_name: string;
    description: string;
    permission_count: number;
    user_count: number;
  }>;
  category_statistics: Array<{
    category: string;
    total_permissions: number;
    assigned_permissions: number;
    users_with_category_permissions: number;
  }>;
}

export function PermissionDashboard({ companyId }: PermissionDashboardProps) {
  const [roles, setRoles] = useState<Role[]>([]);
  const [permissionCategories, setPermissionCategories] = useState<PermissionCategory[]>([]);
  const [roleHierarchies, setRoleHierarchies] = useState<RoleHierarchy[]>([]);
  const [analytics, setAnalytics] = useState<PermissionAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [activeTab, setActiveTab] = useState('matrix');

  useEffect(() => {
    loadData();
  }, [companyId]);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadRoles(),
        loadPermissionCategories(),
        loadRoleHierarchies(),
        loadAnalytics()
      ]);
    } catch (error) {
      console.error('Error loading permission data:', error);
      toast({
        title: "Error",
        description: "Failed to load permission data. Please refresh the page.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const loadRoles = async () => {
    const response = await fetch('/api/roles');
    if (!response.ok) throw new Error('Failed to load roles');
    const data = await response.json();

    // Load permissions and hierarchy data for each role
    const rolesWithData = await Promise.all(
      data.map(async (role: Role) => {
        try {
          // Load direct permissions
          const permResponse = await fetch(`/api/roles/${role.id}/permissions`);
          let permissions: number[] = [];
          if (permResponse.ok) {
            const perms = await permResponse.json();
            permissions = perms.map((p: any) => p.id);
          }

          // Load effective permissions (including inherited)
          let effectivePermissions: number[] = [];
          let inheritedPermissions: number[] = [];
          try {
            const effectiveResponse = await fetch(`/api/roles/${role.id}/effective-permissions`);
            if (effectiveResponse.ok) {
              const effectiveData = await effectiveResponse.json();
              effectivePermissions = effectiveData.effective_permissions || [];
              inheritedPermissions = effectiveData.inherited_permissions || [];
            }
          } catch (error) {
            console.warn(`Failed to load effective permissions for role ${role.id}`);
          }

          // Load temporary permissions
          let temporaryPermissions: TemporaryPermission[] = [];
          try {
            const tempResponse = await fetch(`/api/roles/${role.id}/temporary-permissions`);
            if (tempResponse.ok) {
              temporaryPermissions = await tempResponse.json();
            }
          } catch (error) {
            console.warn(`Failed to load temporary permissions for role ${role.id}`);
          }

          return {
            ...role,
            permissions,
            effective_permissions: effectivePermissions,
            inherited_permissions: inheritedPermissions,
            temporary_permissions: temporaryPermissions
          };
        } catch (error) {
          console.warn(`Failed to load data for role ${role.id}`);
          return { ...role, permissions: [] };
        }
      })
    );

    setRoles(rolesWithData);
  };

  const loadRoleHierarchies = async () => {
    try {
      const response = await fetch('/api/role-hierarchy');
      if (response.ok) {
        const data = await response.json();
        setRoleHierarchies(data);

        // Update roles with hierarchy depth information
        const hierarchyTree = await fetch('/api/role-hierarchy/tree');
        if (hierarchyTree.ok) {
          const treeData = await hierarchyTree.json();

          // Calculate hierarchy depths and update roles
          const roleDepths = new Map<number, number>();
          const calculateDepth = (nodes: any[], depth = 0) => {
            nodes.forEach(node => {
              roleDepths.set(node.role.id, depth);
              if (node.children && node.children.length > 0) {
                calculateDepth(node.children, depth + 1);
              }
            });
          };
          calculateDepth(treeData);

          setRoles(prevRoles =>
            prevRoles.map(role => ({
              ...role,
              hierarchy_depth: roleDepths.get(role.id) || 0
            }))
          );
        }
      }
    } catch (error) {
      console.warn('Failed to load role hierarchies:', error);
    }
  };

  const loadPermissionCategories = async () => {
    const response = await fetch('/api/dashboard/permissions/categories');
    if (!response.ok) throw new Error('Failed to load permission categories');
    const data = await response.json();
    setPermissionCategories(data);
  };

  const loadAnalytics = async () => {
    try {
      const response = await fetch('/api/dashboard/permissions/analytics');
      if (response.ok) {
        const data = await response.json();
        setAnalytics(data);
      }
    } catch (error) {
      console.warn('Failed to load analytics:', error);
    }
  };

  const handlePermissionChange = async (roleId: number, permissionId: number, granted: boolean) => {
    setUpdating(true);
    try {
      const response = await fetch('/api/dashboard/permissions/bulk-assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          assignments: [{
            role_id: roleId,
            permission_ids: [permissionId],
            action: granted ? 'grant' : 'revoke'
          }]
        })
      });

      if (!response.ok) throw new Error('Failed to update permission');

      // Update local state
      setRoles(prevRoles =>
        prevRoles.map(role => {
          if (role.id === roleId) {
            const permissions = role.permissions || [];
            return {
              ...role,
              permissions: granted
                ? [...permissions, permissionId]
                : permissions.filter(id => id !== permissionId)
            };
          }
          return role;
        })
      );

      // Reload analytics
      loadAnalytics();
    } finally {
      setUpdating(false);
    }
  };

  const handleBulkAssign = async (assignments: Array<{role_id: number, permission_ids: number[], action: 'grant' | 'revoke'}>) => {
    setUpdating(true);
    try {
      const response = await fetch('/api/dashboard/permissions/bulk-assign', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ assignments })
      });

      if (!response.ok) throw new Error('Failed to perform bulk assignment');

      // Reload data to ensure consistency
      await loadRoles();
      await loadAnalytics();
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader2 className="h-8 w-8 animate-spin" />
        <span className="ml-2">Loading permission data...</span>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Permission Management</h1>
          <p className="text-muted-foreground">
            Manage roles, permissions, and access controls for your organization
          </p>
        </div>
        <Button onClick={loadData} variant="outline" disabled={loading || updating}>
          {loading || updating ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : null}
          Refresh
        </Button>
      </div>

      {/* Quick Stats */}
      {analytics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{roles.length}</div>
              <p className="text-xs text-muted-foreground">
                {roles.filter(r => r.is_system).length} system roles
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Permissions</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {permissionCategories.reduce((acc, cat) => acc + cat.permissions.length, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                {permissionCategories.length} categories
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
              <CheckCircle className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {analytics.role_statistics.reduce((acc, role) => acc + role.permission_count, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Role-permission assignments
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Coverage</CardTitle>
              <BarChart3 className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {Math.round(
                  (analytics.category_statistics.reduce((acc, cat) => acc + cat.assigned_permissions, 0) /
                   analytics.category_statistics.reduce((acc, cat) => acc + cat.total_permissions, 0)) * 100
                )}%
              </div>
              <p className="text-xs text-muted-foreground">
                Permissions assigned
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="matrix">Permission Matrix</TabsTrigger>
          <TabsTrigger value="analytics">Analytics</TabsTrigger>
          <TabsTrigger value="roles">Role Overview</TabsTrigger>
        </TabsList>

        <TabsContent value="matrix" className="space-y-4">
          {updating && (
            <Alert>
              <Loader2 className="h-4 w-4 animate-spin" />
              <AlertDescription>
                Updating permissions... Please wait.
              </AlertDescription>
            </Alert>
          )}

          <PermissionMatrix
            roles={roles}
            permissionCategories={permissionCategories}
            roleHierarchies={roleHierarchies}
            onPermissionChange={handlePermissionChange}
            onBulkAssign={handleBulkAssign}
            loading={updating}
            showHierarchy={true}
            showTemporaryPermissions={true}
            showEffectivePermissions={true}
          />
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          {analytics && (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
              {/* Category Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Permission Categories</CardTitle>
                  <CardDescription>
                    Permission distribution across categories
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.category_statistics.map((category) => (
                      <div key={category.category} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium capitalize">
                            {category.category.replace('_', ' ')}
                          </div>
                          <div className="text-sm text-muted-foreground">
                            {category.assigned_permissions}/{category.total_permissions} assigned
                          </div>
                        </div>
                        <Badge variant="outline">
                          {Math.round((category.assigned_permissions / category.total_permissions) * 100)}%
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              {/* Role Statistics */}
              <Card>
                <CardHeader>
                  <CardTitle>Role Statistics</CardTitle>
                  <CardDescription>
                    Permission assignments by role
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {analytics.role_statistics.map((role) => (
                      <div key={role.role_name} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{role.role_name}</div>
                          <div className="text-sm text-muted-foreground">
                            {role.user_count} users
                          </div>
                        </div>
                        <Badge variant="secondary">
                          {role.permission_count} permissions
                        </Badge>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </TabsContent>

        <TabsContent value="roles" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {roles.map((role) => (
              <Card key={role.id}>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{role.name}</CardTitle>
                    {role.is_system && (
                      <Badge variant="outline">System</Badge>
                    )}
                  </div>
                  <CardDescription>{role.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-muted-foreground">Permissions</span>
                    <Badge variant="secondary">
                      {role.permissions?.length || 0}
                    </Badge>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
