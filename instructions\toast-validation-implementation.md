# Toast Message Implementation for Form Validation Errors

## Overview

This document outlines the implementation of toast notifications for form validation errors in the FinancialTracker application, specifically focusing on the company registration form validation improvements.

## Problem Statement

The company registration form had the following issues:
- Company name was marked as mandatory (*) but validation wasn't properly enforced
- Company email was not marked as required and lacked proper validation
- Users could proceed with registration without filling required fields
- No clear error feedback when validation failed
- **Critical Issue**: When users clicked directly on "Company Details" tab and then "Complete Registration", personal details validation errors were not shown in toast notifications
- Users could bypass personal details validation by jumping directly to company details

## Solution Implementation

### 1. Enhanced Schema Validation

**File:** `client/src/pages/register.tsx`

**Changes Made:**

```typescript
// Extended company schema with explicit validation
const extendedCompanySchema = insertCompanySchema.extend({
  // Company name validation - required field
  name: z.string()
    .min(1, { message: "Company name is required" })
    .min(2, { message: "Company name must be at least 2 characters" }),

  // Email validation - required field
  email: z.string()
    .min(1, { message: "Company email is required" })
    .email({ message: "Please enter a valid email address" }),

  // Phone number validation
  phone: z.string().refine(
    (value) => {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      return pattern.test(value);
    },
    { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
  )
});
```

### 2. UI Improvements

**Required Field Indicators:**

```typescript
// Company Name Field
<FormLabel className="text-sm font-medium">
  Company Name
  <span className="text-destructive ml-1">*</span>
</FormLabel>

// Company Email Field
<FormLabel className="text-sm font-medium">
  Company Email (for business correspondence only)
  <span className="text-destructive ml-1">*</span>
</FormLabel>
```

### 3. Real-time Validation

**Enhanced Email Validation Logic:**

```typescript
onChange={(e) => {
  field.onChange(e);

  // Validate email on change
  const emailValue = e.target.value.trim();
  if (!emailValue) {
    companyForm.setError("email", {
      type: "manual",
      message: "Company email is required"
    });
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
    companyForm.setError("email", {
      type: "manual",
      message: "Please enter a valid email address"
    });
  } else {
    companyForm.clearErrors("email");
  }
}}

onBlur={(e) => {
  field.onBlur();

  // Validate email on blur
  const emailValue = e.target.value.trim();
  if (!emailValue) {
    companyForm.setError("email", {
      type: "manual",
      message: "Company email is required"
    });
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
    companyForm.setError("email", {
      type: "manual",
      message: "Please enter a valid email address"
    });
  } else {
    companyForm.clearErrors("email");
  }
}}
```

### 4. Comprehensive Validation Flow

**Enhanced Validation Functions with Tab Switching:**

```typescript
// Enhanced personal details validation with tab switching support
const validatePersonalDetails = async (switchToPersonalTab = false) => {
  const personalFields = ['full_name', 'username', 'email', 'password', 'confirmPassword'];
  const isValid = await userForm.trigger(personalFields as any);

  if (!isValid) {
    const errors = userForm.formState.errors;

    // Switch to personal tab if validation fails and requested
    if (switchToPersonalTab) {
      setActiveTab("personal");
    }

    // Show specific error toasts for each field...
    return false;
  }

  return true;
};

// Enhanced company details validation with tab switching support
const validateCompanyDetails = async (switchToCompanyTab = false) => {
  const isValid = await companyForm.trigger();

  if (!isValid) {
    const errors = companyForm.formState.errors;

    // Switch to company tab if validation fails and requested
    if (switchToCompanyTab) {
      setActiveTab("company");
    }

    // Show specific error toasts for each field...
    return false;
  }

  return true;
};

// Comprehensive validation for complete registration
const validateCompleteRegistration = async () => {
  // First validate personal details
  const personalDetailsValid = await validatePersonalDetails(true);
  if (!personalDetailsValid) {
    return false;
  }

  // Then validate company details
  const companyDetailsValid = await validateCompanyDetails(true);
  if (!companyDetailsValid) {
    return false;
  }

  return true;
};
```

**Tab Switching Validation:**

```typescript
<Tabs value={activeTab} onValueChange={async (value) => {
  // If switching to company tab, validate personal details first
  if (value === "company") {
    const isPersonalValid = await validatePersonalDetails();
    if (isPersonalValid) {
      setActiveTab(value);
    }
    // If validation fails, stay on personal tab and show toast
  } else {
    // Allow switching to personal tab without validation
    setActiveTab(value);
  }
}} className="w-full">
```

**Complete Registration Button Handler:**

```typescript
<Button
  type="button"
  onClick={async () => {
    // Validate both forms before proceeding
    const isValidRegistration = await validateCompleteRegistration();
    if (isValidRegistration) {
      // Get values from both forms
      const userValues = userForm.getValues();
      // Call onSubmit with the user values
      await onSubmit(userValues);
    }
  }}
  disabled={isLoading}
>
```

### 5. Enhanced Server Error Handling

**Comprehensive Error Message Extraction:**

```typescript
} catch (error) {
  console.error("Registration error:", error);

  // Extract the actual error message with comprehensive error handling
  let errorMessage = "An error occurred during registration";

  // Handle different error formats that might come from the API
  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  } else if (error && typeof error === 'object') {
    // Handle error objects from apiRequest (like {message: "Username already exists"})
    if ('message' in error && error.message) {
      errorMessage = String(error.message);
    } else if ('error' in error && error.error) {
      // Handle nested error objects
      if (typeof error.error === 'string') {
        errorMessage = error.error;
      } else if (typeof error.error === 'object' && error.error.message) {
        errorMessage = String(error.error.message);
      }
    }
  }

  toast({
    title: "Registration failed",
    description: errorMessage,
    variant: "destructive",
  });
}
```

**Simplified API Request Handling:**

```typescript
// Register using the API directly instead of the hook
// Note: apiRequest from queryClient automatically handles error responses and throws error objects
const response = await apiRequest('POST', '/api/auth/register', registrationData);
```

### 6. Login Error Handling Implementation

**Problem Identified:**
The login page had multiple issues preventing error messages from being displayed:
1. Duplicate error handling in both `auth.ts` and `login.tsx`
2. Form submission causing page reloads
3. Error messages being lost due to navigation/reload

**Solution Implemented:**

**1. Removed Duplicate Error Handling in auth.ts:**
```typescript
// Before: auth.ts was showing toast and returning null
const login = async (email: string, password: string): Promise<LoginResponse | null> => {
  try {
    // ... login logic
    return data;
  } catch (error) {
    console.error("Login error:", error);
    toast({
      title: "Login Failed",
      description: error instanceof Error ? error.message : "Invalid email or password",
      variant: "destructive",
    });
    return null; // This caused issues
  }
};

// After: auth.ts re-throws error for login page to handle
const login = async (email: string, password: string): Promise<LoginResponse | null> => {
  try {
    // ... login logic
    return data;
  } catch (error) {
    console.error("Login error:", error);
    // Don't show toast here - let the calling component handle the error display
    // This prevents duplicate error messages
    throw error; // Re-throw the error so the calling component can handle it
  }
};
```

**2. Enhanced Error Handling in login.tsx:**
```typescript
} catch (error) {
  console.error("Login error:", error);

  // Extract error message with comprehensive error handling
  let errorMessage = "An unknown error occurred";

  if (error instanceof Error) {
    errorMessage = error.message;
  } else if (typeof error === 'string') {
    errorMessage = error;
  } else if (error && typeof error === 'object') {
    // Handle error objects from apiRequest (like {message: "Invalid email or password"})
    if ('message' in error && error.message) {
      errorMessage = String(error.message);
    } else if ('error' in error && error.error) {
      // Handle nested error objects
      if (typeof error.error === 'string') {
        errorMessage = error.error;
      } else if (typeof error.error === 'object' && error.error.message) {
        errorMessage = String(error.error.message);
      }
    }
  }

  toast({
    title: "Login failed",
    description: errorMessage,
    variant: "destructive"
  });
}
```

**3. Prevented Form Submission Page Reload:**
```typescript
// Before: Default form submission behavior
<form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">

// After: Explicit preventDefault to stop page reload
<form onSubmit={(e) => {
  e.preventDefault(); // Prevent default form submission behavior
  form.handleSubmit(onSubmit)(e);
}} className="space-y-4">
```

**4. Fixed 401 Error Auto-Redirect Issue:**
```typescript
// Problem: All 401 errors were causing automatic redirects to login page
// Before: queryClient.ts was redirecting on ANY 401 error
if (res.status === 401) {
  // This was causing login page to reload when credentials were wrong
  window.location.href = '/login';
}

// After: Only redirect for 401 errors when NOT on login/register pages
if (res.status === 401) {
  // Only redirect for 401 errors if we're NOT on the login page
  // Login page 401 errors are for invalid credentials, not expired tokens
  const currentPath = isBrowser ? window.location.pathname : '';
  const isLoginPage = currentPath === '/login' || currentPath === '/register';

  if (!authErrorShown && isBrowser && !isLoginPage) {
    authErrorShown = true;
    // Redirect for expired tokens on other pages
    console.log('Authentication expired. Redirecting to login page...');
    localStorage.removeItem('auth_token');
    window.location.href = '/login';
  } else if (isLoginPage) {
    // On login page, 401 errors are for invalid credentials, not expired tokens
    // Don't clear token or redirect, just let the error propagate normally
    console.log('Login failed: Invalid credentials');
  }
}
```

### 7. Toast Notification System Overview

**Toast Component Structure:**
The application uses Shadcn/UI toast component with the following structure:

```typescript
import { useToast } from "@/hooks/use-toast";

const { toast } = useToast();

// Basic toast structure
toast({
  title: "Toast Title",           // Main heading (required)
  description: "Toast message",   // Detailed message (optional)
  variant: "default" | "destructive", // Style variant
});
```

**Toast Variants:**
- `default`: Standard blue/neutral styling for general notifications
- `destructive`: Red styling for errors and validation failures

**Toast Positioning:**
- Toasts appear in the top-right corner of the screen
- Multiple toasts stack vertically
- Auto-dismiss after 5 seconds (configurable)
- Users can manually dismiss by clicking the X button

### 7. Toast Message Patterns and Best Practices

**Field-Specific Validation Messages:**

```typescript
// Personal Details Validation Messages
if (errors.full_name) {
  toast({
    title: "Full Name Required",
    description: errors.full_name.message || "Full name must be at least 2 characters",
    variant: "destructive",
  });
}

if (errors.username) {
  toast({
    title: "Username Required",
    description: errors.username.message || "Username must be at least 3 characters",
    variant: "destructive",
  });
}

if (errors.email) {
  toast({
    title: "Email Required",
    description: errors.email.message || "Please enter a valid email address",
    variant: "destructive",
  });
}

if (errors.password) {
  toast({
    title: "Password Required",
    description: errors.password.message || "Password must be at least 8 characters",
    variant: "destructive",
  });
}

if (errors.confirmPassword) {
  toast({
    title: "Password Confirmation Required",
    description: errors.confirmPassword.message || "Passwords do not match",
    variant: "destructive",
  });
}
```

**Company Details Validation Messages:**

```typescript
// Company Details Validation Messages
if (errors.name) {
  toast({
    title: "Company Name Required",
    description: errors.name.message || "Company name is required",
    variant: "destructive",
  });
}

if (errors.email) {
  toast({
    title: "Company Email Required",
    description: errors.email.message || "Company email is required",
    variant: "destructive",
  });
}

if (errors.phone) {
  toast({
    title: "Phone Number Required",
    description: errors.phone.message || "Phone number must be exactly 10 digits with +91 country code",
    variant: "destructive",
  });
}
```

**Success Messages:**

```typescript
// Registration Success
toast({
  title: "Registration successful",
  description: "Your account has been created. Please login.",
  variant: "default", // or omit variant for default styling
});

// General Success Pattern
toast({
  title: "Action Completed",
  description: "Your changes have been saved successfully.",
});
```

**Server Error Messages:**

```typescript
// Server Error Handling with Dynamic Messages
toast({
  title: "Registration failed",
  description: errorMessage, // Dynamic message from server like "Username already exists"
  variant: "destructive",
});

// API Error Pattern
toast({
  title: "Operation Failed",
  description: serverErrorMessage || "An unexpected error occurred",
  variant: "destructive",
});
```

### 8. Toast Validation Implementation

**Validation Function with Toast Messages:**

```typescript
// Function to validate company details and show toast if invalid
const validateCompanyDetails = async () => {
  const isValid = await companyForm.trigger();

  if (!isValid) {
    const errors = companyForm.formState.errors;

    // Check for specific field errors and show appropriate toast
    if (errors.name) {
      toast({
        title: "Company Name Required",
        description: errors.name.message || "Company name is required",
        variant: "destructive",
      });
      return false;
    }

    if (errors.email) {
      toast({
        title: "Company Email Required",
        description: errors.email.message || "Company email is required",
        variant: "destructive",
      });
      return false;
    }

    if (errors.phone) {
      toast({
        title: "Phone Number Required",
        description: errors.phone.message || "Phone number must be exactly 10 digits with +91 country code",
        variant: "destructive",
      });
      return false;
    }

    // Generic validation error
    toast({
      title: "Company Details Required",
      description: "Please complete all required company fields",
      variant: "destructive",
    });
    return false;
  }

  return true;
};
```

## Toast Message Types

### 1. Company Name Validation
- **Trigger:** Empty company name field
- **Title:** "Company Name Required"
- **Description:** "Company name is required"
- **Variant:** destructive (red)

### 2. Company Email Validation
- **Trigger:** Empty company email field
- **Title:** "Company Email Required"
- **Description:** "Company email is required"
- **Variant:** destructive (red)

### 3. Invalid Email Format
- **Trigger:** Invalid email format
- **Title:** "Company Email Required"
- **Description:** "Please enter a valid email address"
- **Variant:** destructive (red)

### 4. Phone Number Validation
- **Trigger:** Invalid phone number format
- **Title:** "Phone Number Required"
- **Description:** "Phone number must be exactly 10 digits with +91 country code"
- **Variant:** destructive (red)

## Implementation Benefits

### 1. User Experience Improvements
- **Clear Error Feedback:** Users receive immediate, specific feedback about validation errors
- **Visual Indicators:** Red asterisks (*) clearly mark required fields
- **Real-time Validation:** Errors are caught and displayed as users type or leave fields
- **Consistent Messaging:** Standardized error messages across the application

### 2. Technical Benefits
- **Robust Validation:** Multiple layers of validation (schema + real-time)
- **Error Prevention:** Users cannot proceed without completing required fields
- **Maintainable Code:** Centralized validation logic with clear error handling
- **Accessibility:** Screen readers can announce error messages through toast notifications

## Testing Scenarios

### 1. Company Name Validation Test
1. Navigate to registration page
2. Fill personal details
3. Navigate to company details tab
4. Leave company name field empty
5. Try to complete registration
6. **Expected:** Toast notification "Company Name Required" appears

### 2. Company Email Validation Test
1. Navigate to registration page
2. Fill personal details
3. Navigate to company details tab
4. Leave company email field empty
5. Try to complete registration
6. **Expected:** Toast notification "Company Email Required" appears

### 3. Invalid Email Format Test
1. Navigate to registration page
2. Fill personal details
3. Navigate to company details tab
4. Enter invalid email format (e.g., "invalid-email")
5. Try to complete registration
6. **Expected:** Toast notification "Please enter a valid email address" appears

### 4. Direct Company Tab Navigation Test (Critical Fix)
1. Navigate to registration page
2. **Directly click on "Company Details" tab** without filling personal details
3. Fill some company details
4. Click "Complete Registration"
5. **Expected:**
   - Toast notification for missing personal details appears
   - User is automatically switched to "Personal Details" tab
   - Registration process is halted until personal details are completed

### 5. Tab Switching Validation Test
1. Navigate to registration page
2. Try to click "Company Details" tab without filling personal details
3. **Expected:**
   - Toast notification for missing personal details appears
   - User remains on "Personal Details" tab
   - Cannot switch to company details until personal details are valid

### 6. Comprehensive Registration Flow Test
1. Navigate to registration page
2. Fill personal details completely
3. Navigate to company details tab (should work now)
4. Leave company details empty
5. Click "Complete Registration"
6. **Expected:**
   - Toast notification for missing company details appears
   - User is automatically switched to "Company Details" tab
   - Registration process is halted until company details are completed

### 7. Single Toast Message Test (Duplicate Toast Fix)
1. Navigate to registration page
2. Leave all personal details empty
3. Click "Company Details" tab
4. **Expected:**
   - Only ONE toast notification appears (for the first missing field - Full Name)
   - No multiple/duplicate toast messages
   - User remains on "Personal Details" tab

### 8. Server Error Message Display Test (Username Already Exists)
1. Navigate to registration page
2. Fill personal details with a username that already exists in the system
3. Fill all other required fields correctly
4. Navigate to company details tab
5. Fill all required company details correctly
6. Click "Complete Registration"
7. **Expected:**
   - Toast notification appears with title "Registration failed"
   - Toast description shows the actual server message: "Username already exists"
   - No generic error message is displayed

### 9. Login Error Message Test (Critical Fix)
1. Navigate to login page
2. Enter valid email format but wrong password
3. Click "Login" button
4. **Expected:**
   - Toast notification appears with "Login failed" title
   - Toast description shows "Invalid email or password"
   - **No page reload occurs**
   - User remains on login page and can try again
   - Error message is visible and readable

### 10. API Error Message Display Test
1. Navigate to Partners page
2. Try to create a partner without configuring company prefix settings
3. **Expected:**
   - Toast shows specific API error: "Company prefix settings not configured. Please configure prefix settings before creating this entity."
   - **NOT** showing generic message: "Failed to create partner. Please try again."
4. Navigate to Financial → Accounts
5. Try to create an account with invalid data
6. **Expected:**
   - Toast shows specific API validation error message
   - **NOT** showing generic "Failed to create account" message

### 11. Successful Registration Test
1. Navigate to registration page
2. Fill all personal details correctly with unique username and email
3. Navigate to company details tab
4. Fill all required company details correctly
5. Click "Complete Registration"
6. **Expected:**
   - No validation errors
   - Registration proceeds successfully
   - Success toast appears
   - User is redirected to login page

## Best Practices Applied

### 1. Validation Strategy
- **Client-side validation:** Immediate feedback for better UX
- **Schema validation:** Type-safe validation using Zod
- **Real-time validation:** Validation on both `onChange` and `onBlur` events

### 2. Error Messaging
- **Specific messages:** Clear, actionable error descriptions
- **Consistent tone:** Professional, helpful language
- **User-friendly:** Non-technical language that users can understand

### 3. UI/UX Principles
- **Visual hierarchy:** Required fields clearly marked with red asterisks
- **Immediate feedback:** Toast notifications appear instantly
- **Non-blocking:** Users can dismiss notifications and continue working
- **Accessible:** Compatible with screen readers and keyboard navigation

## Future Enhancements

### 1. Additional Validation
- Email domain validation for business emails
- Company name uniqueness check
- Phone number format validation for different countries

### 2. Enhanced User Experience
- Progress indicators for form completion
- Auto-save functionality for partially completed forms
- Smart suggestions for common company domains

### 3. Analytics Integration
- Track validation error frequency
- Monitor user completion rates
- Identify common user pain points

## Conclusion

The comprehensive toast notification implementation for form validation errors significantly improves the user experience by providing clear, immediate feedback when validation fails. This implementation follows modern web development best practices and ensures that users cannot proceed with incomplete or invalid data, maintaining data integrity while providing excellent user experience.

## Quick Reference Guide

### Basic Toast Usage
```typescript
import { useToast } from "@/hooks/use-toast";
const { toast } = useToast();

// Error Toast
toast({
  title: "Error Title",
  description: "Error description",
  variant: "destructive",
});

// Success Toast
toast({
  title: "Success Title",
  description: "Success description",
});
```

### Error Handling Pattern
```typescript
try {
  // API call
} catch (error) {
  const errorMessage = extractErrorMessage(error, "Default message");
  toast({
    title: "Operation Failed",
    description: errorMessage,
    variant: "destructive",
  });
}
```

### Form Validation Pattern
```typescript
const validateForm = async () => {
  const isValid = await form.trigger();
  if (!isValid) {
    const errors = form.formState.errors;
    // Show first error only
    if (errors.fieldName) {
      toast({
        title: "Field Name Required",
        description: errors.fieldName.message || "Default message",
        variant: "destructive",
      });
      return false;
    }
  }
  return true;
};
```

### Key Principles
1. **One Toast at a Time**: Prevent multiple toasts for better UX
2. **Specific Error Messages**: Show actual server messages when available
3. **Actionable Feedback**: Guide users on what to do next
4. **Consistent Patterns**: Use standardized message formats
5. **Proper Variants**: Use `destructive` for errors, `default` for success

### Key Improvements Implemented:

1. **Comprehensive Validation Flow**: Both personal and company details are validated before registration
2. **Tab Switching Protection**: Users cannot bypass personal details by jumping directly to company details
3. **Automatic Tab Navigation**: When validation fails, users are automatically directed to the relevant tab
4. **Enhanced Error Handling**: All validation scenarios now properly display toast notifications
5. **Robust Registration Process**: The "Complete Registration" button validates both forms comprehensively
6. **User-Friendly Experience**: Clear feedback guides users through the registration process step by step

### 9. Toast Implementation Guidelines for Future Development

**Toast Message Structure Guidelines:**

1. **Title Guidelines:**
   - Keep titles concise and action-oriented
   - Use present tense for ongoing actions ("Loading...", "Saving...")
   - Use past tense for completed actions ("Saved", "Updated", "Created")
   - For errors, be specific about what failed ("Email Required", "Login Failed")

2. **Description Guidelines:**
   - Provide actionable information
   - Include specific error details when available
   - Suggest next steps when appropriate
   - Keep messages user-friendly, avoid technical jargon

3. **Variant Usage:**
   - `destructive`: Errors, validation failures, critical warnings
   - `default`: Success messages, informational updates, confirmations

**Common Toast Patterns:**

```typescript
// Loading States
toast({
  title: "Processing...",
  description: "Please wait while we process your request.",
});

// Validation Errors
toast({
  title: "[Field Name] Required",
  description: "[Specific validation rule or helpful message]",
  variant: "destructive",
});

// Server Errors
toast({
  title: "[Action] Failed",
  description: serverMessage || "An unexpected error occurred. Please try again.",
  variant: "destructive",
});

// Success Messages
toast({
  title: "[Action] Successful",
  description: "Your [item] has been [action] successfully.",
});

// Confirmation Messages
toast({
  title: "Changes Saved",
  description: "Your settings have been updated.",
});
```

**Error Handling Best Practices:**

```typescript
// Comprehensive Error Message Extraction
const extractErrorMessage = (error: any, defaultMessage: string = "An error occurred") => {
  if (error instanceof Error) {
    return error.message;
  } else if (typeof error === 'string') {
    return error;
  } else if (error && typeof error === 'object') {
    if ('message' in error && error.message) {
      return String(error.message);
    } else if ('error' in error && error.error) {
      if (typeof error.error === 'string') {
        return error.error;
      } else if (typeof error.error === 'object' && error.error.message) {
        return String(error.error.message);
      }
    }
  }
  return defaultMessage;
};

// Usage in try-catch blocks
try {
  // API call
} catch (error) {
  const errorMessage = extractErrorMessage(error, "Operation failed");
  toast({
    title: "Operation Failed",
    description: errorMessage,
    variant: "destructive",
  });
}
```

**Form Validation Toast Patterns:**

```typescript
// Single Field Validation
const validateField = (fieldName: string, value: any, rules: ValidationRule[]) => {
  for (const rule of rules) {
    if (!rule.validate(value)) {
      toast({
        title: `${fieldName} ${rule.errorType}`,
        description: rule.message,
        variant: "destructive",
      });
      return false;
    }
  }
  return true;
};

// Multi-Field Validation (show only first error)
const validateForm = async (formData: any) => {
  const fields = [
    { name: 'email', value: formData.email, rules: emailRules },
    { name: 'password', value: formData.password, rules: passwordRules },
    // ... other fields
  ];

  for (const field of fields) {
    if (!validateField(field.name, field.value, field.rules)) {
      return false; // Stop at first error to avoid multiple toasts
    }
  }
  return true;
};
```

**Toast Integration with Different Components:**

```typescript
// Button Click Actions
const handleSaveButton = async () => {
  try {
    setLoading(true);
    await saveData();
    toast({
      title: "Data Saved",
      description: "Your changes have been saved successfully.",
    });
  } catch (error) {
    toast({
      title: "Save Failed",
      description: extractErrorMessage(error, "Failed to save data"),
      variant: "destructive",
    });
  } finally {
    setLoading(false);
  }
};

// Form Submission
const handleFormSubmit = async (data: FormData) => {
  try {
    await submitForm(data);
    toast({
      title: "Form Submitted",
      description: "Your form has been submitted successfully.",
    });
    navigate("/success");
  } catch (error) {
    toast({
      title: "Submission Failed",
      description: extractErrorMessage(error, "Failed to submit form"),
      variant: "destructive",
    });
  }
};

// Delete Confirmation
const handleDelete = async (id: string) => {
  try {
    await deleteItem(id);
    toast({
      title: "Item Deleted",
      description: "The item has been permanently removed.",
    });
    refreshList();
  } catch (error) {
    toast({
      title: "Delete Failed",
      description: extractErrorMessage(error, "Failed to delete item"),
      variant: "destructive",
    });
  }
};
```

**Toast Timing and UX Considerations:**

```typescript
// For immediate feedback (form validation)
toast({
  title: "Validation Error",
  description: "Please check the highlighted fields",
  variant: "destructive",
  // Auto-dismiss quickly for validation errors
});

// For success actions (longer display)
toast({
  title: "Success",
  description: "Operation completed successfully",
  // Default timing is appropriate for success messages
});

// For critical errors (user should acknowledge)
toast({
  title: "Critical Error",
  description: "Please contact support if this persists",
  variant: "destructive",
  // Consider longer display time for critical errors
});
```

**Toast Message Localization Pattern:**

```typescript
// Centralized message definitions
const TOAST_MESSAGES = {
  VALIDATION: {
    EMAIL_REQUIRED: {
      title: "Email Required",
      description: "Please enter a valid email address"
    },
    PASSWORD_WEAK: {
      title: "Password Too Weak",
      description: "Password must be at least 8 characters with numbers and symbols"
    }
  },
  SUCCESS: {
    REGISTRATION: {
      title: "Registration Successful",
      description: "Your account has been created. Please login."
    },
    SAVE: {
      title: "Changes Saved",
      description: "Your changes have been saved successfully."
    }
  },
  ERROR: {
    NETWORK: {
      title: "Connection Error",
      description: "Please check your internet connection and try again."
    },
    SERVER: {
      title: "Server Error",
      description: "An unexpected error occurred. Please try again later."
    }
  }
};

// Usage
toast({
  ...TOAST_MESSAGES.VALIDATION.EMAIL_REQUIRED,
  variant: "destructive"
});
```

### Critical Bugs Fixed:

1. **Primary Issue**: The major issue where users could click directly on "Company Details" tab and then "Complete Registration" without seeing personal details validation errors has been completely resolved. Now, all validation errors are properly displayed via toast notifications, and users are guided to complete all required fields in the correct order.

2. **Duplicate Toast Issue**: Fixed the problem where clicking the "Company Details" tab without entering required fields would show multiple toast messages. Now only one specific error toast is shown at a time, providing a cleaner user experience.

3. **Server Error Message Display**: Fixed the issue where server error messages (like "Username already exists") were not being displayed in toast notifications. The system was showing generic "An error occurred during registration" instead of the actual server response. Now all server error messages are properly extracted and displayed to users.

4. **Login Page Error Handling**: Fixed the critical issue where login error messages (like "Invalid email or password") were not being displayed due to page reloading and duplicate error handling. The system now properly shows server error messages without page reloads, allowing users to see and respond to authentication errors.

5. **401 Error Handling on Login Page**: Fixed the root cause where 401 errors (invalid credentials) on the login page were triggering automatic redirects to the login page, causing instant page reloads. The system now distinguishes between authentication token expiry (redirect) and login credential errors (show toast), preventing unwanted page reloads during login attempts.

6. **Transaction Reference Display**: Fixed the issue where transaction details were showing internal IDs instead of proper reference codes. See `transaction-reference-display-fix.md` for detailed implementation.

7. **API Error Message Display**: Fixed multiple pages that were showing generic error messages instead of actual API error messages in toast notifications. Updated partners, financial accounts, and transaction pages to display the specific error message from the API response (e.g., "Company prefix settings not configured" instead of "Failed to create partner").

### Validation Logic Improvements:

- **Single Toast Policy**: Each validation function now shows only the first error found, preventing multiple toast notifications from appearing simultaneously
- **Priority-Based Error Display**: Errors are checked in logical order (full name → username → email → password → confirm password) and only the first missing field triggers a toast
- **Cleaner User Experience**: Users see one clear, actionable error message at a time instead of being overwhelmed with multiple notifications
- **Enhanced Server Error Handling**: Comprehensive error parsing that handles different error object formats from the API, ensuring server messages like "Username already exists" are properly displayed
- **Robust Error Extraction**: The system now handles Error instances, string errors, and complex error objects with nested message properties
