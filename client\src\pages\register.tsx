import { useState, useEffect } from "react";
import { useLocation } from "wouter";
import { useAuth } from "@/lib/auth";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { insertCompanySchema } from "@shared/schema";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

import { useToast } from "@/hooks/use-toast";
import { Loader2, Building2, CheckCircle2 } from "lucide-react";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { apiRequest } from "@/lib/queryClient";
import { usdToFormattedInr } from "@/lib/currency";

// Define country code constant
const COUNTRY_CODE = '+91';

// Plan type for subscription plans
type SubscriptionPlan = {
  id: number;
  name: string;
  description: string | null;
  price: string;
  billing_period: string;
  features: string[] | null;
  is_active: boolean;
};

export default function Register() {
  const [isLoading, setIsLoading] = useState(false);
  const [subscriptionPlans, setSubscriptionPlans] = useState<SubscriptionPlan[]>([]);
  const [selectedPlan, setSelectedPlan] = useState<number | null>(null);
  const [activeTab, setActiveTab] = useState("personal");

  const { } = useAuth(); // Auth hook used for context
  const [, navigate] = useLocation();
  const { toast } = useToast();

  // Define the user form schema - all users are company_admin by default
  const extendedFormSchema = z.object({
    email: z.string().email({ message: "Please enter a valid email address" }),
    password: z.string().min(8, { message: "Password must be at least 8 characters" }),
    confirmPassword: z.string(),
    full_name: z.string().min(2, { message: "Full name must be at least 2 characters" }),
    // All users will be company_admin by default
    role: z.literal("company_admin").default("company_admin")
  }).refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"]
  });

  // Fetch subscription plans
  useEffect(() => {
    const fetchPlans = async () => {
      try {
        const response = await fetch('/api/public/subscription-plans');
        if (response.ok) {
          const data = await response.json();
          setSubscriptionPlans(data.filter((plan: SubscriptionPlan) => plan.is_active));

          // Set default selected plan to the first one if available
          if (data.length > 0) {
            setSelectedPlan(data[0].id);
          }
        } else {
          console.error('Error fetching subscription plans:', response.statusText);
        }
      } catch (error) {
        console.error('Error fetching subscription plans:', error);
      }
    };

    fetchPlans();
  }, []);

  // User form
  const userForm = useForm<z.infer<typeof extendedFormSchema>>({
    resolver: zodResolver(extendedFormSchema),
    defaultValues: {
      email: "",
      password: "",
      confirmPassword: "",
      full_name: "",
      role: "company_admin" // All users are company admins
    },
  });

  // Extend company schema to include validation for required fields
  const extendedCompanySchema = insertCompanySchema.extend({
    // Company name validation - required field
    name: z.string()
      .min(1, { message: "Company name is required" })
      .min(2, { message: "Company name must be at least 2 characters" }),

    // Email validation - required field
    email: z.string()
      .min(1, { message: "Company email is required" })
      .email({ message: "Please enter a valid email address" }),

    // Phone number validation
    phone: z.string().refine(
      (value) => {
        // Accept only format with country code followed by exactly 10 digits
        // Escape the + in the country code for regex
        const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
        const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
        return pattern.test(value);
      },
      { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
    )
  });

  // Company form
  const companyForm = useForm<z.infer<typeof extendedCompanySchema>>({
    resolver: zodResolver(extendedCompanySchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
      website: "",
    },
  });

  // Function to validate personal details and show toast if invalid
  const validatePersonalDetails = async (switchToPersonalTab = false) => {
    const personalFields = ['full_name', 'email', 'password', 'confirmPassword'];
    const isValid = await userForm.trigger(personalFields as any);

    if (!isValid) {
      const errors = userForm.formState.errors;

      // Switch to personal tab if validation fails and requested
      if (switchToPersonalTab) {
        setActiveTab("personal");
      }

      // Check for specific field errors and show appropriate toast
      // Show only the first error found to avoid multiple toasts
      if (errors.full_name) {
        toast({
          title: "Full Name Required",
          description: errors.full_name.message || "Full name must be at least 2 characters",
          variant: "destructive",
        });
        return false;
      }

      if (errors.email) {
        toast({
          title: "Email Required",
          description: errors.email.message || "Please enter a valid email address",
          variant: "destructive",
        });
        return false;
      }

      if (errors.password) {
        toast({
          title: "Password Required",
          description: errors.password.message || "Password must be at least 8 characters",
          variant: "destructive",
        });
        return false;
      }

      if (errors.confirmPassword) {
        toast({
          title: "Password Confirmation Required",
          description: errors.confirmPassword.message || "Passwords do not match",
          variant: "destructive",
        });
        return false;
      }

      // Generic validation error - only show if no specific errors were found
      // This should rarely happen since we check all fields above
      toast({
        title: "Personal Details Required",
        description: "Please complete all required personal details",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  // Function to validate company details and show toast if invalid
  const validateCompanyDetails = async (switchToCompanyTab = false) => {
    const isValid = await companyForm.trigger();

    if (!isValid) {
      const errors = companyForm.formState.errors;

      // Switch to company tab if validation fails and requested
      if (switchToCompanyTab) {
        setActiveTab("company");
      }

      // Check for specific field errors and show appropriate toast
      // Show only the first error found to avoid multiple toasts
      if (errors.name) {
        toast({
          title: "Company Name Required",
          description: errors.name.message || "Company name is required",
          variant: "destructive",
        });
        return false;
      }

      if (errors.email) {
        toast({
          title: "Company Email Required",
          description: errors.email.message || "Company email is required",
          variant: "destructive",
        });
        return false;
      }

      if (errors.phone) {
        toast({
          title: "Phone Number Required",
          description: errors.phone.message || "Phone number must be exactly 10 digits with +91 country code",
          variant: "destructive",
        });
        return false;
      }

      // Generic validation error - only show if no specific errors were found
      // This should rarely happen since we check all fields above
      toast({
        title: "Company Details Required",
        description: "Please complete all required company fields",
        variant: "destructive",
      });
      return false;
    }

    return true;
  };

  // Comprehensive validation function for complete registration
  const validateCompleteRegistration = async () => {
    // First validate personal details
    const personalDetailsValid = await validatePersonalDetails(true);
    if (!personalDetailsValid) {
      return false;
    }

    // Then validate company details
    const companyDetailsValid = await validateCompanyDetails(true);
    if (!companyDetailsValid) {
      return false;
    }

    return true;
  };

  // Tab navigation is handled directly by the TabsTrigger component

  async function onSubmit(values: z.infer<typeof extendedFormSchema>) {
    setIsLoading(true);

    try {
      // Validation is now handled before calling this function
      // This function focuses on the actual registration process

      // Get company form values (for company creation)
      const companyValues = companyForm.getValues();

      // Make sure we're using the personal email from personal details for the user account
      // and preserving the company email for company details
      const userPersonalEmail = values.email;

      // Prepare registration data
      const registrationData = {
        ...values,
        email: userPersonalEmail, // Ensure we're using personal email for the user account
        // Include company data if user is company_admin
        ...(values.role === "company_admin" && {
          company: companyValues, // Company email is preserved here
          subscriptionPlanId: selectedPlan
        })
      };

      // Register using the API directly instead of the hook
      // Note: apiRequest from queryClient automatically handles error responses and throws error objects
      const response = await apiRequest('POST', '/api/auth/register', registrationData);

      toast({
        title: "Registration successful",
        description: "Your account has been created. Please login.",
      });

      navigate("/login");
    } catch (error) {
      console.error("Registration error:", error);

      // Extract the actual error message with comprehensive error handling
      let errorMessage = "An error occurred during registration";

      // Handle different error formats that might come from the API
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (error && typeof error === 'object') {
        // Handle error objects from apiRequest (like {message: "Username already exists"})
        if ('message' in error && error.message) {
          errorMessage = String(error.message);
        } else if ('error' in error && error.error) {
          // Handle nested error objects
          if (typeof error.error === 'string') {
            errorMessage = error.error;
          } else if (typeof error.error === 'object' && error.error.message) {
            errorMessage = String(error.error.message);
          }
        }
      }

      toast({
        title: "Registration failed",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-background bg-[radial-gradient(ellipse_at_top,_var(--tw-gradient-stops))] from-primary/20 via-background to-background">
      <div className="absolute inset-0 z-0 bg-[url('/assets/grid-pattern.svg')] bg-center opacity-5"></div>
      <Card className="w-full max-w-md mx-4 shadow-xl border-primary/20 overflow-hidden relative z-10">
        <div className="w-full bg-gradient-to-b from-primary/10 to-primary/5 border-b border-primary/20">
          <CardHeader className="space-y-2 pb-4 pt-6">
            <div className="flex justify-center mb-3">
              <div className="h-16 w-16 rounded-full bg-gradient-to-br from-primary/20 to-primary/10 flex items-center justify-center shadow-md border border-primary/30 relative">
                <div className="absolute inset-0 rounded-full bg-primary/5 animate-pulse opacity-70"></div>
                <svg className="h-10 w-10 text-primary relative z-10" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 8V16M8 12H16M22 12C22 17.5228 17.5228 22 12 22C6.47715 22 2 17.5228 2 12C2 6.47715 6.47715 2 12 2C17.5228 2 22 6.47715 22 12Z"
                    stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </div>
            </div>
            <CardTitle className="text-xl font-bold text-center bg-clip-text text-transparent bg-gradient-to-r from-primary to-primary/80">TrackFina</CardTitle>
            <CardDescription className="text-center text-sm">
              Create your account to get started
            </CardDescription>
          </CardHeader>
        </div>
        <CardContent className="pt-5 pb-5">
          <Tabs value={activeTab} onValueChange={async (value) => {
            // If switching to company tab, validate personal details first
            if (value === "company") {
              const isPersonalValid = await validatePersonalDetails();
              if (isPersonalValid) {
                setActiveTab(value);
              }
              // If validation fails, stay on personal tab and show toast
            } else {
              // Allow switching to personal tab without validation
              setActiveTab(value);
            }
          }} className="w-full">
            <TabsList className="w-full grid grid-cols-2 mb-6 bg-primary/5 p-1 border border-primary/20 rounded-md">
              <TabsTrigger value="personal" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Personal Details</TabsTrigger>
              <TabsTrigger value="company" className="data-[state=active]:bg-primary/10 data-[state=active]:text-primary data-[state=active]:shadow-sm">Company Details</TabsTrigger>
            </TabsList>

            <TabsContent value="personal">
              <Form {...userForm}>
                <form className="space-y-4">
                  <FormField
                    control={userForm.control}
                    name="full_name"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">Full Name</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter your full name"
                            className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={userForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">Email (used for login)</FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="Enter your email"
                            className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <FormField
                      control={userForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel className="text-sm font-medium">Password</FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Create a password"
                              className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={userForm.control}
                      name="confirmPassword"
                      render={({ field }) => (
                        <FormItem className="space-y-1.5">
                          <FormLabel className="text-sm font-medium">Confirm Password</FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Confirm your password"
                              className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Hidden role field - always company_admin */}
                  <input type="hidden" name="role" value="company_admin" />

                  <Button
                    type="button"
                    className="w-full h-10 mt-6 font-semibold text-sm shadow-md"
                    onClick={async () => {
                      const isValid = await validatePersonalDetails();
                      if (isValid) {
                        setActiveTab("company");
                      }
                    }}
                  >
                    <Building2 className="mr-2 h-4 w-4" />
                    Next: Company Details
                  </Button>
                </form>
              </Form>
            </TabsContent>

            <TabsContent value="company">
              <div className="mb-6">
                <h3 className="text-lg font-semibold mb-2">Company Information</h3>
                <p className="text-sm text-muted-foreground">
                  As a company administrator, you'll need to provide details about your organization.
                </p>
              </div>

              <Form {...companyForm}>
                <form className="space-y-4">
                  <FormField
                    control={companyForm.control}
                    name="name"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">
                          Company Name
                          <span className="text-destructive ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            placeholder="Enter company name"
                            className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Use separate fields for email and phone instead of grid layout */}
                  <FormField
                    control={companyForm.control}
                    name="email"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">
                          Company Email (for business correspondence only)
                          <span className="text-destructive ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <Input
                            type="email"
                            placeholder="<EMAIL>"
                            className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            value={field.value || ''}
                            onChange={(e) => {
                              field.onChange(e);

                              // Validate email on change
                              const emailValue = e.target.value.trim();
                              if (!emailValue) {
                                companyForm.setError("email", {
                                  type: "manual",
                                  message: "Company email is required"
                                });
                              } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                                companyForm.setError("email", {
                                  type: "manual",
                                  message: "Please enter a valid email address"
                                });
                              } else {
                                companyForm.clearErrors("email");
                              }
                            }}
                            onBlur={(e) => {
                              field.onBlur();

                              // Validate email on blur
                              const emailValue = e.target.value.trim();
                              if (!emailValue) {
                                companyForm.setError("email", {
                                  type: "manual",
                                  message: "Company email is required"
                                });
                              } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(emailValue)) {
                                companyForm.setError("email", {
                                  type: "manual",
                                  message: "Please enter a valid email address"
                                });
                              } else {
                                companyForm.clearErrors("email");
                              }
                            }}
                            ref={field.ref}
                            name={field.name}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={companyForm.control}
                    name="phone"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">
                          Phone Number
                          <span className="text-destructive ml-1">*</span>
                        </FormLabel>
                        <FormControl>
                          <div className="flex w-full" style={{ display: "flex", width: "100%" }}>
                            <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground" style={{ flexShrink: 0, width: "50px" }}>
                              {COUNTRY_CODE}
                            </span>
                            <div style={{ flex: 1, width: "100%" }}>
                              <Input
                                placeholder="XXXXXXXXXX"
                                className="rounded-l-none h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300 w-full"
                                value={field.value?.replace(new RegExp(`^${COUNTRY_CODE.replace('+', '\\+')}`), '')}
                                onChange={(e) => {
                                // Remove non-digit characters
                                const digitsOnly = e.target.value.replace(/\D/g, '');

                                // Trim to 10 digits max
                                const trimmed = digitsOnly.substring(0, 10);

                                // Update form value with country code prefix
                                field.onChange(`${COUNTRY_CODE}${trimmed}`);

                                // Validate phone number
                                if (digitsOnly.length > 0 && digitsOnly.length < 10) {
                                  companyForm.setError("phone", {
                                    type: "manual",
                                    message: `Phone number must be exactly 10 digits (currently ${digitsOnly.length})`
                                  });
                                } else if (digitsOnly.length === 10) {
                                  // Clear error when exactly 10 digits
                                  companyForm.clearErrors("phone");
                                }
                              }}
                            />
                            </div>
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={companyForm.control}
                    name="address"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">Address</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter company address"
                            className="text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            value={field.value || ''}
                            onChange={field.onChange}
                            onBlur={field.onBlur}
                            ref={field.ref}
                            name={field.name}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={companyForm.control}
                    name="website"
                    render={({ field }) => (
                      <FormItem className="space-y-1.5">
                        <FormLabel className="text-sm font-medium">Website</FormLabel>
                        <FormControl>
                          <Input
                            placeholder="https://yourcompany.com"
                            className="h-10 text-sm shadow-sm focus:border-primary/50 transition-all duration-300"
                            value={field.value || ''}
                            onChange={field.onChange}
                            onBlur={field.onBlur}
                            ref={field.ref}
                            name={field.name}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {subscriptionPlans.length > 0 && (
                    <div className="mt-6">
                      <h3 className="text-lg font-semibold mb-4">Select a Subscription Plan</h3>
                      <div className="grid grid-cols-1 gap-4">
                        {subscriptionPlans.map((plan) => (
                          <div
                            key={plan.id}
                            className={`border rounded-lg p-4 cursor-pointer transition-all ${
                              selectedPlan === plan.id ? 'border-primary bg-primary/5' : 'border-border hover:border-primary/50'
                            }`}
                            onClick={() => setSelectedPlan(plan.id)}
                          >
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="flex items-center gap-2">
                                  <h4 className="font-medium">{plan.name}</h4>
                                  <Badge variant={plan.billing_period === 'monthly' ? 'default' : 'secondary'}>
                                    {plan.billing_period === 'monthly' ? 'Monthly' : 'Annual'}
                                  </Badge>
                                </div>
                                <p className="text-sm text-muted-foreground mt-1">{plan.description}</p>
                              </div>
                              <div className="flex items-center">
                                <span className="font-bold text-lg">{usdToFormattedInr(parseFloat(plan.price))}</span>
                                <span className="text-xs text-muted-foreground ml-1">
                                  /{plan.billing_period === 'monthly' ? 'mo' : 'yr'}
                                </span>
                              </div>
                            </div>

                            {plan.features && plan.features.length > 0 && (
                              <div className="mt-3">
                                <Accordion type="single" collapsible>
                                  <AccordionItem value="features">
                                    <AccordionTrigger className="text-sm">View features</AccordionTrigger>
                                    <AccordionContent>
                                      <ul className="text-sm space-y-1 mt-2">
                                        {plan.features.map((feature, index) => (
                                          <li key={index} className="flex items-start gap-2">
                                            <CheckCircle2 className="h-4 w-4 text-primary shrink-0 mt-0.5" />
                                            <span>{feature}</span>
                                          </li>
                                        ))}
                                      </ul>
                                    </AccordionContent>
                                  </AccordionItem>
                                </Accordion>
                              </div>
                            )}

                            {selectedPlan === plan.id && (
                              <div className="absolute top-2 right-2">
                                <CheckCircle2 className="h-5 w-5 text-primary" />
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  <div className="flex gap-4 mt-6">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveTab("personal")}
                      className="flex-1 h-10 text-sm font-medium"
                    >
                      Back
                    </Button>
                    <Button
                      type="button"
                      className="flex-1 h-10 font-semibold text-sm shadow-md"
                      onClick={async () => {
                        // Validate both forms before proceeding
                        const isValidRegistration = await validateCompleteRegistration();
                        if (isValidRegistration) {
                          // Get values from both forms
                          const userValues = userForm.getValues();
                          // Call onSubmit with the user values
                          await onSubmit(userValues);
                        }
                      }}
                      disabled={isLoading}
                    >
                      {isLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Registering...
                        </>
                      ) : (
                        <>
                          <CheckCircle2 className="mr-2 h-4 w-4" />
                          Complete Registration
                        </>
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="flex flex-col pt-3 pb-5 border-t">
          <div className="text-center">
            <p className="text-muted-foreground mb-2 text-sm">Already have an account?</p>
            <Button
              variant="secondary"
              className="h-10 px-8 text-sm font-semibold bg-primary/10 hover:bg-primary/20 text-primary border border-primary/30 transition-all duration-200 hover:scale-105"
              onClick={() => navigate("/login")}
            >
              Login Now
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}
