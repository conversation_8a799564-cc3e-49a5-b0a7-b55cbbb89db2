import { db } from '../db';
import { up } from './migrations/20250515_add_company_settings';

async function runMigration() {
  try {
    console.log('Running migration: 20250515_add_company_settings');
    await up(db);
    console.log('Migration completed successfully');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
  } finally {
    process.exit(0);
  }
}

runMigration();
