import { Express, Response } from 'express';
import { authMiddleware, AuthRequest } from '../middleware/auth';
import { requirePermission } from '../middleware/permission';
import { EnhancedPermissionService } from '../services/enhancedPermissionService';
import { db } from '../db';
import { permissions, customRoles, rolePermissions, userRoles, users } from '../../shared/schema';
import { eq, inArray, and, sql } from 'drizzle-orm';

const permissionService = new EnhancedPermissionService();

export function registerEnhancedPermissionRoutes(app: Express): void {

  // Test endpoint to check if auth middleware is being applied
  app.get('/api/permissions/test', (req: any, res: Response) => {
    res.json({ message: 'Test endpoint working without auth', timestamp: new Date().toISOString() });
  });

  // Get permission categories with grouped permissions - allow any user for demo purposes
  app.get('/api/permissions/categories', async (req: AuthRequest, res: Response) => {
    try {
      // Get all permissions grouped by category
      const allPermissions = await db.select().from(permissions);

      const categorizedPermissions = allPermissions.reduce((acc, permission) => {
        if (!acc[permission.category]) {
          acc[permission.category] = [];
        }
        acc[permission.category].push(permission);
        return acc;
      }, {} as Record<string, typeof allPermissions>);

      // Add category metadata
      const categoryMetadata = {
        loan_management: {
          name: 'Loan Management',
          description: 'Permissions related to loan creation, approval, and management',
          icon: 'CreditCard'
        },
        customer_management: {
          name: 'Customer Management',
          description: 'Permissions for customer data access and communication',
          icon: 'Users'
        },
        financial_management: {
          name: 'Financial Management',
          description: 'Permissions for payment processing and financial operations',
          icon: 'DollarSign'
        },
        report_management: {
          name: 'Report Management',
          description: 'Permissions for viewing and creating reports',
          icon: 'BarChart'
        },
        user_management: {
          name: 'User Management',
          description: 'Permissions for managing users and access',
          icon: 'UserCheck'
        },
        role_management: {
          name: 'Role Management',
          description: 'Permissions for managing roles and permissions',
          icon: 'Shield'
        },
        company_management: {
          name: 'Company Management',
          description: 'Permissions for company settings and configuration',
          icon: 'Building'
        },
        system_settings: {
          name: 'System Settings',
          description: 'Permissions for system administration and settings',
          icon: 'Settings'
        }
      };

      const result = Object.entries(categorizedPermissions).map(([category, perms]) => ({
        category,
        metadata: categoryMetadata[category as keyof typeof categoryMetadata] || {
          name: category.replace('_', ' ').toUpperCase(),
          description: `Permissions for ${category.replace('_', ' ')}`,
          icon: 'Lock'
        },
        permissions: perms
      }));

      return res.json(result);
    } catch (error) {
      console.error('Error fetching permission categories:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Bulk assign permissions to roles
  app.post('/api/permissions/bulk-assign', authMiddleware, requirePermission('permission_management'), async (req: AuthRequest, res: Response) => {
    try {
      const { assignments } = req.body;

      if (!Array.isArray(assignments)) {
        return res.status(400).json({ message: 'Assignments must be an array' });
      }

      const results = [];
      const errors = [];

      for (const assignment of assignments) {
        try {
          const { role_id, permission_ids, action } = assignment;

          if (!role_id || !Array.isArray(permission_ids) || !['grant', 'revoke'].includes(action)) {
            errors.push({
              assignment,
              error: 'Invalid assignment format. Required: role_id, permission_ids (array), action (grant/revoke)'
            });
            continue;
          }

          if (action === 'grant') {
            // Add permissions to role
            for (const permission_id of permission_ids) {
              await db.insert(rolePermissions)
                .values({ role_id, permission_id })
                .onConflictDoNothing();
            }
            results.push({
              role_id,
              action: 'granted',
              permission_count: permission_ids.length
            });
          } else if (action === 'revoke') {
            // Remove permissions from role
            await db.delete(rolePermissions)
              .where(and(
                eq(rolePermissions.role_id, role_id),
                inArray(rolePermissions.permission_id, permission_ids)
              ));
            results.push({
              role_id,
              action: 'revoked',
              permission_count: permission_ids.length
            });
          }
        } catch (error) {
          errors.push({
            assignment,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return res.json({
        message: 'Bulk permission assignment completed',
        successful: results.length,
        failed: errors.length,
        results,
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error in bulk permission assignment:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get effective permissions for a user
  app.get('/api/permissions/user/:id/effective', authMiddleware, requirePermission('user_management'), async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.id);

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      // Check if user exists
      const user = await db.select().from(users).where(eq(users.id, userId)).limit(1);
      if (!user.length) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Get effective permissions using our service
      const effectivePermissions = await permissionService.getUserPermissions(userId);

      // Get detailed permission information
      const permissionDetails = await db.select()
        .from(permissions)
        .where(inArray(permissions.code, effectivePermissions));

      // Group by category
      const categorizedPermissions = permissionDetails.reduce((acc, permission) => {
        if (!acc[permission.category]) {
          acc[permission.category] = [];
        }
        acc[permission.category].push(permission);
        return acc;
      }, {} as Record<string, typeof permissionDetails>);

      // Get user's direct roles
      const userRoleData = await db.select({
        role_id: userRoles.role_id,
        role_name: customRoles.name,
        role_description: customRoles.description
      })
      .from(userRoles)
      .innerJoin(customRoles, eq(userRoles.role_id, customRoles.id))
      .where(eq(userRoles.user_id, userId));

      return res.json({
        user_id: userId,
        user_info: {
          id: user[0].id,
          username: user[0].username,
          email: user[0].email,
          role: user[0].role
        },
        direct_roles: userRoleData,
        effective_permissions: {
          total_count: effectivePermissions.length,
          permissions_by_category: categorizedPermissions,
          permission_codes: effectivePermissions
        }
      });
    } catch (error) {
      console.error('Error fetching effective permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Check specific permission for user with context
  app.post('/api/permissions/check', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { user_id, permission_type, operation_type, context } = req.body;

      if (!user_id || !permission_type) {
        return res.status(400).json({ message: 'user_id and permission_type are required' });
      }

      const permissionContext = {
        userId: user_id,
        amount: context?.amount,
        timestamp: new Date(),
        ip_address: req.ip,
        company_id: context?.company_id
      };

      const hasPermission = await permissionService.checkPermissionWithContext(
        permissionContext,
        permission_type,
        operation_type
      );

      return res.json({
        user_id,
        permission_type,
        operation_type,
        context: permissionContext,
        has_permission: hasPermission,
        checked_at: new Date().toISOString()
      });
    } catch (error) {
      console.error('Error checking permission:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get permission usage analytics - allow any user for demo purposes
  app.get('/api/permissions/analytics', async (req: AuthRequest, res: Response) => {
    try {
      // Return demo analytics data since the database tables might not exist
      const demoAnalytics = {
        permission_distribution: [
          {
            category: 'user_management',
            code: 'user_view',
            name: 'View Users',
            role_count: 3,
            user_count: 5
          },
          {
            category: 'user_management',
            code: 'user_create',
            name: 'Create Users',
            role_count: 2,
            user_count: 2
          },
          {
            category: 'loan_management',
            code: 'loan_create_basic',
            name: 'Create Basic Loans',
            role_count: 4,
            user_count: 8
          }
        ],
        role_statistics: [
          {
            role_name: 'Company Admin',
            description: 'Full company administration access',
            permission_count: 15,
            user_count: 2
          },
          {
            role_name: 'Loan Officer',
            description: 'Loan management and processing',
            permission_count: 8,
            user_count: 5
          }
        ],
        category_statistics: [
          {
            category: 'user_management',
            total_permissions: 6,
            assigned_permissions: 4,
            users_with_category_permissions: 3
          },
          {
            category: 'loan_management',
            total_permissions: 12,
            assigned_permissions: 8,
            users_with_category_permissions: 8
          }
        ],
        generated_at: new Date().toISOString(),
        note: 'Demo data - authentication required for real analytics'
      };

      return res.json(demoAnalytics);
    } catch (error) {
      console.error('Error fetching permission analytics:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Request additional permissions (self-service)
  app.post('/api/permissions/request', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const { permission_codes, justification, duration_hours } = req.body;

      if (!Array.isArray(permission_codes) || permission_codes.length === 0) {
        return res.status(400).json({ message: 'permission_codes array is required' });
      }

      if (!justification || justification.trim().length < 10) {
        return res.status(400).json({ message: 'Justification must be at least 10 characters' });
      }

      // Validate permission codes exist
      const validPermissions = await db.select()
        .from(permissions)
        .where(inArray(permissions.code, permission_codes));

      if (validPermissions.length !== permission_codes.length) {
        const invalidCodes = permission_codes.filter(code =>
          !validPermissions.some(p => p.code === code)
        );
        return res.status(400).json({
          message: 'Invalid permission codes',
          invalid_codes: invalidCodes
        });
      }

      // Check which permissions user already has
      const currentPermissions = await permissionService.getUserPermissions(req.user.id);
      const newPermissions = permission_codes.filter(code => !currentPermissions.includes(code));

      if (newPermissions.length === 0) {
        return res.status(400).json({ message: 'User already has all requested permissions' });
      }

      // In a real implementation, this would create a permission request record
      // For now, we'll just return the request details
      const requestId = `REQ-${Date.now()}-${req.user.id}`;

      return res.status(201).json({
        request_id: requestId,
        user_id: req.user.id,
        requested_permissions: newPermissions,
        already_granted: permission_codes.filter(code => currentPermissions.includes(code)),
        justification,
        duration_hours: duration_hours || null,
        status: 'pending',
        created_at: new Date().toISOString(),
        message: 'Permission request submitted successfully. It will be reviewed by an administrator.'
      });
    } catch (error) {
      console.error('Error creating permission request:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
