# Customer Reference Code Implementation

This document provides step-by-step instructions for implementing company-specific customer reference codes in the FinancialTracker system, based on our actual implementation process.

## Overview

Customer reference codes are unique identifiers for customers within a company, following a similar pattern to collection IDs, agent reference codes, and loan reference codes. The format is:

```
[COMPANY_PREFIX]-[SEQUENTIAL_NUMBER]
```

For example:
- "GS-001" for the first customer in "GOVINDARAJI S" company
- "CS-001" for the first customer in "Cloud Stier" company

Our implementation followed these steps:
1. Adding a new column to the database through a migration
2. Setting a default empty value for existing records
3. Referencing the agent-reference.md file for the company prefix generation logic
4. Implementing the sequential number generation
5. Updating the customer creation and update processes to use the company-specific reference codes

## Implementation Details

### 1. Database Schema Changes

#### Step 1: Create SQL Migration
We created a migration file `migrations/007_add_customer_reference_code.sql` with the following SQL:

```sql
-- Migration to add customer_reference_code column to customers table
-- This column will store company-specific customer identifiers

-- Add customer_reference_code column to customers table
ALTER TABLE "customers"
  ADD COLUMN IF NOT EXISTS "customer_reference_code" TEXT;

-- Create an index for faster lookups by customer_reference_code
CREATE INDEX IF NOT EXISTS idx_customers_reference_code ON customers(customer_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN customers.customer_reference_code IS 'Company-specific customer identifier string (e.g., GS-001) that is unique within each company';
```

#### Step 2: Create Migration Runner Script
We created a script `run-customer-reference-migration.js` to run the migration:

```javascript
// Script to run the customer reference code migration
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function runMigration() {
  console.log('Starting customer reference code migration...');

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', '007_add_customer_reference_code.sql');
    console.log(`Reading migration file from: ${migrationPath}`);

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    console.log('Executing migration...');
    await pool.query(migrationSQL);

    // Verify the migration
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'customers' AND column_name = 'customer_reference_code'
    `);

    if (columnCheck.rows.length > 0) {
      console.log('✅ Migration verified successfully!');
      console.log('✅ customer_reference_code column added to customers table');
    } else {
      console.log('❌ Migration verification failed: customer_reference_code column not found');
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the migration
runMigration();
```

#### Step 3: Update Schema Definition
We updated the customers table definition in `shared/schema.ts`:

```typescript
// Customers
export const customers = pgTable('customers', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  full_name: text('full_name').notNull(),
  email: text('email'),
  phone: text('phone').notNull(),
  address: text('address'),
  profile_image: text('profile_image'),
  credit_score: integer('credit_score'),
  kyc_verified: boolean('kyc_verified').default(false).notNull(),
  notes: text('notes'),
  customer_reference_code: text('customer_reference_code'),
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

#### Step 4: Update Migration Runner
We updated the `run-neon-migrations.js` file to include our new migration:

```javascript
// Define migrations to run in order
const migrations = [
  // ... existing migrations ...
  {
    name: '007_add_customer_reference_code',
    path: path.join(__dirname, 'migrations', '007_add_customer_reference_code.sql'),
    verification: async (pool) => {
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'customers' AND column_name = 'customer_reference_code'
      `);
      return result.rows.length > 0;
    }
  }
];
```

### 2. Setting Default Value for Existing Records

After adding the column, we needed to set a default empty value for all existing customer records. We created a script to update all existing customers:

#### Step 5: Create Script to Update Existing Records
We created a script `update-customer-reference-codes.js` to update all existing customers with an empty value:

```javascript
// Script to update all existing customers to have an empty value in the customer_reference_code column
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function updateCustomerReferenceCodes() {
  console.log('Starting update of customer_reference_code values...');

  try {
    // First, check if there are any customers in the database
    const countResult = await pool.query(`
      SELECT COUNT(*) FROM customers
    `);

    const customerCount = parseInt(countResult.rows[0].count);
    console.log(`Found ${customerCount} customers in the database`);

    if (customerCount === 0) {
      console.log('No customers to update. Exiting.');
      return;
    }

    // Update all customers to have an empty value in the customer_reference_code column
    const updateResult = await pool.query(`
      UPDATE customers
      SET customer_reference_code = ''
      WHERE customer_reference_code IS NULL
      RETURNING id
    `);

    const updatedCount = updateResult.rows.length;
    console.log(`Updated ${updatedCount} customers with empty customer_reference_code`);

    // Verify the update
    const verifyResult = await pool.query(`
      SELECT COUNT(*) FROM customers WHERE customer_reference_code = ''
    `);

    const verifiedCount = parseInt(verifyResult.rows[0].count);
    console.log(`Verified ${verifiedCount} customers now have empty customer_reference_code`);

    console.log('Update completed successfully!');
  } catch (error) {
    console.error('Error updating customer reference codes:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the update
updateCustomerReferenceCodes();
```

## Next Steps

To complete the implementation, the following steps should be taken:

1. Add a `getHighestCustomerSerial` method to the `CustomerStorage` class
2. Update the customer creation endpoint to generate and use company-specific prefixes
3. Update the customer update endpoint to maintain the reference code format
4. Add an API endpoint for updating customer reference codes programmatically

These steps will follow the same pattern as the loan reference code implementation, using the existing `getCompanyName` function for prefix generation and implementing sequential numbering within each company.

## Testing

To test the implementation:

1. **Create a new customer**:
   - It should automatically get a company-specific reference code (e.g., "GS-001")
   - Creating another customer for the same company should increment the serial (e.g., "GS-002")
   - Creating a customer for a different company should start with 001 (e.g., "CS-001")

2. **Update an existing customer**:
   - If it doesn't have a reference code, it should get a company-specific one
   - If it already has a reference code, it should keep it

3. **Use the update-reference-codes endpoint**:
   - It should update all customers without reference codes for a specific company
   - The reference codes should follow the company-specific format and sequential numbering
