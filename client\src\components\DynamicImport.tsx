import { lazy, Suspense, ComponentType } from 'react';

interface DynamicImportProps {
  path: string;
}

// Handle specific dynamic paths since Vite requires static parts in dynamic imports
const getComponentForPath = (path: string): ComponentType => {
  // Map paths to specific lazy components
  switch (path) {
    // Customer routes
    case '/customers/[id]':
    case 'customers/[id]':
      return lazy(() => import('@/pages/customers/[id]'));

    case '/customers/[id]/edit':
    case 'customers/[id]/edit':
      return lazy(() => import('@/pages/customers/[id]/edit'));

    case '/customers/[id]/loans':
    case 'customers/[id]/loans':
      return lazy(() => import('@/pages/customers/[id]/loans'));

    // Financial accounts routes
    case '/financial/accounts/index':
    case 'financial/accounts/index':
      return lazy(() => import('@/pages/financial/accounts/index'));

    case '/financial/accounts/create':
    case 'financial/accounts/create':
      return lazy(() => import('@/pages/financial/accounts/create'));

    case '/financial/accounts/[id]':
    case 'financial/accounts/[id]':
      return lazy(() => import('@/pages/financial/accounts/[id]'));

    case '/financial/accounts/[id]/edit':
    case 'financial/accounts/[id]/edit':
      return lazy(() => import('@/pages/financial/accounts/[id]/edit'));

    // Financial transactions routes
    case '/financial/transactions/index':
    case 'financial/transactions/index':
      return lazy(() => import('@/pages/financial/transactions/index'));

    case '/financial/transactions/create':
    case 'financial/transactions/create':
      return lazy(() => import('@/pages/financial/transactions/create'));

    case '/financial/transactions/[id]':
    case 'financial/transactions/[id]':
      return lazy(() => import('@/pages/financial/transactions/[id]'));

    case '/financial/transactions/[id]/edit':
    case 'financial/transactions/[id]/edit':
      return lazy(() => import('@/pages/financial/transactions/[id]/edit'));

    // Financial expenses routes
    case '/financial/expenses/index':
    case 'financial/expenses/index':
      return lazy(() => import('@/pages/financial/expenses/index'));

    // Financial reports routes
    case '/financial/reports/index':
    case 'financial/reports/index':
      return lazy(() => import('@/pages/financial/reports/index'));

    // Test routes
    case '/test/trial-balance':
    case 'test/trial-balance':
      return lazy(() => import('@/pages/test/trial-balance'));

    // User management routes
    case '/user-management/permissions/index':
    case 'user-management/permissions/index':
      return lazy(() => import('@/pages/user-management/permissions/index'));

    case '/user-management/permissions/user-permissions':
    case 'user-management/permissions/user-permissions':
      return lazy(() => import('@/pages/user-management/permissions/user-permissions'));

    case '/user-management/roles/[id]':
    case 'user-management/roles/[id]':
      return lazy(() => import('@/pages/user-management/roles/[id]'));

    case '/user-management/groups/[id]':
    case 'user-management/groups/[id]':
      return lazy(() => import('@/pages/user-management/groups/[id]'));

    default:
      console.error(`No component mapping for path: ${path}`);
      return () => <div className="p-4 text-center">Component not found</div>;
  }
};

// This component loads specific components for param-based directories
export function DynamicImport({ path }: DynamicImportProps) {
  // Add a unique ID to track instances of this component
  const instanceId = Math.random().toString(36).substring(2, 9);
  console.log(`[DynamicImport ${instanceId}] Loading component for path: ${path}`);

  const Component = getComponentForPath(path);

  return (
    <Suspense fallback={<div className="p-4 text-center">Loading...</div>}>
      <Component />
    </Suspense>
  );
}

export default DynamicImport;