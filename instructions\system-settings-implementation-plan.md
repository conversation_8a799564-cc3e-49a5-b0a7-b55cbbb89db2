# System Settings Implementation Plan

## Overview
This document outlines the implementation plan for adding country code prefix to branch creation forms and creating a system settings page for configuration options like date format and currency symbol.

## 1. Add Country Code +91 Prefix in Branch Creation Form

### Current State:
- The branch creation form (`BranchManager.tsx`) currently has a simple input field for phone numbers without a country code prefix.
- Other forms in the application (like `DirectCustomerForm.tsx`) already implement a country code prefix using a combined input approach.

### Implementation Plan:
1. **Modify the Branch Form Component**:
   - Update the phone input field in `BranchManager.tsx` to include a prefix span with "+91"
   - Implement validation to ensure proper formatting of phone numbers
   - Handle the phone number formatting in form submission

2. **Update Schema Validation**:
   - Modify the `branchFormSchema` to validate phone numbers with the country code
   - Ensure backward compatibility with existing data

3. **Update API Handling**:
   - Ensure the API correctly processes phone numbers with the country code prefix

## 2. Create System Settings Page for Configuration

### Current State:
- The application has a `settings.tsx` page with tabs for Profile, Security, Notifications, Company, and Branches.
- There's no dedicated section for system-wide settings like date format or currency format.
- Date and currency formatting utilities exist in `utils.ts` and `format-utils.ts` but use hardcoded values.

### Implementation Plan:
1. **Create a New "System Settings" Tab**:
   - Add a new tab to the existing Settings page for system-wide configurations
   - Make this tab visible only to users with appropriate permissions (company_admin or saas_admin)

2. **Implement Date Format Settings**:
   - Create a form section for configuring date format preferences
   - Provide options like "dd-MM-yyyy", "MM-dd-yyyy", "yyyy-MM-dd", etc.
   - Store the selected format in the database linked to the company

3. **Implement Currency Format Settings**:
   - Create a form section for configuring currency symbol preferences
   - Default to "₹" for Indian Rupee
   - Allow customization for different currency symbols
   - Store the selected currency symbol in the database linked to the company

4. **Create Backend API Endpoints**:
   - Implement API endpoints to save and retrieve system settings
   - Create a new database table for storing company-specific settings

5. **Update Formatting Utilities**:
   - Modify existing formatting utilities to use the configured settings
   - Create a context provider to make settings available throughout the application
   - Ensure backward compatibility with existing code

6. **Add Future Extensibility**:
   - Design the settings page to be easily extensible for future settings like SMS API and Email settings
   - Implement a modular approach to add new setting categories

## Detailed Technical Implementation

### 1. Database Schema Updates:
```sql
CREATE TABLE company_settings (
  id SERIAL PRIMARY KEY,
  company_id INTEGER NOT NULL REFERENCES companies(id) ON DELETE CASCADE,
  date_format VARCHAR(20) NOT NULL DEFAULT 'dd-MM-yyyy',
  currency_symbol VARCHAR(5) NOT NULL DEFAULT '₹',
  created_at TIMESTAMP NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMP NOT NULL DEFAULT NOW(),
  UNIQUE(company_id)
);
```

### 2. API Endpoints:
- `GET /api/companies/:id/settings` - Retrieve company settings
- `PUT /api/companies/:id/settings` - Update company settings

### 3. Frontend Components:
- Create a new `SystemSettings.tsx` component for the settings tab
- Implement form controls for date and currency format settings
- Add validation and error handling

### 4. Context Provider:
- Create a `SettingsProvider` to make settings available throughout the app
- Update existing formatting utilities to use the context

### 5. Phone Number Implementation:
- Update the branch form to include the country code prefix
- Implement proper validation and formatting

## Timeline Estimate:
1. **Country Code Prefix**: 1-2 hours
2. **Database Schema & API**: 2-3 hours
3. **Settings UI Implementation**: 3-4 hours
4. **Context Provider & Utilities**: 2-3 hours
5. **Testing & Debugging**: 2-3 hours

**Total Estimated Time**: 10-15 hours

## Considerations:
- Backward compatibility with existing data
- Performance impact of fetching settings for each format operation
- User permissions and access control
- Future extensibility for additional settings
