/**
 * Client-side logging utility for TrackFina application
 * Provides consistent logging across the application with severity levels and context
 */

type LogLevel = 'info' | 'warn' | 'error' | 'debug';
type LogContext = string;

interface LogOptions {
  context?: LogContext;
  data?: any;
}

class Logger {
  private isDevMode: boolean;
  
  constructor() {
    this.isDevMode = import.meta.env.DEV || false;
  }

  /**
   * Log informational messages
   */
  info(message: string, options?: LogOptions): void {
    this.log('info', message, options);
  }

  /**
   * Log warning messages
   */
  warn(message: string, options?: LogOptions): void {
    this.log('warn', message, options);
  }

  /**
   * Log error messages
   */
  error(message: string, error?: Error, options?: LogOptions): void {
    const combinedOptions = {
      ...options,
      data: {
        ...(options?.data || {}),
        error: error ? {
          message: error.message,
          stack: error.stack,
          name: error.name
        } : undefined
      }
    };
    
    this.log('error', message, combinedOptions);
  }

  /**
   * Log debug messages (only in development)
   */
  debug(message: string, options?: LogOptions): void {
    if (this.isDevMode) {
      this.log('debug', message, options);
    }
  }

  /**
   * Internal logging method
   */
  private log(level: LogLevel, message: string, options?: LogOptions): void {
    const context = options?.context ? `[${options.context}]` : '';
    const timestamp = new Date().toISOString();
    const prefix = `${timestamp} ${level.toUpperCase()} ${context}`;
    
    switch (level) {
      case 'info':
        console.info(`${prefix} ${message}`, options?.data || '');
        break;
      case 'warn':
        console.warn(`${prefix} ${message}`, options?.data || '');
        break;
      case 'error':
        console.error(`${prefix} ${message}`, options?.data || '');
        break;
      case 'debug':
        console.debug(`${prefix} ${message}`, options?.data || '');
        break;
    }
  }
}

// Create singleton instance
const logger = new Logger();
export default logger;