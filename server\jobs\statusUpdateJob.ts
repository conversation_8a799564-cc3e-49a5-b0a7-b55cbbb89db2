/**
 * Status Update Job
 * 
 * This job runs daily to update collection statuses based on dates.
 * It uses the CollectionStatusService to:
 * - Update "Pending" collections to "Due" when the scheduled date arrives
 * - Update "Due" collections to "Overdue" when they are past due
 */

import { collectionStatusService } from '../services/collectionStatusService';
import errorLogger from '../utils/errorLogger';

export async function runStatusUpdateJob(): Promise<void> {
  try {
    console.log('Starting collection status update job...');
    
    const result = await collectionStatusService.updateCollectionStatuses();
    
    console.log(`Collection status update job completed: ${result.updated} collections updated, ${result.errors} errors`);
  } catch (error) {
    errorLogger.logError('Error running status update job', 'status-update-job', error as Error);
    console.error('Status update job failed:', error);
  }
}

// If this file is run directly (e.g., via node jobs/statusUpdateJob.js)
if (require.main === module) {
  runStatusUpdateJob()
    .then(() => {
      console.log('Status update job completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Status update job failed:', error);
      process.exit(1);
    });
}
