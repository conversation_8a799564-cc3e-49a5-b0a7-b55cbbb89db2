import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { formatCurrency } from "@/lib/utils";

// Define type for agent data
interface AgentData {
  agent?: {
    id?: number;
    user?: {
      id?: number;
      full_name?: string;
    };
  };
  collectionsAmount: number;
  collectionsCount: number;
  successRate?: number;
  commission: number;
}

interface TopAgentsProps {
  agents: AgentData[];
  isLoading: boolean;
  onViewAll: () => void;
}

export default function TopAgents({
  agents,
  isLoading,
  onViewAll,
}: TopAgentsProps) {
  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  const renderSkeletonRows = () => {
    return Array(5)
      .fill(0)
      .map((_, index) => (
        <TableRow key={`skeleton-${index}`}>
          <TableCell>
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="ml-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16 mt-1" />
              </div>
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-16 mt-1" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-12" />
            <Skeleton className="h-2.5 w-full mt-1" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
        </TableRow>
      ));
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between border-b border-border">
        <div>
          <CardTitle>Top Performing Agents</CardTitle>
          <CardDescription>Based on collection amounts this month</CardDescription>
        </div>
        <Button
          variant="link"
          onClick={onViewAll}
          className="text-primary font-medium"
        >
          View All Agents
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Agent</TableHead>
                <TableHead>Collections</TableHead>
                <TableHead>Success Rate</TableHead>
                <TableHead>Commission</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading
                ? renderSkeletonRows()
                : agents.map((agentData) => (
                    <TableRow key={agentData.agent?.id || `agent-${Math.random()}`}>
                      <TableCell>
                        <div className="flex items-center">                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-accent-200 text-accent-800">
                              {agentData.agent?.user?.full_name ? getInitials(agentData.agent.user.full_name) : 'AG'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {agentData.agent?.user?.full_name || `Agent ${agentData.agent?.id || 'Unknown'}`}
                            </div>                            <div className="text-sm text-gray-500">
                              AGT-{agentData.agent?.id || 'Unknown'}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium text-gray-900 font-mono">
                          {formatCurrency(agentData.collectionsAmount, 'INR', 'en-IN')}
                        </div>
                        <div className="text-sm text-gray-500">
                          {agentData.collectionsCount} collections
                        </div>
                      </TableCell>
                      <TableCell>                        <div className="text-sm text-gray-900">
                          {agentData.successRate !== undefined ? agentData.successRate.toFixed(1) : '0.0'}%
                        </div>
                        <Progress
                          value={agentData.successRate ?? 0}
                          className="h-2.5 mt-1"
                          indicatorClassName="bg-green-600"
                        />
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium text-gray-900 font-mono">
                          {formatCurrency(agentData.commission, 'INR', 'en-IN')}
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
