# Transaction Workflow and Company Data Isolation Documentation

## Overview

This document explains how the transaction workflow ensures complete company data isolation in the FinancialTracker system, following the same pattern as the loan workflow. When a user logs in with Company ID 1, they can only access transactions belonging to Company ID 1, ensuring multi-tenant security.

## Architecture Overview

```
User Authentication → Company Context → API Protection → Database Filtering → Response
```

## 1. Authentication & Company Context

### User Token Structure
When a user logs in, their JWT token contains:
```typescript
{
  userId: number,
  role: string,
  company_id: number  // Critical for company isolation
}
```

### Auth Middleware Implementation
**File:** `server/middleware/auth.ts`

```typescript
// Sets user context from JWT token
req.user = {
  id: user.id,
  role: user.role,
  company_id: user.company_id  // Company context from token
};
```

### Frontend Company Context
**File:** `client/src/pages/financial/transactions/index.tsx`

```typescript
const { companyId } = useContextData(); // Gets company ID from context
```

## 2. API Route Protection

### Company Access Middleware
**File:** `server/middleware/auth.ts`

The `requireCompanyAccess` middleware ensures users can only access their company's resources:

```typescript
export function requireCompanyAccess(req: AuthRequest, res: Response, next: NextFunction) {
  const companyId = parseInt(req.params.companyId || req.body.company_id, 10);

  // SaaS admin has access to all companies
  if (req.user.role === 'saas_admin') {
    return next();
  }

  // Check if user's token company_id matches requested company
  if (req.user.company_id === companyId) {
    return next();
  }

  // Additional check for multi-company users
  storage.getUserCompanies(req.user.id)
    .then(userCompanies => {
      const hasAccess = userCompanies.some(uc => uc.company_id === companyId);
      if (hasAccess) {
        return next();
      } else {
        return res.status(403).json({ message: 'Access denied to this company' });
      }
    });
}
```

### Transaction Routes Protection
**File:** `server/routes.ts`

```typescript
// Get all transactions for a company - Protected route
app.get('/api/companies/:companyId/transactions',
  authMiddleware,           // Validates JWT token
  requireCompanyAccess,     // Validates company access
  async (req: AuthRequest, res: Response) => {
    const companyId = parseInt(req.params.companyId);
    const result = await storage.getTransactionsByCompany(companyId, options);
    return res.json(result);
  }
);
```

## 3. Database Layer Filtering

### Transaction Storage Implementation
**File:** `server/financialManagement.ts`

```typescript
export async function getTransactionsByCompany(
  companyId: number,
  options?: {
    startDate?: string;
    endDate?: string;
    page?: number;
    limit?: number;
    accountType?: string;
    transactionType?: string;
    referenceType?: string;
    searchTerm?: string;
  }
): Promise<{ transactions: Transaction[], totalCount: number }> {
  try {
    // Build the WHERE conditions
    const whereConditions = [eq(transactions.company_id, companyId)]; // ✅ COMPANY FILTERING

    // Build main query with JOIN to accounts table
    let query = db
      .select({
        transaction: transactions,
        account: {
          id: accounts.id,
          account_code: accounts.account_code,
          account_name: accounts.account_name,
          account_type: accounts.account_type
        }
      })
      .from(transactions)
      .leftJoin(accounts, eq(transactions.account_id, accounts.id))
      .where(and(...whereConditions))
      .orderBy(desc(transactions.transaction_date))
      .limit(limit)
      .offset(offset);

    const results = await query;

    // Transform the results to include account information
    const transformedTransactions = results.map(row => ({
      ...row.transaction,
      account: row.account.id ? row.account : undefined
    }));

    return {
      transactions: transformedTransactions,
      totalCount
    };
  } catch (error) {
    errorLogger.logError(`Failed to get transactions for company ${companyId}`, 'transactions-fetch', error);
    return { transactions: [], totalCount: 0 };
  }
}
```

### Database Schema
**File:** `shared/schema.ts`

```typescript
export const transactions = pgTable('transactions', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id')
    .references(() => companies.id, { onDelete: 'cascade' })
    .notNull(),  // ✅ FOREIGN KEY ENSURES COMPANY ASSOCIATION
  account_id: integer('account_id')
    .references(() => accounts.id, { onDelete: 'cascade' })
    .notNull(),
  // ... other fields
});
```

## 4. Frontend Implementation

### API Query Implementation
**File:** `client/src/pages/financial/transactions/index.tsx`

```typescript
// Fetch transactions for specific company with enhanced filtering
const { data, isLoading, isError } = useQuery<PaginatedResponse>({
  queryKey: [
    '/api/companies', companyId, 'transactions',
    currentPage, itemsPerPage, dateRange,
    selectedAccountType, selectedTransactionType, selectedReferenceType, searchTerm
  ],
  queryFn: async () => {
    let url = `/api/companies/${companyId}/transactions`;  // Company-specific endpoint
    const params = new URLSearchParams();

    // Add pagination and filtering parameters
    params.append('page', currentPage.toString());
    params.append('limit', itemsPerPage.toString());
    // ... other filters

    const response = await apiRequest('GET', url + '?' + params.toString());
    return await response.json();
  },
  enabled: !!companyId,
});
```

### Individual Transaction Access
**File:** `server/routes.ts`

```typescript
// Get individual transaction with company context
app.get('/api/companies/:companyId/transactions/:id',
  authMiddleware, requireCompanyAccess,
  async (req: AuthRequest, res: Response) => {
    const companyId = parseInt(req.params.companyId);
    const transactionId = parseInt(req.params.id);

    const transaction = await storage.getTransaction(transactionId, companyId);

    if (!transaction) {
      return res.status(404).json({ message: 'Transaction not found' });
    }

    // Enrich with account information
    if (transaction.account_id) {
      const account = await storage.getAccount(transaction.account_id, companyId);
      if (account) {
        transaction.account = {
          id: account.id,
          account_code: account.account_code,
          account_name: account.account_name,
          account_type: account.account_type
        };
      }
    }

    return res.json(transaction);
  }
);
```

## 5. Security Layers

### Layer 1: Authentication
- JWT token validation
- User existence verification
- Company context extraction

### Layer 2: Authorization
- Company access validation
- Role-based permissions
- Multi-company user support

### Layer 3: Database Filtering
- SQL-level company filtering
- Foreign key constraints
- Cascade delete protection

### Layer 4: Individual Resource Protection
**File:** `server/routes.ts`

```typescript
// Additional security for individual transaction access
if (req.user!.role !== 'saas_admin' && transaction.company_id !== companyId) {
  const userCompanies = await storage.getUserCompanies(userId);
  const hasAccess = userCompanies.some(uc => uc.company_id === transaction.company_id);

  if (!hasAccess) {
    return res.status(403).json({ message: 'Access denied to this transaction' });
  }
}
```

## 6. Complete Data Flow

### Transaction List Workflow
```
1. User Login (Company ID 1)
   ↓
2. JWT Token: { userId: X, company_id: 1, role: 'user' }
   ↓
3. Frontend: GET /api/companies/1/transactions?page=1&limit=10
   ↓
4. authMiddleware: Validates token, sets req.user.company_id = 1
   ↓
5. requireCompanyAccess: Validates user can access company 1
   ↓
6. Database Query: SELECT * FROM transactions
   LEFT JOIN accounts ON transactions.account_id = accounts.id
   WHERE transactions.company_id = 1
   ↓
7. Response: Only transactions belonging to Company 1 with account info
```

### Individual Transaction Access Workflow
```
1. Frontend: GET /api/companies/1/transactions/123
   ↓
2. authMiddleware: Validates token
   ↓
3. requireCompanyAccess: Validates company access
   ↓
4. Database: SELECT * FROM transactions WHERE id = 123 AND company_id = 1
   ↓
5. Response: Transaction data (if authorized) or 403 Forbidden
```

## 7. Error Handling

### Company Access Denied
```json
{
  "message": "Access denied to this company",
  "status": 403
}
```

### Transaction Access Denied
```json
{
  "message": "Access denied to this transaction",
  "status": 403
}
```

### Invalid Company ID
```json
{
  "message": "Company ID is required",
  "status": 400
}
```

## 8. Testing Company Isolation

### Test Scenarios
1. **User A (Company 1)** should only see transactions where `company_id = 1`
2. **User B (Company 2)** should only see transactions where `company_id = 2`
3. **Cross-company access** should return 403 Forbidden
4. **SaaS Admin** should see all transactions (with proper company context)

### Verification Points
- Database queries include `WHERE company_id = ?`
- API responses contain only company-specific data
- Frontend displays correct company context
- Error messages for unauthorized access
- Account information is properly joined and displayed

## 9. Enhanced Features (vs Basic Loan Pattern)

### Pagination Support
- Server-side pagination with page and limit parameters
- Total count and total pages calculation
- Frontend pagination controls

### Advanced Filtering
- Date range filtering (startDate, endDate)
- Account type filtering (asset, liability, equity, income, expense)
- Transaction type filtering (debit, credit)
- Reference type filtering (loan, collection, expense, etc.)
- Search functionality across descriptions

### JOIN Operations
- Transactions are joined with accounts table
- Account information (code, name, type) included in response
- Similar to how loans are joined with customers

## 10. Related Components

### Similar Isolation Patterns
- **Loans:** `GET /api/companies/:companyId/loans`
- **Customers:** `GET /api/companies/:companyId/customers`
- **Collections:** `GET /api/companies/:companyId/collections`
- **Accounts:** `GET /api/companies/:companyId/accounts`

### Middleware Dependencies
- `authMiddleware`: JWT validation
- `requireCompanyAccess`: Company authorization
- `requirePrefixSettings`: Company configuration validation

## 11. Best Practices

### Security Guidelines
1. Always use company-specific API endpoints
2. Validate company access at middleware level
3. Filter database queries by company_id
4. Double-check individual resource access
5. Use foreign key constraints for data integrity

### Performance Considerations
1. Index `company_id` columns for fast filtering
2. Use JOIN queries to reduce database calls
3. Implement proper caching strategies
4. Monitor query performance for large datasets
5. Use server-side pagination for large result sets

## Conclusion

The transaction workflow implements comprehensive company data isolation through multiple security layers, following the exact same pattern as the loan workflow:
- **Authentication** ensures valid users
- **Authorization** validates company access
- **Database filtering** enforces data separation
- **Individual checks** provide additional security
- **Enhanced features** provide better user experience

This multi-layered approach ensures that Company ID 1 users can only access Company ID 1 transaction data, maintaining strict tenant isolation in the multi-tenant system while providing advanced filtering and pagination capabilities.
