import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useLocation } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useContextData } from '@/lib/useContextData';
import { useToast } from '@/hooks/use-toast';
import { format } from 'date-fns';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import * as z from 'zod';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
  CardFooter,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Sheet,
  SheetContent,
  SheetDescription,
  SheetFooter,
  SheetHeader,
  SheetTitle,
  SheetTrigger,
} from '@/components/ui/sheet';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Badge } from '@/components/ui/badge';
import { Spinner } from '@/components/ui/spinner';
import { Separator } from '@/components/ui/separator';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

// Icons
import {
  Plus,
  Search,
  CalendarIcon,
  Filter,
  MoreHorizontal,
  Edit,
  Trash2,
  AlertTriangle,
  ArrowUpDown,
} from 'lucide-react';

// Expense type interface based on the schema
interface Expense {
  id: number;
  company_id: number;
  branch_id: number | null;
  amount: number;
  description: string;
  expense_date: string;
  expense_type: 'rent' | 'salary' | 'utilities' | 'office_supplies' | 'marketing' | 'transport' | 'loan_disbursement' | 'other';
  created_by: number | null;
  reference_number: string | null;
  payment_method: 'cash' | 'upi' | 'bank_transfer' | 'cheque' | 'card_payment' | 'online_transfer' | 'mobile_wallet';
  notes: string | null;
  created_at: string;
  updated_at: string;
  // Optional joined fields
  branch?: {
    id: number;
    name: string;
  };
  createdBy?: {
    id: number;
    full_name: string;
    email: string;
  };
}

interface Branch {
  id: number;
  name: string;
  company_id: number;
}

// Form validation schema for creating/editing expenses
const expenseSchema = z.object({
  amount: z.coerce.number().positive({ message: 'Amount must be greater than zero' }),
  description: z.string().min(1, { message: 'Description is required' }),
  expense_date: z.date({ required_error: 'Expense date is required' }),
  expense_type: z.enum(['rent', 'salary', 'utilities', 'office_supplies', 'marketing', 'transport', 'loan_disbursement', 'other'], {
    required_error: 'Expense type is required',
  }),
  branch_id: z.coerce.number().optional().nullable(),
  reference_number: z.string().optional().nullable(),
  payment_method: z.enum(['cash', 'upi', 'bank_transfer', 'cheque', 'card_payment', 'online_transfer', 'mobile_wallet'], {
    required_error: 'Payment method is required',
  }),
  notes: z.string().optional().nullable(),
  // Don't include company_id as it's added by the server
});

type ExpenseFormValues = z.infer<typeof expenseSchema>;

export default function ExpensesList() {
  const [, navigate] = useLocation();
  const { companyId } = useContextData();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // State for filters, sheet and dialog
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState<{
    from: Date | undefined;
    to: Date | undefined;
  }>({ from: undefined, to: undefined });
  const [selectedType, setSelectedType] = useState<string>('all');
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<string>('all');
  const [createSheetOpen, setCreateSheetOpen] = useState(false);
  const [editSheetOpen, setEditSheetOpen] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [expenseToDelete, setExpenseToDelete] = useState<number | null>(null);
  const [expenseToEdit, setExpenseToEdit] = useState<Expense | null>(null);

  // Fetch expenses
  const {
    data: expenses = [],
    isLoading: isLoadingExpenses,
    isError: isExpensesError
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'expenses'],
    queryFn: async () => {
      if (!companyId) return [];

      let url = `/api/companies/${companyId}/expenses`;
      const params = new URLSearchParams();

      if (dateRange.from) {
        params.append('startDate', format(dateRange.from, 'yyyy-MM-dd'));
      }
      if (dateRange.to) {
        params.append('endDate', format(dateRange.to, 'yyyy-MM-dd'));
      }

      const queryParams = params.toString();
      if (queryParams) {
        url += `?${queryParams}`;
      }

      const response = await apiRequest('GET', url);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Fetch branches for dropdown
  const { data: branches = [] } = useQuery<Branch[]>({
    queryKey: ['/api/companies', companyId, 'branches'],
    queryFn: async () => {
      if (!companyId) return [];

      const response = await apiRequest('GET', `/api/companies/${companyId}/branches`);
      return await response.json();
    },
    enabled: !!companyId
  });

  // Setup form for creating expenses
  const form = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      amount: undefined,
      description: '',
      expense_date: new Date(),
      expense_type: undefined,
      branch_id: null,
      reference_number: null,
      payment_method: undefined,
      notes: null,
    },
  });

  // Setup form for editing expenses
  const editForm = useForm<ExpenseFormValues>({
    resolver: zodResolver(expenseSchema),
    defaultValues: {
      amount: undefined,
      description: '',
      expense_date: new Date(),
      expense_type: undefined,
      branch_id: null,
      reference_number: null,
      payment_method: undefined,
      notes: null,
    },
  });

  // Create expense mutation
  const createMutation = useMutation({
    mutationFn: async (data: ExpenseFormValues) => {
      if (!companyId) {
        throw new Error('Company ID is missing');
      }

      // Format date to string properly for API consumption
      // Using ISO format which will be properly parsed by the server
      const formattedData = {
        ...data,
        expense_date: data.expense_date instanceof Date ? data.expense_date.toISOString() : data.expense_date,
      };

      console.log('Submitting expense data:', formattedData);

      const response = await apiRequest(
        'POST',
        `/api/companies/${companyId}/expenses`,
        formattedData
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create expense');
      }

      return await response.json();
    },
    onSuccess: () => {
      // Invalidate the expenses query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'expenses'] });

      // Show success toast
      toast({
        title: "Expense created",
        description: "The expense has been successfully created.",
        variant: "default",
      });

      // Close the sheet and reset form
      setCreateSheetOpen(false);
      form.reset();
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to create expense",
        variant: "destructive",
      });
    },
  });

  // Edit expense mutation
  const editMutation = useMutation({
    mutationFn: async (data: { id: number, data: ExpenseFormValues }) => {
      // Format date to string properly for API consumption
      const formattedData = {
        ...data.data,
        expense_date: data.data.expense_date instanceof Date ? data.data.expense_date.toISOString() : data.data.expense_date,
      };

      console.log('Updating expense data:', formattedData);

      const response = await apiRequest(
        'PATCH',
        `/api/expenses/${data.id}`,
        formattedData
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update expense');
      }

      return await response.json();
    },
    onSuccess: () => {
      // Invalidate the expenses query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'expenses'] });

      // Show success toast
      toast({
        title: "Expense updated",
        description: "The expense has been successfully updated.",
        variant: "default",
      });

      // Close the sheet and reset form
      setEditSheetOpen(false);
      setExpenseToEdit(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update expense",
        variant: "destructive",
      });
    },
  });

  // Delete expense mutation
  const deleteMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest('DELETE', `/api/expenses/${id}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete expense');
      }

      return await response.json();
    },
    onSuccess: () => {
      // Invalidate the expenses query to refresh data
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'expenses'] });

      // Show success toast
      toast({
        title: "Expense deleted",
        description: "The expense has been successfully deleted.",
        variant: "default",
      });

      // Close the dialog
      setDeleteDialogOpen(false);
      setExpenseToDelete(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete expense",
        variant: "destructive",
      });
    },
  });

  // Handle create form submission
  const onSubmit = (values: ExpenseFormValues) => {
    createMutation.mutate(values);
  };

  // Handle edit form submission
  const onEditSubmit = (values: ExpenseFormValues) => {
    if (expenseToEdit) {
      editMutation.mutate({ id: expenseToEdit.id, data: values });
    }
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (expenseToDelete) {
      deleteMutation.mutate(expenseToDelete);
    }
  };

  // Open edit form with expense data
  const openEditForm = (expense: Expense) => {
    setExpenseToEdit(expense);

    // Convert string date to Date object for the form
    const expenseDate = new Date(expense.expense_date);

    // Reset form with expense data
    editForm.reset({
      amount: expense.amount,
      description: expense.description,
      expense_date: expenseDate,
      expense_type: expense.expense_type,
      branch_id: expense.branch_id,
      reference_number: expense.reference_number || '',
      payment_method: expense.payment_method,
      notes: expense.notes || '',
    });

    setEditSheetOpen(true);
  };

  // Filter expenses based on search and filters
  const filteredExpenses = expenses.filter((expense: Expense) => {
    // Search filter
    const searchFields = [
      expense.description,
      expense.reference_number,
      expense.notes,
      expense.expense_type
    ];
    const matchesSearch = searchTerm === '' || searchFields.some(field =>
      field && field.toLowerCase().includes(searchTerm.toLowerCase())
    );

    // Expense type filter
    const matchesType = selectedType === 'all' || expense.expense_type === selectedType;

    // Payment method filter
    const matchesPaymentMethod = selectedPaymentMethod === 'all' || expense.payment_method === selectedPaymentMethod;

    return matchesSearch && matchesType && matchesPaymentMethod;
  });

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-IN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      maximumFractionDigits: 0
    }).format(amount);
  };

  // Get expense type badge color
  const getExpenseTypeBadge = (type: string) => {
    switch (type) {
      case 'rent':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'salary':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'utilities':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'office_supplies':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'marketing':
        return 'bg-pink-100 text-pink-800 hover:bg-pink-200';
      case 'transport':
        return 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200';
      case 'loan_disbursement':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Format expense type for display
  const formatExpenseType = (type: string) => {
    return type.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Get payment method badge color
  const getPaymentMethodBadge = (method: string) => {
    switch (method) {
      case 'cash':
        return 'bg-green-100 text-green-800 hover:bg-green-200';
      case 'upi':
        return 'bg-purple-100 text-purple-800 hover:bg-purple-200';
      case 'bank_transfer':
        return 'bg-blue-100 text-blue-800 hover:bg-blue-200';
      case 'cheque':
        return 'bg-yellow-100 text-yellow-800 hover:bg-yellow-200';
      case 'card_payment':
        return 'bg-red-100 text-red-800 hover:bg-red-200';
      case 'online_transfer':
        return 'bg-indigo-100 text-indigo-800 hover:bg-indigo-200';
      case 'mobile_wallet':
        return 'bg-pink-100 text-pink-800 hover:bg-pink-200';
      default:
        return 'bg-gray-100 text-gray-800 hover:bg-gray-200';
    }
  };

  // Format payment method for display
  const formatPaymentMethod = (method: string) => {
    return method.split('_').map(word =>
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  // Clear all filters
  const clearFilters = () => {
    setSearchTerm('');
    setDateRange({ from: undefined, to: undefined });
    setSelectedType('all');
    setSelectedPaymentMethod('all');
  };

  // Handle loading state
  if (isLoadingExpenses) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Handle error state
  if (isExpensesError) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <CardTitle>Error Loading Expenses</CardTitle>
            <CardDescription>
              There was an error loading the expenses. Please try again.
            </CardDescription>
          </CardHeader>
          <CardContent className="flex justify-center p-6">
            <Button onClick={() => window.location.reload()}>Retry</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Calculate total expenses
  const totalExpenses = filteredExpenses.reduce((total: number, expense: Expense) => {
    return total + Number(expense.amount);
  }, 0);

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader className="flex flex-col space-y-1.5 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 pb-4">
          <div>
            <CardTitle className="text-2xl font-bold">Expenses</CardTitle>
            <CardDescription>
              Manage and track all company expenses
            </CardDescription>
          </div>
          <Sheet open={createSheetOpen} onOpenChange={setCreateSheetOpen}>
            <SheetTrigger asChild>
              <Button className="w-full sm:w-auto mt-3 sm:mt-0">
                <Plus className="mr-2 h-4 w-4" />
                Add Expense
              </Button>
            </SheetTrigger>
            <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
              <SheetHeader className="mb-5">
                <SheetTitle>Add New Expense</SheetTitle>
                <SheetDescription>
                  Record a new expense for your company. Fill in the details below.
                </SheetDescription>
              </SheetHeader>
              <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
                  {/* Date and Amount in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Expense Date */}
                    <FormField
                      control={form.control}
                      name="expense_date"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`
                                    w-full pl-3 text-left font-normal
                                    ${!field.value && "text-muted-foreground"}
                                  `}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date > new Date()
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Amount */}
                    <FormField
                      control={form.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Enter amount"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : '')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                  </div>

                  {/* Expense Type and Payment Method in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Expense Type */}
                    <FormField
                      control={form.control}
                      name="expense_type"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Expense Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="rent">Rent</SelectItem>
                              <SelectItem value="salary">Salary</SelectItem>
                              <SelectItem value="utilities">Utilities</SelectItem>
                              <SelectItem value="office_supplies">Office Supplies</SelectItem>
                              <SelectItem value="marketing">Marketing</SelectItem>
                              <SelectItem value="transport">Transport</SelectItem>
                              <SelectItem value="loan_disbursement">Loan Disbursement</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Payment Method */}
                    <FormField
                      control={form.control}
                      name="payment_method"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Payment Method</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select payment method" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="cash">Cash</SelectItem>
                              <SelectItem value="upi">UPI</SelectItem>
                              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                              <SelectItem value="cheque">Cheque</SelectItem>
                              <SelectItem value="card_payment">Card Payment</SelectItem>
                              <SelectItem value="online_transfer">Online Transfer</SelectItem>
                              <SelectItem value="mobile_wallet">Mobile Wallet</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                  </div>

                  {/* Reference Number and Branch in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Reference Number */}
                    <FormField
                      control={form.control}
                      name="reference_number"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Reference Number (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter reference number"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Branch */}
                    <FormField
                      control={form.control}
                      name="branch_id"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Branch (Optional)</FormLabel>
                          <Select
                            onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}
                            defaultValue={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select branch" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {branches.map((branch) => (
                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                  {branch.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Description */}
                  <FormField
                    control={form.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter expense description"
                            className="h-24"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Notes */}
                  <FormField
                    control={form.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional notes"
                            className="h-20"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <SheetFooter className="pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setCreateSheetOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={createMutation.isPending}
                    >
                      {createMutation.isPending ? (
                        <>
                          <Spinner className="mr-2 h-4 w-4" />
                          Saving...
                        </>
                      ) : (
                        "Save Expense"
                      )}
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>
        </CardHeader>

        <CardContent>
          <div className="flex flex-col sm:flex-row justify-between mb-6 space-y-2 sm:space-y-0 sm:space-x-2">
            <div className="relative w-full sm:w-1/3">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-gray-500" />
              <Input
                type="search"
                placeholder="Search expenses..."
                className="pl-8 w-full"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex flex-col sm:flex-row space-y-2 sm:space-y-0 sm:space-x-2">
              {/* Date Range Filter */}
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className="w-full sm:w-auto justify-start text-left font-normal"
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {dateRange.from ? (
                      dateRange.to ? (
                        <>
                          {format(dateRange.from, "LLL dd, y")} -{" "}
                          {format(dateRange.to, "LLL dd, y")}
                        </>
                      ) : (
                        format(dateRange.from, "LLL dd, y")
                      )
                    ) : (
                      "Date Range"
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    initialFocus
                    mode="range"
                    defaultMonth={dateRange.from}
                    selected={{
                      from: dateRange.from,
                      to: dateRange.to
                    }}
                    onSelect={(range) => {
                      if (range) {
                        setDateRange({
                          from: range.from,
                          to: range.to
                        });
                      } else {
                        setDateRange({
                          from: undefined,
                          to: undefined
                        });
                      }
                    }}
                    numberOfMonths={2}
                  />
                </PopoverContent>
              </Popover>

              {/* Expense Type Filter */}
              <Select
                value={selectedType}
                onValueChange={(value) => setSelectedType(value)}
              >
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Expense Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  <SelectItem value="rent">Rent</SelectItem>
                  <SelectItem value="salary">Salary</SelectItem>
                  <SelectItem value="utilities">Utilities</SelectItem>
                  <SelectItem value="office_supplies">Office Supplies</SelectItem>
                  <SelectItem value="marketing">Marketing</SelectItem>
                  <SelectItem value="transport">Transport</SelectItem>
                  <SelectItem value="loan_disbursement">Loan Disbursement</SelectItem>
                  <SelectItem value="other">Other</SelectItem>
                </SelectContent>
              </Select>

              {/* Payment Method Filter */}
              <Select
                value={selectedPaymentMethod}
                onValueChange={(value) => setSelectedPaymentMethod(value)}
              >
                <SelectTrigger className="w-full sm:w-[180px]">
                  <SelectValue placeholder="Payment Method" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Methods</SelectItem>
                  <SelectItem value="cash">Cash</SelectItem>
                  <SelectItem value="upi">UPI</SelectItem>
                  <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                  <SelectItem value="cheque">Cheque</SelectItem>
                  <SelectItem value="card_payment">Card Payment</SelectItem>
                  <SelectItem value="online_transfer">Online Transfer</SelectItem>
                  <SelectItem value="mobile_wallet">Mobile Wallet</SelectItem>
                </SelectContent>
              </Select>

              {/* Clear Filters Button */}
              <Button
                variant="ghost"
                onClick={clearFilters}
                className="w-full sm:w-auto"
              >
                <Filter className="mr-2 h-4 w-4" />
                Clear Filters
              </Button>
            </div>
          </div>

          {/* Total Expenses Summary */}
          <Card className="mb-6 bg-gray-50">
            <CardContent className="pt-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-1">
                  <p className="text-sm text-gray-500">Total Expenses (Filtered)</p>
                  <p className="text-2xl font-bold">{formatCurrency(totalExpenses)}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-gray-500">Total Records</p>
                  <p className="text-2xl font-bold">{filteredExpenses.length}</p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-gray-500">Date Range</p>
                  <p className="text-lg">
                    {dateRange.from && dateRange.to ? (
                      `${format(dateRange.from, "dd MMM yyyy")} - ${format(dateRange.to, "dd MMM yyyy")}`
                    ) : (
                      "All Time"
                    )}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Expenses Table */}
          {filteredExpenses.length === 0 ? (
            <div className="text-center py-12">
              <p className="text-gray-500 mb-4">No expenses found matching your search criteria.</p>
              <Button onClick={() => setCreateSheetOpen(true)}>
                <Plus className="mr-2 h-4 w-4" />
                Add New Expense
              </Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-[110px]">
                      <div className="flex items-center">
                        Date
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead>Description</TableHead>
                    <TableHead className="w-[130px]">
                      <div className="flex items-center">
                        Type
                        <ArrowUpDown className="ml-2 h-4 w-4" />
                      </div>
                    </TableHead>
                    <TableHead className="w-[100px] text-right">Amount</TableHead>
                    <TableHead className="w-[140px]">Payment Method</TableHead>
                    <TableHead className="w-[130px]">Reference</TableHead>
                    <TableHead className="w-[80px]"></TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredExpenses.map((expense: Expense) => (
                    <TableRow key={expense.id}>
                      <TableCell className="font-medium">
                        {formatDate(expense.expense_date)}
                      </TableCell>
                      <TableCell>
                        <div className="max-w-[250px] truncate" title={expense.description}>
                          {expense.description}
                        </div>
                        {expense.branch && (
                          <div className="text-xs text-gray-500 mt-1">
                            Branch: {expense.branch.name}
                          </div>
                        )}
                      </TableCell>
                      <TableCell>
                        <Badge className={getExpenseTypeBadge(expense.expense_type)}>
                          {formatExpenseType(expense.expense_type)}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right font-medium">
                        {formatCurrency(expense.amount)}
                      </TableCell>
                      <TableCell>
                        <Badge className={getPaymentMethodBadge(expense.payment_method)}>
                          {formatPaymentMethod(expense.payment_method)}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {expense.reference_number ? (
                          <span className="text-sm">{expense.reference_number}</span>
                        ) : (
                          <span className="text-xs text-gray-500">-</span>
                        )}
                      </TableCell>
                      <TableCell>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => openEditForm(expense)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => {
                                setExpenseToDelete(expense.id);
                                setDeleteDialogOpen(true);
                              }}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}

          {/* Edit Expense Sheet */}
          <Sheet open={editSheetOpen} onOpenChange={setEditSheetOpen}>
            <SheetContent className="overflow-y-auto w-full sm:max-w-lg">
              <SheetHeader className="mb-5">
                <SheetTitle>Edit Expense</SheetTitle>
                <SheetDescription>
                  Update the expense details below.
                </SheetDescription>
              </SheetHeader>
              <Form {...editForm}>
                <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-4">
                  {/* Date and Amount in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Expense Date */}
                    <FormField
                      control={editForm.control}
                      name="expense_date"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Date</FormLabel>
                          <Popover>
                            <PopoverTrigger asChild>
                              <FormControl>
                                <Button
                                  variant="outline"
                                  className={`
                                    w-full pl-3 text-left font-normal
                                    ${!field.value && "text-muted-foreground"}
                                  `}
                                >
                                  {field.value ? (
                                    format(field.value, "PPP")
                                  ) : (
                                    <span>Pick a date</span>
                                  )}
                                  <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                                </Button>
                              </FormControl>
                            </PopoverTrigger>
                            <PopoverContent className="w-auto p-0" align="start">
                              <Calendar
                                mode="single"
                                selected={field.value}
                                onSelect={field.onChange}
                                disabled={(date) =>
                                  date > new Date()
                                }
                                initialFocus
                              />
                            </PopoverContent>
                          </Popover>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Amount */}
                    <FormField
                      control={editForm.control}
                      name="amount"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Amount</FormLabel>
                          <FormControl>
                            <Input
                              type="number"
                              step="0.01"
                              placeholder="Enter amount"
                              {...field}
                              onChange={(e) => field.onChange(e.target.value ? parseFloat(e.target.value) : '')}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Expense Type and Payment Method in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Expense Type */}
                    <FormField
                      control={editForm.control}
                      name="expense_type"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Expense Type</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select type" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="rent">Rent</SelectItem>
                              <SelectItem value="salary">Salary</SelectItem>
                              <SelectItem value="utilities">Utilities</SelectItem>
                              <SelectItem value="office_supplies">Office Supplies</SelectItem>
                              <SelectItem value="marketing">Marketing</SelectItem>
                              <SelectItem value="transport">Transport</SelectItem>
                              <SelectItem value="loan_disbursement">Loan Disbursement</SelectItem>
                              <SelectItem value="other">Other</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Payment Method */}
                    <FormField
                      control={editForm.control}
                      name="payment_method"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Payment Method</FormLabel>
                          <Select
                            onValueChange={field.onChange}
                            defaultValue={field.value}
                            value={field.value}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select payment method" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="cash">Cash</SelectItem>
                              <SelectItem value="upi">UPI</SelectItem>
                              <SelectItem value="bank_transfer">Bank Transfer</SelectItem>
                              <SelectItem value="cheque">Cheque</SelectItem>
                              <SelectItem value="card_payment">Card Payment</SelectItem>
                              <SelectItem value="online_transfer">Online Transfer</SelectItem>
                              <SelectItem value="mobile_wallet">Mobile Wallet</SelectItem>
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Reference Number and Branch in the same row */}
                  <div className="flex flex-row gap-4">
                    {/* Reference Number */}
                    <FormField
                      control={editForm.control}
                      name="reference_number"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Reference Number (Optional)</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="Enter reference number"
                              {...field}
                              value={field.value || ''}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    {/* Branch */}
                    <FormField
                      control={editForm.control}
                      name="branch_id"
                      render={({ field }) => (
                        <FormItem className="flex-1">
                          <FormLabel>Branch (Optional)</FormLabel>
                          <Select
                            onValueChange={(value) => field.onChange(value ? parseInt(value) : null)}
                            defaultValue={field.value?.toString()}
                            value={field.value?.toString()}
                          >
                            <FormControl>
                              <SelectTrigger>
                                <SelectValue placeholder="Select branch" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent>
                              <SelectItem value="none">None</SelectItem>
                              {branches.map((branch) => (
                                <SelectItem key={branch.id} value={branch.id.toString()}>
                                  {branch.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  {/* Description */}
                  <FormField
                    control={editForm.control}
                    name="description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Description</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Enter expense description"
                            className="h-24"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  {/* Notes */}
                  <FormField
                    control={editForm.control}
                    name="notes"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Notes (Optional)</FormLabel>
                        <FormControl>
                          <Textarea
                            placeholder="Additional notes"
                            className="h-20"
                            {...field}
                            value={field.value || ''}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <SheetFooter className="pt-4">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setEditSheetOpen(false)}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      disabled={editMutation.isPending}
                    >
                      {editMutation.isPending ? (
                        <>
                          <Spinner className="mr-2 h-4 w-4" />
                          Updating...
                        </>
                      ) : (
                        "Update Expense"
                      )}
                    </Button>
                  </SheetFooter>
                </form>
              </Form>
            </SheetContent>
          </Sheet>

          {/* Delete Confirmation Dialog */}
          <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
            <DialogContent>
              <DialogHeader>
                <DialogTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
                  Confirm Deletion
                </DialogTitle>
                <DialogDescription>
                  Are you sure you want to delete this expense? This action cannot be undone.
                </DialogDescription>
              </DialogHeader>
              <DialogFooter className="flex justify-end space-x-2">
                <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
                  Cancel
                </Button>
                <Button
                  variant="destructive"
                  onClick={handleDeleteConfirm}
                  disabled={deleteMutation.isPending}
                >
                  {deleteMutation.isPending ? (
                    <>
                      <Spinner className="mr-2 h-4 w-4" />
                      Deleting...
                    </>
                  ) : (
                    "Delete"
                  )}
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </CardContent>
      </Card>
    </div>
  );
}