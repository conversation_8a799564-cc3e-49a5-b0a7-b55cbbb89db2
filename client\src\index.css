@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 220 25% 97%;
  --foreground: 220 10% 10%;
  --muted: 220 10% 92%;
  --muted-foreground: 220 4% 44%;
  --popover: 0 0% 100%;
  --popover-foreground: 220 10% 10%;
  --card: 0 0% 100%;
  --card-foreground: 220 10% 10%;
  --border: 220 13% 88%;
  --input: 220 13% 88%;
  --primary: 214 89% 51%;
  --primary-foreground: 211 100% 99%;
  --secondary: 220 14% 96%;
  --secondary-foreground: 220 10% 28%;
  --accent: 220 80% 50%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 60 9% 98%;
  --ring: 214 89% 51%;
  --radius: 0.5rem;

  /* Chart colors */
  --chart-1: 214 89% 51%;
  --chart-2: 160 67% 52%;
  --chart-3: 48 95% 53%;
  --chart-4: 325 78% 60%;
  --chart-5: 275 80% 60%;

  /* Sidebar colors */
  --sidebar-background: 214 76% 17%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 214 83% 24%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 100%;
  --sidebar-accent-foreground: 214 89% 51%;
  --sidebar-border: 214 60% 27%;
  --sidebar-ring: 214 89% 51%;
}

.dark {
  --background: 220 12% 10%;
  --foreground: 213 31% 91%;
  --muted: 220 10% 20%;
  --muted-foreground: 220 4% 65%;
  --popover: 220 12% 10%;
  --popover-foreground: 213 31% 91%;
  --card: 220 12% 12%;
  --card-foreground: 213 31% 91%;
  --border: 220 13% 22%;
  --input: 220 13% 22%;
  --primary: 214 89% 51%;
  --primary-foreground: 211 100% 99%;
  --secondary: 220 14% 16%;
  --secondary-foreground: 220 10% 80%;
  --accent: 220 80% 50%;
  --accent-foreground: 0 0% 100%;
  --destructive: 0 84% 60%;
  --destructive-foreground: 60 9% 98%;
  --ring: 214 89% 51%;

  /* Sidebar colors remain the same in dark mode */
  --sidebar-background: 214 76% 17%;
  --sidebar-foreground: 0 0% 100%;
  --sidebar-primary: 214 83% 24%;
  --sidebar-primary-foreground: 0 0% 100%;
  --sidebar-accent: 0 0% 100%;
  --sidebar-accent-foreground: 214 89% 51%;
  --sidebar-border: 214 60% 27%;
  --sidebar-ring: 214 89% 51%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
  }
}

/* Custom classes */
.success-gradient {
  @apply bg-gradient-to-r from-emerald-500 to-emerald-600;
}

.warning-gradient {
  @apply bg-gradient-to-r from-amber-500 to-amber-600;
}

.danger-gradient {
  @apply bg-gradient-to-r from-red-500 to-red-600;
}

.primary-gradient {
  @apply bg-gradient-to-r from-primary to-blue-600;
}

.accent-gradient {
  @apply bg-gradient-to-r from-accent to-violet-600;
}

.metric-card-icon {
  @apply flex-shrink-0 p-3 rounded-md text-white;
}

.sidebar-link {
  @apply flex items-center w-full px-3 py-2 rounded-md text-sm transition-colors duration-150 ease-in-out;
}

.sidebar-link.active {
  @apply bg-sidebar-primary text-white font-medium;
}

.sidebar-link:not(.active) {
  @apply text-sidebar-foreground/90 hover:bg-sidebar-primary/80 hover:text-white;
}

/* Custom scrollbar styles */
.custom-scrollbar::-webkit-scrollbar {
  width: 5px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 20px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Hide scrollbar when not in use but keep functionality */
.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(255, 255, 255, 0.1) transparent;
}

/* Animation classes for landing page */
@keyframes float {
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
}

@keyframes spin-slow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.animate-float {
  animation: float 5s ease-in-out infinite;
}

.animate-float-delayed {
  animation: float 5s ease-in-out 2s infinite;
}

.animate-float-slow {
  animation: float 7s ease-in-out infinite;
}

.animate-spin-slow {
  animation: spin-slow 20s linear infinite;
}

.animate-bounce {
  animation: bounce 3s infinite;
}

@keyframes bounce {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}
