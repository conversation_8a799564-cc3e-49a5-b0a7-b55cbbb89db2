import React, { useState, useMemo } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Search, Users, UserCheck, Shield, X, Plus } from 'lucide-react';
import { useQuery } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';

interface Role {
  id: number;
  name: string;
  description?: string;
  is_system: boolean;
}

interface User {
  id: number;
  email: string;
  first_name?: string;
  last_name?: string;
  role_name?: string;
}

interface ApproverSelectorProps {
  selectedRoles: number[];
  selectedUsers: number[];
  onRolesChange: (roles: number[]) => void;
  onUsersChange: (users: number[]) => void;
  companyId?: number;
  disabled?: boolean;
  maxSelections?: number;
  showCounts?: boolean;
}

export function ApproverSelector({
  selectedRoles,
  selectedUsers,
  onRolesChange,
  onUsersChange,
  companyId,
  disabled = false,
  maxSelections,
  showCounts = true,
}: ApproverSelectorProps) {
  const [roleSearchQuery, setRoleSearchQuery] = useState('');
  const [userSearchQuery, setUserSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('roles');

  // Fetch roles
  const {
    data: roles = [],
    isLoading: isLoadingRoles,
  } = useQuery({
    queryKey: ['roles', companyId],
    queryFn: async (): Promise<Role[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest(`/api/roles${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      return response.json();
    },
    enabled: true,
  });

  // Fetch users
  const {
    data: users = [],
    isLoading: isLoadingUsers,
  } = useQuery({
    queryKey: ['users', companyId],
    queryFn: async (): Promise<User[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest(`/api/users${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }
      return response.json();
    },
    enabled: true,
  });

  // Filter roles based on search
  const filteredRoles = useMemo(() => {
    if (!roleSearchQuery.trim()) return roles;
    return roles.filter(role =>
      role.name.toLowerCase().includes(roleSearchQuery.toLowerCase()) ||
      role.description?.toLowerCase().includes(roleSearchQuery.toLowerCase())
    );
  }, [roles, roleSearchQuery]);

  // Filter users based on search
  const filteredUsers = useMemo(() => {
    if (!userSearchQuery.trim()) return users;
    return users.filter(user =>
      user.email.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.first_name?.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.last_name?.toLowerCase().includes(userSearchQuery.toLowerCase()) ||
      user.role_name?.toLowerCase().includes(userSearchQuery.toLowerCase())
    );
  }, [users, userSearchQuery]);

  // Get selected role names
  const selectedRoleNames = useMemo(() => {
    return roles.filter(role => selectedRoles.includes(role.id)).map(role => role.name);
  }, [roles, selectedRoles]);

  // Get selected user names
  const selectedUserNames = useMemo(() => {
    return users.filter(user => selectedUsers.includes(user.id)).map(user => 
      `${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email
    );
  }, [users, selectedUsers]);

  const totalSelections = selectedRoles.length + selectedUsers.length;
  const canAddMore = !maxSelections || totalSelections < maxSelections;

  const handleRoleToggle = (roleId: number) => {
    if (disabled) return;
    
    const isSelected = selectedRoles.includes(roleId);
    if (isSelected) {
      onRolesChange(selectedRoles.filter(id => id !== roleId));
    } else if (canAddMore) {
      onRolesChange([...selectedRoles, roleId]);
    }
  };

  const handleUserToggle = (userId: number) => {
    if (disabled) return;
    
    const isSelected = selectedUsers.includes(userId);
    if (isSelected) {
      onUsersChange(selectedUsers.filter(id => id !== userId));
    } else if (canAddMore) {
      onUsersChange([...selectedUsers, userId]);
    }
  };

  const handleRemoveRole = (roleId: number) => {
    onRolesChange(selectedRoles.filter(id => id !== roleId));
  };

  const handleRemoveUser = (userId: number) => {
    onUsersChange(selectedUsers.filter(id => id !== userId));
  };

  const handleClearAll = () => {
    onRolesChange([]);
    onUsersChange([]);
  };

  return (
    <div className="space-y-4">
      {/* Selection Summary */}
      {(selectedRoles.length > 0 || selectedUsers.length > 0) && (
        <Card>
          <CardHeader className="pb-3">
            <div className="flex items-center justify-between">
              <CardTitle className="text-sm">Selected Approvers</CardTitle>
              <div className="flex items-center gap-2">
                {showCounts && (
                  <Badge variant="outline">
                    {totalSelections} selected
                    {maxSelections && ` / ${maxSelections}`}
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearAll}
                  disabled={disabled}
                >
                  Clear All
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent className="pt-0">
            <div className="space-y-2">
              {/* Selected Roles */}
              {selectedRoles.length > 0 && (
                <div>
                  <Label className="text-xs text-muted-foreground">Roles</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedRoleNames.map((roleName, index) => (
                      <Badge key={selectedRoles[index]} variant="default" className="flex items-center gap-1">
                        <Shield className="h-3 w-3" />
                        {roleName}
                        {!disabled && (
                          <button
                            onClick={() => handleRemoveRole(selectedRoles[index])}
                            className="ml-1 hover:bg-white/20 rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}

              {/* Selected Users */}
              {selectedUsers.length > 0 && (
                <div>
                  <Label className="text-xs text-muted-foreground">Users</Label>
                  <div className="flex flex-wrap gap-1 mt-1">
                    {selectedUserNames.map((userName, index) => (
                      <Badge key={selectedUsers[index]} variant="secondary" className="flex items-center gap-1">
                        <UserCheck className="h-3 w-3" />
                        {userName}
                        {!disabled && (
                          <button
                            onClick={() => handleRemoveUser(selectedUsers[index])}
                            className="ml-1 hover:bg-white/20 rounded-full p-0.5"
                          >
                            <X className="h-3 w-3" />
                          </button>
                        )}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Selection Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            Add Approvers
          </CardTitle>
          <CardDescription>
            Select roles and users who can approve this workflow step
            {maxSelections && ` (max ${maxSelections} selections)`}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="roles" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Roles ({selectedRoles.length})
              </TabsTrigger>
              <TabsTrigger value="users" className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                Users ({selectedUsers.length})
              </TabsTrigger>
            </TabsList>

            <TabsContent value="roles" className="space-y-4">
              {/* Role Search */}
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search roles..."
                  className="pl-8"
                  value={roleSearchQuery}
                  onChange={(e) => setRoleSearchQuery(e.target.value)}
                />
              </div>

              {/* Role List */}
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {isLoadingRoles ? (
                    <div className="text-center py-4 text-muted-foreground">Loading roles...</div>
                  ) : filteredRoles.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      {roleSearchQuery ? 'No roles found matching your search.' : 'No roles available.'}
                    </div>
                  ) : (
                    filteredRoles.map((role) => (
                      <div
                        key={role.id}
                        className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                          selectedRoles.includes(role.id)
                            ? 'bg-blue-50 border-blue-200'
                            : 'hover:bg-gray-50'
                        } ${!canAddMore && !selectedRoles.includes(role.id) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleRoleToggle(role.id)}
                      >
                        <Checkbox
                          checked={selectedRoles.includes(role.id)}
                          disabled={disabled || (!canAddMore && !selectedRoles.includes(role.id))}
                          onChange={() => handleRoleToggle(role.id)}
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{role.name}</span>
                            {role.is_system && <Badge variant="outline" className="text-xs">System</Badge>}
                          </div>
                          {role.description && (
                            <p className="text-sm text-muted-foreground">{role.description}</p>
                          )}
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>

            <TabsContent value="users" className="space-y-4">
              {/* User Search */}
              <div className="relative">
                <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                <Input
                  type="search"
                  placeholder="Search users..."
                  className="pl-8"
                  value={userSearchQuery}
                  onChange={(e) => setUserSearchQuery(e.target.value)}
                />
              </div>

              {/* User List */}
              <ScrollArea className="h-64">
                <div className="space-y-2">
                  {isLoadingUsers ? (
                    <div className="text-center py-4 text-muted-foreground">Loading users...</div>
                  ) : filteredUsers.length === 0 ? (
                    <div className="text-center py-4 text-muted-foreground">
                      {userSearchQuery ? 'No users found matching your search.' : 'No users available.'}
                    </div>
                  ) : (
                    filteredUsers.map((user) => (
                      <div
                        key={user.id}
                        className={`flex items-center space-x-2 p-2 rounded-md border cursor-pointer transition-colors ${
                          selectedUsers.includes(user.id)
                            ? 'bg-green-50 border-green-200'
                            : 'hover:bg-gray-50'
                        } ${!canAddMore && !selectedUsers.includes(user.id) ? 'opacity-50 cursor-not-allowed' : ''}`}
                        onClick={() => handleUserToggle(user.id)}
                      >
                        <Checkbox
                          checked={selectedUsers.includes(user.id)}
                          disabled={disabled || (!canAddMore && !selectedUsers.includes(user.id))}
                          onChange={() => handleUserToggle(user.id)}
                        />
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">
                              {`${user.first_name || ''} ${user.last_name || ''}`.trim() || user.email}
                            </span>
                            {user.role_name && (
                              <Badge variant="outline" className="text-xs">{user.role_name}</Badge>
                            )}
                          </div>
                          <p className="text-sm text-muted-foreground">{user.email}</p>
                        </div>
                      </div>
                    ))
                  )}
                </div>
              </ScrollArea>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}
