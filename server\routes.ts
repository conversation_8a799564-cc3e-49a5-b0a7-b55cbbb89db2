import type { Express } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import { db } from "./db";
import { authMiddleware, requireRole, requireCompanyAccess, AuthRequest } from "./middleware/auth";
import { requirePrefixSettings } from "./middleware/prefix-settings";
import {
  companySettings, companies, companyPrefixSettings, companyPrefixSettingsSchema,
  customers, loans, collections, partners, agents
} from "../shared/schema";
import { generateToken } from "./utils/jwt";
import bcrypt from "bcrypt";
import { eq, and, count } from "drizzle-orm";
import { collectionStatusService } from "./services/collectionStatusService";
import { runStatusUpdateJob } from "./jobs/statusUpdateJob";
import { notificationService } from "./services/notificationService";
import { LoanStorage } from "./storage/loan.storage";
import { PartnerStorage } from "./storage/partner.storage";
import { TransactionStorage } from "./storage/financial/transaction.storage";
import {
  getTransactionsByCompany,
  getTransaction,
  createTransaction,
  getAccountById,
  getAccountByCode,
  initializeSystemAccounts,
  createJournalEntry,
  deleteAllTestData,
  fixMissingCollectionTransactions
} from "./financialManagement";
import {
  loginSchema, registerSchema, insertCompanySchema, insertCustomerSchema,
  insertLoanSchema, insertCollectionSchema, insertAgentSchema,
  insertResellerSchema, insertReferralSchema, insertSubscriptionPlanSchema,
  insertSubscriptionSchema, insertPartnerSchema, insertUserSchema, users,
  userCompanies, insertBranchSchema, insertGroupSchema, branches, groups,
  insertFormTemplateSchema, insertFormFieldSchema, insertFormSubmissionSchema,
  insertLoanConfigurationSchema, insertExpenseSchema,
  // Financial Management Schemas
  insertAccountSchema, insertTransactionSchema, insertAccountBalanceSchema,
  insertAccountingPeriodSchema, insertShareholderSchema, insertShareholdingSchema,
  insertInvestmentTransactionSchema, insertFixedAssetSchema, insertBalanceSheetSchema,
  insertBalanceSheetItemSchema, insertDepreciationScheduleSchema,
  // Tables
  collections, payments, accounts, transactions, accountBalances,
  accountingPeriods, shareholders, shareholdings, investmentTransactions, fixedAssets,
  balanceSheets, balanceSheetItems, depreciationSchedules,
  // Types for Collections and Loans
  type InsertCustomer, type InsertAgent, type InsertPartner, type InsertUser,
  type InsertCollection, type InsertLoan, type InsertBranch, type InsertGroup,
  type InsertFormTemplate, type InsertFormField, type InsertFormSubmission,
  type InsertLoanConfiguration, type InsertExpense, type Expense, type ExpenseFilters,
  // Types for Financial Management
  type InsertAccount, type Account, type InsertTransaction, type Transaction,
  type InsertAccountBalance, type AccountBalance, type InsertAccountingPeriod, type AccountingPeriod,
  type InsertShareholder, type Shareholder, type InsertShareholding, type Shareholding,
  type InsertInvestmentTransaction, type InvestmentTransaction, type InsertFixedAsset, type FixedAsset,
  type InsertBalanceSheet, type BalanceSheet, type InsertBalanceSheetItem, type BalanceSheetItem,
  type InsertDepreciationSchedule, type DepreciationSchedule,
  // Report Types
  type DailyCollectionReport, type DaySheetReport, type CustomerReport,
  type AgentReport, type ProfitLossReport, type AccountStatement, type AccountBalanceReport,
  type BalanceSheetReport, type BalanceSheetDetail, type CashFlowReport, type ShareholderReport
} from "@shared/schema";
import { Request, Response } from "express";
import { nanoid } from "nanoid";
import { ZodError, z } from 'zod';
import errorLogger from './utils/errorLogger';
import { SYSTEM_ACCOUNT_CODES } from './config/systemAccounts';
import { PaymentProcessor } from './utils/paymentProcessor';
import { createJournalEntry } from './financialManagement';
import { inArray } from 'drizzle-orm';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to validate user's company role
async function getUserCompanyRole(userId: number, companyId: number) {
  const [userCompany] = await db.select()
    .from(userCompanies)
    .where(eq(userCompanies.user_id, userId))
    .where(eq(userCompanies.company_id, companyId));

  return userCompany;
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

// Helper to get company name from company_id (DEPRECATED - use getPrefixFromSettings instead)
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of last word
      // This handles cases like "GOVINDARAJI S" correctly
      prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    console.log(`Generated prefix: "${prefix}" for company name: "${fullName}"`);
    return prefix;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}`;
  }
}

// Helper to get prefix from company_prefix_settings table
async function getPrefixFromSettings(companyId: number, entityType: 'loan' | 'collection' | 'customer' | 'partner' | 'agent'): Promise<{ prefix: string, startNumber: number }> {
  try {
    // Get company prefix settings
    const settings = await db.query.companyPrefixSettings.findFirst({
      where: eq(companyPrefixSettings.company_id, companyId),
    });

    if (!settings) {
      console.log(`No prefix settings found for company ${companyId}, falling back to company name`);
      // Fall back to company name-based prefix if no settings found
      const prefix = await getCompanyName(companyId);
      return { prefix, startNumber: 1 };
    }

    // Return the appropriate prefix and start number based on entity type
    switch (entityType) {
      case 'loan':
        return { prefix: settings.loan_prefix, startNumber: settings.loan_start_number };
      case 'collection':
        return { prefix: settings.collection_prefix, startNumber: settings.collection_start_number };
      case 'customer':
        return { prefix: settings.customer_prefix, startNumber: settings.customer_start_number };
      case 'partner':
        return { prefix: settings.partner_prefix, startNumber: settings.partner_start_number };
      case 'agent':
        return { prefix: settings.agent_prefix, startNumber: settings.agent_start_number };
      default:
        throw new Error(`Invalid entity type: ${entityType}`);
    }
  } catch (error) {
    console.error(`Error fetching prefix settings for company ${companyId}:`, error);
    // Fall back to company name-based prefix if error occurs
    const prefix = await getCompanyName(companyId);
    return { prefix, startNumber: 1 };
  }
}

// Helper function to get the highest agent serial number for a company
async function getHighestAgentSerial(companyId: number, companyPrefix: string): Promise<number> {
  try {
    // Get all agents for this company
    const agents = await storage.getAgentsByCompany(companyId);

    if (!agents || agents.length === 0) {
      return 0; // No agents found, start with 1
    }

    // Extract the numeric part from agent_reference_code strings and find the highest
    let highestNumber = 0;
    for (const agent of agents) {
      if (agent.agent_reference_code && agent.agent_reference_code.startsWith(companyPrefix)) {
        const parts = agent.agent_reference_code.split('-');
        if (parts.length === 2) {
          const numericPart = parseInt(parts[1], 10);
          if (!isNaN(numericPart) && numericPart > highestNumber) {
            highestNumber = numericPart;
          }
        }
      }
    }

    return highestNumber;
  } catch (error) {
    console.error('Error getting highest agent serial number:', error);
    return 0;
  }
}

// Helper function to get the highest customer serial number for a company
async function getHighestCustomerSerial(companyId: number, companyPrefix: string): Promise<number> {
  try {
    // Get all customers for this company
    const customers = await storage.getCustomersByCompany(companyId);

    if (!customers || customers.length === 0) {
      return 0; // No customers found, start with 1
    }

    // Extract the numeric part from customer_reference_code strings and find the highest
    let highestNumber = 0;
    for (const customer of customers) {
      if (customer.customer_reference_code && customer.customer_reference_code.startsWith(companyPrefix)) {
        const parts = customer.customer_reference_code.split('-');
        if (parts.length === 2) {
          const numericPart = parseInt(parts[1], 10);
          if (!isNaN(numericPart) && numericPart > highestNumber) {
            highestNumber = numericPart;
          }
        }
      }
    }

    return highestNumber;
  } catch (error) {
    console.error('Error getting highest customer serial number:', error);
    return 0;
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // Company settings routes
  app.get('/api/companies/:id/settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // SaaS admin has access to all companies
      if (req.user.role === 'saas_admin') {
        // Continue with fetching settings
      }
      // Direct company match
      else if (req.user.company_id === companyId) {
        // Continue with fetching settings
      }
      // Check if user has access through company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Get company settings from database
      const settings = await db.query.companySettings.findFirst({
        where: eq(companySettings.company_id, companyId),
      });

      // If settings don't exist, return default values
      if (!settings) {
        return res.json({
          company_id: companyId,
          date_format: 'dd-MM-yyyy',
          currency_symbol: '₹',
        });
      }

      return res.json(settings);
    } catch (error) {
      console.error('Error fetching company settings:', error);
      return res.status(500).json({ message: 'Failed to fetch company settings' });
    }
  });

  app.put('/api/companies/:id/settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // SaaS admin has access to all companies
      if (req.user.role === 'saas_admin') {
        // Continue with updating settings
      }
      // Direct company match
      else if (req.user.company_id === companyId) {
        // Continue with updating settings
      }
      // Check if user has access through company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      const { date_format, currency_symbol } = req.body;

      // Validate input
      if (!date_format || !currency_symbol) {
        return res.status(400).json({ message: 'Date format and currency symbol are required' });
      }

      // Check if settings already exist
      const existingSettings = await db.query.companySettings.findFirst({
        where: eq(companySettings.company_id, companyId),
      });

      let result;

      if (existingSettings) {
        // Update existing settings
        result = await db
          .update(companySettings)
          .set({
            date_format,
            currency_symbol,
            updated_at: new Date(),
          })
          .where(eq(companySettings.company_id, companyId))
          .returning();
      } else {
        // Create new settings
        result = await db
          .insert(companySettings)
          .values({
            company_id: companyId,
            date_format,
            currency_symbol,
          })
          .returning();
      }

      return res.json(result[0]);
    } catch (error) {
      console.error('Error updating company settings:', error);
      return res.status(500).json({ message: 'Failed to update company settings' });
    }
  });

  // Company Prefix Settings Routes

  // GET /api/companies/:id/prefix-settings
  app.get('/api/companies/:id/prefix-settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    // Manual company access check
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const companyId = parseInt(req.params.id);

    // SaaS admin has access to all companies
    if (req.user.role === 'saas_admin') {
      // Continue with the request
    }
    // Direct company match
    else if (req.user.company_id === companyId) {
      // Continue with the request
    }
    // Check if user has access through company associations
    else {
      try {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      } catch (error) {
        console.error('Error checking company access:', error);
        return res.status(500).json({ message: 'Error checking company access' });
      }
    }
    try {
      const companyId = parseInt(req.params.id);

      console.log(`GET /api/companies/${companyId}/prefix-settings called`);

      // Get company prefix settings
      const settings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      // If settings don't exist, return a 200 response with empty data
      if (!settings) {
        console.log(`No prefix settings found for company ${companyId}`);
        return res.status(200).json({
          data: null,
          message: 'No prefix settings found for this company',
          code: 'NO_PREFIX_SETTINGS'
        });
      }

      console.log(`Found prefix settings for company ${companyId}:`, settings);
      return res.json({
        data: settings,
        message: 'Prefix settings retrieved successfully',
        code: 'PREFIX_SETTINGS_FOUND'
      });
    } catch (error) {
      errorLogger.logError('Error fetching company prefix settings', 'prefix-settings-fetch', error as Error);
      return res.status(500).json({
        data: null,
        message: 'Failed to fetch company prefix settings',
        code: 'PREFIX_SETTINGS_FETCH_ERROR'
      });
    }
  });

  // POST /api/companies/:id/prefix-settings
  app.post('/api/companies/:id/prefix-settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    // Manual company access check
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const companyId = parseInt(req.params.id);

    // SaaS admin has access to all companies
    if (req.user.role === 'saas_admin') {
      // Continue with the request
    }
    // Direct company match
    else if (req.user.company_id === companyId) {
      // Continue with the request
    }
    // Check if user has access through company associations
    else {
      try {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      } catch (error) {
        console.error('Error checking company access:', error);
        return res.status(500).json({ message: 'Error checking company access' });
      }
    }
    try {
      const companyId = parseInt(req.params.id);

      console.log(`POST /api/companies/${companyId}/prefix-settings called with body:`, req.body);

      // Check if settings already exist for this company
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      if (existingSettings) {
        console.log(`Prefix settings already exist for company ${companyId}`);
        return res.status(409).json({
          data: null,
          message: 'Prefix settings already exist for this company. Use PUT to update.',
          code: 'PREFIX_SETTINGS_ALREADY_EXIST'
        });
      }

      // Validate request body
      const validatedData = companyPrefixSettingsSchema.parse({
        ...req.body,
        company_id: companyId,
      });

      // Create new settings
      const [result] = await db
        .insert(companyPrefixSettings)
        .values(validatedData)
        .returning();

      console.log(`Created prefix settings for company ${companyId}:`, result);
      return res.status(201).json({
        data: result,
        message: 'Prefix settings created successfully',
        code: 'PREFIX_SETTINGS_CREATED'
      });
    } catch (error) {
      errorLogger.logError('Error creating company prefix settings', 'prefix-settings-create', error as Error);

      // Check if it's a validation error
      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({
          data: null,
          message: 'Invalid input data',
          code: 'INVALID_INPUT',
          errors: (error as any).errors
        });
      }

      return res.status(500).json({
        data: null,
        message: 'Failed to create company prefix settings',
        code: 'PREFIX_SETTINGS_CREATE_ERROR'
      });
    }
  });

  // PUT /api/companies/:id/prefix-settings
  app.put('/api/companies/:id/prefix-settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    // Manual company access check
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const companyId = parseInt(req.params.id);

    // SaaS admin has access to all companies
    if (req.user.role === 'saas_admin') {
      // Continue with the request
    }
    // Direct company match
    else if (req.user.company_id === companyId) {
      // Continue with the request
    }
    // Check if user has access through company associations
    else {
      try {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      } catch (error) {
        console.error('Error checking company access:', error);
        return res.status(500).json({ message: 'Error checking company access' });
      }
    }
    try {
      const companyId = parseInt(req.params.id);

      console.log(`PUT /api/companies/${companyId}/prefix-settings called with body:`, req.body);

      // Validate request body
      const validatedData = companyPrefixSettingsSchema.parse({
        ...req.body,
        company_id: companyId,
      });

      // Check if settings already exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      // If settings don't exist, allow creation
      if (!existingSettings) {
        console.log(`Creating new prefix settings for company ${companyId}`);
        // Create new settings if they don't exist
        const [result] = await db
          .insert(companyPrefixSettings)
          .values(validatedData)
          .returning();

        console.log(`Created prefix settings for company ${companyId}:`, result);
        return res.status(201).json({
          data: result,
          message: `Prefix settings created successfully`,
          code: 'PREFIX_SETTINGS_CREATED'
        });
      }

      // If settings exist, check if there's data in related tables
      // Check for existing data in collections, loans, partners, agents, customers tables
      console.log(`Checking for existing data in related tables for company ${companyId}`);

      // Check collections
      const collectionsCount = await db
        .select({ count: count() })
        .from(collections)
        .where(eq(collections.company_id, companyId));

      // Check loans
      const loansCount = await db
        .select({ count: count() })
        .from(loans)
        .where(eq(loans.company_id, companyId));

      // Check partners
      const partnersCount = await db
        .select({ count: count() })
        .from(partners)
        .where(eq(partners.company_id, companyId));

      // Check agents
      const agentsCount = await db
        .select({ count: count() })
        .from(agents)
        .where(eq(agents.company_id, companyId));

      // Check customers
      const customersCount = await db
        .select({ count: count() })
        .from(customers)
        .where(eq(customers.company_id, companyId));

      // If any of the tables have data, deny the update
      if (
        collectionsCount[0].count > 0 ||
        loansCount[0].count > 0 ||
        partnersCount[0].count > 0 ||
        agentsCount[0].count > 0 ||
        customersCount[0].count > 0
      ) {
        console.log(`Access denied: Company ${companyId} already has data in related tables`);
        console.log(`Collections: ${collectionsCount[0].count}, Loans: ${loansCount[0].count}, Partners: ${partnersCount[0].count}, Agents: ${agentsCount[0].count}, Customers: ${customersCount[0].count}`);

        return res.status(403).json({
          data: null,
          message: 'Cannot update prefix settings because there is already data in the system. This would break existing reference codes.',
          code: 'PREFIX_SETTINGS_UPDATE_DENIED',
          details: {
            collections: collectionsCount[0].count,
            loans: loansCount[0].count,
            partners: partnersCount[0].count,
            agents: agentsCount[0].count,
            customers: customersCount[0].count
          }
        });
      }

      // If no data exists in related tables, allow the update
      console.log(`Updating existing prefix settings for company ${companyId}`);
      // Update existing settings
      const [result] = await db
        .update(companyPrefixSettings)
        .set({
          ...validatedData,
          updated_at: new Date(),
        })
        .where(eq(companyPrefixSettings.company_id, companyId))
        .returning();

      console.log(`Updated prefix settings for company ${companyId}:`, result);
      return res.status(200).json({
        data: result,
        message: `Prefix settings updated successfully`,
        code: 'PREFIX_SETTINGS_UPDATED'
      });
    } catch (error) {
      errorLogger.logError('Error updating company prefix settings', 'prefix-settings-update', error as Error);

      // Check if it's a validation error
      if (error && typeof error === 'object' && 'name' in error && error.name === 'ZodError') {
        return res.status(400).json({
          data: null,
          message: 'Invalid input data',
          code: 'INVALID_INPUT',
          errors: (error as any).errors
        });
      }

      return res.status(500).json({
        data: null,
        message: 'Failed to update company prefix settings',
        code: 'PREFIX_SETTINGS_UPDATE_ERROR'
      });
    }
  });

  // DELETE /api/companies/:id/prefix-settings
  app.delete('/api/companies/:id/prefix-settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    // Manual company access check
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const companyId = parseInt(req.params.id);

    // SaaS admin has access to all companies
    if (req.user.role === 'saas_admin') {
      // Continue with the request
    }
    // Direct company match
    else if (req.user.company_id === companyId) {
      // Continue with the request
    }
    // Check if user has access through company associations
    else {
      try {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      } catch (error) {
        console.error('Error checking company access:', error);
        return res.status(500).json({ message: 'Error checking company access' });
      }
    }
    try {
      const companyId = parseInt(req.params.id);

      console.log(`DELETE /api/companies/${companyId}/prefix-settings called`);

      // Check if settings exist
      const existingSettings = await db.query.companyPrefixSettings.findFirst({
        where: eq(companyPrefixSettings.company_id, companyId),
      });

      if (!existingSettings) {
        console.log(`No prefix settings found for company ${companyId}`);
        return res.status(200).json({
          data: null,
          message: 'No prefix settings found for this company',
          code: 'NO_PREFIX_SETTINGS'
        });
      }

      // Delete the settings
      await db
        .delete(companyPrefixSettings)
        .where(eq(companyPrefixSettings.company_id, companyId));

      console.log(`Deleted prefix settings for company ${companyId}`);
      return res.status(200).json({
        data: null,
        message: 'Prefix settings deleted successfully',
        code: 'PREFIX_SETTINGS_DELETED'
      });
    } catch (error) {
      errorLogger.logError('Error deleting company prefix settings', 'prefix-settings-delete', error as Error);
      return res.status(500).json({
        data: null,
        message: 'Failed to delete company prefix settings',
        code: 'PREFIX_SETTINGS_DELETE_ERROR'
      });
    }
  });
  // Auth routes
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    try {
      const result = loginSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const { email, password } = result.data;

      // Get user with retry logic for rate limiting
      let user;
      try {
        user = await storage.getUserByEmail(email);
      } catch (error: any) {
        console.error('Error fetching user during login:', error);

        // Check if it's a rate limit error
        if (error.message && (error.message.includes('rate limit') || error.message.includes('exceeded the rate limit'))) {
          return res.status(429).json({
            message: 'Too many requests. Please try again in a moment.',
            retryAfter: 3 // Suggest retry after 3 seconds
          });
        }

        // For other errors, return a generic error message
        return res.status(500).json({ message: 'An error occurred during login. Please try again.' });
      }

      if (!user) {
        return res.status(401).json({ message: 'Invalid email or password' });
      }

      // Compare password with retry logic for potential rate limiting
      let isPasswordValid = false;
      try {
        isPasswordValid = await bcrypt.compare(password, user.password);
      } catch (error) {
        console.error('Error comparing passwords during login:', error);
        return res.status(500).json({ message: 'An error occurred during login. Please try again.' });
      }

      if (!isPasswordValid) {
        return res.status(401).json({ message: 'Invalid email or password' });
      }

      // Check if user has a primary company in user_companies table
      let primaryCompanyId = null;
      let company = null;
      let company_name = null;

      try {
        // Get all companies for the user with a single query (with retry logic)
        const userCompanies = await storage.getUserCompanies(user.id);
        console.log(`Found ${userCompanies.length} companies for user ${user.id}`);

        if (userCompanies.length > 0) {
          // First try to find the primary company
          const primaryUserCompany = userCompanies.find(uc => uc.is_primary);

          if (primaryUserCompany) {
            // Use the primary company from user_companies table
            primaryCompanyId = primaryUserCompany.company_id;
            company = primaryUserCompany.company;
            company_name = company ? company.name : null;

            console.log('Found primary company for user:', {
              user_id: user.id,
              company_id: primaryCompanyId,
              company_name,
              is_primary: true
            });
          } else {
            // If no primary company is set, use the first company in the list
            const firstUserCompany = userCompanies[0];
            primaryCompanyId = firstUserCompany.company_id;
            company = firstUserCompany.company;
            company_name = company ? company.name : null;

            console.log('No primary company found, using first company:', {
              user_id: user.id,
              company_id: primaryCompanyId,
              company_name,
              is_primary: false
            });

            // Automatically set this company as primary for better user experience
            try {
              await storage.setUserCompanyAsPrimary(firstUserCompany.id, user.id);
              console.log(`Set company ${primaryCompanyId} as primary for user ${user.id}`);
            } catch (setPrimaryError) {
              console.error('Error setting first company as primary:', setPrimaryError);
              // Continue despite error - this is not critical
            }
          }
        } else if (user.company_id) {
          // Fallback to user.company_id if no companies in user_companies table
          primaryCompanyId = user.company_id;

          try {
            company = await storage.getCompany(user.company_id);
            company_name = company ? company.name : null;

            console.log('No companies in user_companies table, using user.company_id:', {
              user_id: user.id,
              company_id: primaryCompanyId,
              company_name
            });

            // Create user-company association with is_primary=true
            if (company) {
              try {
                const userCompany = await storage.createUserCompany({
                  user_id: user.id,
                  company_id: primaryCompanyId,
                  is_primary: true,
                  role: user.role
                });
                console.log(`Created user-company association for user ${user.id} and company ${primaryCompanyId}:`, userCompany);
              } catch (createError) {
                console.error('Error creating user-company association:', createError);
                // Continue despite error - this is not critical
              }
            }
          } catch (companyError) {
            console.error('Error fetching company details:', companyError);
            // If we can't get company details, just use the ID without the name
            primaryCompanyId = user.company_id;
          }
        }
      } catch (error) {
        console.error('Error fetching company details:', error);

        // Check if it's a rate limit error
        if (error.message && (error.message.includes('rate limit') || error.message.includes('exceeded the rate limit'))) {
          // For rate limit errors, we can still proceed with login but with limited company info
          console.log('Rate limit hit when fetching company details, proceeding with limited info');
        }

        // Fallback to user.company_id if there's an error
        if (user.company_id) {
          primaryCompanyId = user.company_id;
          // Don't try to fetch company details again to avoid more rate limit issues
          company_name = `Company ${user.company_id}`;
        }
      }

      // Generate token with the primary company ID
      const token = generateToken({
        userId: user.id,
        role: user.role,
        companyId: primaryCompanyId
      });

      // Create user data with explicit company name handling
      const userData = {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: primaryCompanyId,
        company_name: company_name || (primaryCompanyId ? `Company ${primaryCompanyId}` : null)
      };

      // Log the final user data being returned
      console.log('Login successful, returning user data:', {
        id: userData.id,
        email: userData.email,
        company_id: userData.company_id,
        company_name: userData.company_name,
        role: userData.role
      });

      // Log the final user data being sent
      console.log('Final userData being sent:', userData);

      console.log('Login response data:', userData);

      // Set token as HTTP-only cookie
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });

      return res.json({
        token,
        user: userData
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Refresh token with new company context
  app.post('/api/auth/refresh-token', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { company_id } = req.body;

      console.log('Token refresh requested for company ID:', company_id);

      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = req.user.id;
      const userRole = req.user.role;

      // Validate that the user has access to this company
      if (company_id && company_id !== req.user.company_id) {
        // If not a saas_admin, verify the user is associated with this company
        if (userRole !== 'saas_admin') {
          const userCompanies = await storage.getUserCompanies(userId);
          const hasCompanyAccess = userCompanies.some(uc =>
            uc.company_id === company_id ||
            (uc.company && uc.company.id === company_id)
          );

          if (!hasCompanyAccess) {
            console.log(`User ${userId} does not have access to company ${company_id}`);
            return res.status(403).json({ message: 'Access denied to this company' });
          }
        }
      }

      // Generate a new token with the updated company ID
      const newToken = generateToken({
        userId,
        role: userRole,
        companyId: company_id || req.user.company_id
      });

      // Set the new token as a cookie
      res.cookie('auth_token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });

      console.log(`Token refreshed for user ${userId} with company ${company_id}`);

      return res.json({
        message: 'Token refreshed',
        token: newToken
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.post('/api/auth/register', async (req: Request, res: Response) => {
    try {
      // Extract company data if present
      const { company, subscriptionPlanId, ...userData } = req.body;

      // Validate user data
      const result = registerSchema.safeParse(userData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if email already exists
      const existingUser = await storage.getUserByEmail(result.data.email);
      if (existingUser) {
        return res.status(400).json({ message: 'Email already exists' });
      }

      // Check if email already exists in the same company
      if (result.data.company_id) {
        const existingEmailInCompany = await storage.getUserByEmailAndCompany(result.data.email, result.data.company_id);
        if (existingEmailInCompany) {
          return res.status(400).json({
            message: 'Email already exists in this company',
            error: 'This email is already registered with another user in this company. Please use a different email address.',
            field: 'email'
          });
        }
      }

      let companyId = result.data.company_id;

      // If company data is provided and user role is company_admin, create a new company
      if (company && result.data.role === 'company_admin' && !companyId) {
        try {
          // Validate company data
          const companyResult = insertCompanySchema.safeParse(company);

          if (!companyResult.success) {
            return res.status(400).json({ message: 'Invalid company data', errors: companyResult.error.errors });
          }

          // Create company
          const newCompany = await storage.createCompany(companyResult.data);
          companyId = newCompany.id;

          console.log('Created new company during registration:', newCompany);

          // Initialize the Chart of Accounts system for this company
          try {
            await initializeSystemAccounts(companyId);
            console.log(`Initialized system accounts for new company ${companyId}`);
          } catch (accountsError) {
            console.error(`Failed to initialize system accounts for company ${companyId}:`, accountsError);
            // We don't fail the whole request if account initialization fails
          }

          // If subscription plan ID is provided, create a subscription for the company
          if (subscriptionPlanId) {
            const plan = await storage.getSubscriptionPlan(subscriptionPlanId);

            if (!plan) {
              return res.status(400).json({ message: 'Invalid subscription plan ID' });
            }

            // Calculate subscription dates
            const startDate = new Date();
            const endDate = new Date();

            // Set end date based on billing period
            if (plan.billing_period === 'monthly') {
              endDate.setMonth(endDate.getMonth() + 1);
            } else if (plan.billing_period === 'annual') {
              endDate.setFullYear(endDate.getFullYear() + 1);
            }

            // Create subscription
            const subscription = await storage.createSubscription({
              company_id: companyId,
              plan_id: subscriptionPlanId,
              status: 'active',
              start_date: startDate,
              end_date: endDate,
              billing_cycle: plan.billing_period,
              next_payment_date: endDate,
              payment_method: 'credit_card',
              auto_renew: true
            });

            console.log('Created new subscription during registration:', subscription);
          }
        } catch (error) {
          console.error('Error creating company during registration:', error);
          return res.status(500).json({ message: 'Error creating company' });
        }
      } else if (result.data.company_id) {
        // Verify existing company_id is valid
        const company = await storage.getCompany(result.data.company_id);
        if (!company) {
          return res.status(400).json({ message: 'Invalid company ID' });
        }
      }

      // Create user with the company_id if it was created
      const { confirmPassword, ...finalUserData } = result.data;
      const user = await storage.createUser({
        ...finalUserData,
        company_id: companyId || finalUserData.company_id
      });

      // If user belongs to a company, fetch the company details and create user-company association
      let company_data = null;
      let company_name = null;
      if (user.company_id) {
        try {
          company_data = await storage.getCompany(user.company_id);
          company_name = company_data ? company_data.name : null;
          console.log('Found company for newly registered user:', {
            user_id: user.id,
            company_id: user.company_id,
            company_name,
            company_details: company_data
          });

          // Create user-company association with is_primary=true
          await storage.createUserCompany({
            user_id: user.id,
            company_id: user.company_id,
            is_primary: true,
            role: user.role
          });

          console.log('Created user-company association with primary flag for new user');
        } catch (error) {
          console.error('Error handling company details for new user:', error);
        }
      }

      // Create consistent user data output
      const userResponseData = {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: user.company_id,
        company_name: company_name || (user.company_id ? `Company ${user.company_id}` : null)
      };

      console.log('Registration response data:', userResponseData);

      // Generate token for the newly registered user
      const token = generateToken({
        userId: user.id,
        role: user.role,
        companyId: user.company_id
      });

      // Set token as HTTP-only cookie
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });

      return res.status(201).json({
        message: 'User registered successfully',
        token,
        user: userResponseData
      });
    } catch (error) {
      console.error('Registration error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Company routes
  app.post('/api/companies', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const result = insertCompanySchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const company = await storage.createCompany(result.data);

      // Initialize the Chart of Accounts system for this company
      try {
        await initializeSystemAccounts(company.id);
        console.log(`Initialized system accounts for new company ${company.id}`);
      } catch (accountsError) {
        console.error(`Failed to initialize system accounts for company ${company.id}:`, accountsError);
        // We don't fail the whole request if account initialization fails
        // The accounts can be initialized later or manually
      }

      return res.status(201).json(company);
    } catch (error) {
      console.error('Create company error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companies = await storage.getCompanies();
      return res.json(companies);
    } catch (error) {
      console.error('Get companies error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id, 10);

      console.log(`Company details request for company ${companyId} by user ${req.user?.id}`);

      // Get the company first to make sure it exists
      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        console.log(`User ${req.user?.id} companies:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      return res.json(company);
    } catch (error) {
      console.error('Get company error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get companies that a user belongs to
  app.get('/api/users/:userId/companies', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId, 10);

      // Users can only get their own companies unless they're saas_admin
      if (req.user?.id !== userId && req.user?.role !== 'saas_admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // Get all companies for the user using the user_companies table
      const userCompanies = await storage.getUserCompanies(userId);

      if (userCompanies.length === 0) {
        console.log(`User ${userId} has no companies`);
        return res.json([]);
      }

      // Map the userCompanies to the expected format, including the user_company_id
      const companies = userCompanies.map(uc => ({
        ...uc.company,
        is_primary: uc.is_primary,
        user_company_id: uc.id // Include the association ID for proper "Set Primary" functionality
      }));

      console.log(`Returning ${companies.length} companies for user ${userId}:`, companies);
      return res.json(companies);
    } catch (error) {
      console.error('Get user companies error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Change password route
  app.post('/api/users/:userId/change-password', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const userId = parseInt(req.params.userId, 10);

      // Users can only change their own password
      if (req.user?.id !== userId) {
        return res.status(403).json({ message: 'Forbidden: You can only change your own password' });
      }

      const { current_password, new_password } = req.body;

      if (!current_password || !new_password) {
        return res.status(400).json({ message: 'Current password and new password are required' });
      }

      // Get the user
      const user = await storage.getUser(userId);
      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Verify current password
      const isPasswordValid = await bcrypt.compare(current_password, user.password);
      if (!isPasswordValid) {
        return res.status(401).json({ message: 'Current password is incorrect' });
      }

      // Hash new password
      const saltRounds = 10;
      const hashedPassword = await bcrypt.hash(new_password, saltRounds);

      // Update user's password in storage
      try {
        console.log('Updating password for user:', userId);
        await db.update(users)
          .set({ password: hashedPassword })
          .where(eq(users.id, userId));
        console.log('Password updated successfully');
      } catch (updateError) {
        console.error('Error updating password:', updateError);
        throw updateError;
      }

      return res.status(200).json({ message: 'Password updated successfully' });
    } catch (error) {
      console.error('Error changing password:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/users', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);

      // Ensure user can only access their company's users
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      const users = await storage.getUsersByCompany(companyId);

      return res.json(users);
    } catch (error) {
      console.error('Get company users error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/companies/:id', authMiddleware, requireRole(['saas_admin', 'company_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id, 10);
      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      // If user is company_admin, verify they can only update their own company
      if (req.user?.role === 'company_admin' && req.user.company_id !== companyId) {
        return res.status(403).json({ message: 'Access denied: You can only update your own company' });
      }

      const updatedCompany = await storage.updateCompany(companyId, req.body);

      if (!updatedCompany) {
        return res.status(500).json({ message: 'Failed to update company' });
      }

      return res.json(updatedCompany);
    } catch (error) {
      console.error('Update company error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/companies/:id', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id, 10);
      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      const result = await storage.deleteCompany(companyId);

      if (!result.success) {
        // Check if the company has customers
        if (result.customersCount && result.customersCount > 0) {
          return res.status(400).json({
            message: result.error,
            customersCount: result.customersCount,
            hint: 'Delete all associated customers before deleting this company'
          });
        }

        return res.status(500).json({ message: result.error || 'Failed to delete company' });
      }

      return res.json({ success: true, message: 'Company deleted successfully' });
    } catch (error) {
      console.error('Delete company error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create a new company for a logged-in user
  app.post('/api/user-companies', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Only logged-in users can create companies
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      console.log('Creating company with request body:', req.body);

      // Validate the request body
      const { name, email, phone, address, website } = req.body;

      if (!name || !email) {
        return res.status(400).json({ message: 'Company name and email are required' });
      }

      // Prepare company data
      const companyData = {
        name,
        email,
        phone: phone || null,
        address: address || null,
        website: website || null,
        active: true
      };

      console.log('Preparing to create company with data:', companyData);

      try {
        // Create the company - let the ORM handle the timestamps
        const company = await storage.createCompany(companyData);
        console.log('Successfully created company:', company);

        // Initialize the Chart of Accounts system for this company
        try {
          await initializeSystemAccounts(company.id);
          console.log(`Initialized system accounts for new company ${company.id}`);
        } catch (accountsError) {
          console.error(`Failed to initialize system accounts for company ${company.id}:`, accountsError);
          // We don't fail the whole request if account initialization fails
        }

        // Check if user already has any companies
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const isPrimary = userCompanies.length === 0; // Make this primary if user has no companies

        // Create the user-company association
        const userCompanyData = {
          user_id: req.user.id,
          company_id: company.id,
          role: 'company_admin' as const, // User creating a company becomes its admin
          is_primary: isPrimary // Make primary if this is user's first company
        };

        console.log('Creating user-company association:', userCompanyData);

        // Create the association in the database
        const userCompany = await storage.createUserCompany(userCompanyData);
        console.log('Successfully created user-company association:', userCompany);

        // Return the created company with is_primary flag set appropriately
        return res.status(201).json({
          ...company,
          is_primary: isPrimary
        });
      } catch (dbError) {
        console.error('Database error creating company or user-company association:', dbError);
        return res.status(500).json({ message: 'Error creating company or user-company association' });
      }
    } catch (error) {
      console.error('Error creating user company:', error);
      return res.status(500).json({ message: 'Error creating company', error: error.message });
    }
  });

  // Update company primary status route
  app.patch('/api/user-companies/:id/primary', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      console.log('=== SET PRIMARY REQUEST ===');
      console.log('Request params:', req.params);
      console.log('Request body:', req.body);
      console.log('User:', req.user);

      if (!req.user) {
        console.log('ERROR: Authentication required');
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userCompanyId = parseInt(req.params.id, 10);
      if (isNaN(userCompanyId)) {
        console.log('ERROR: Invalid user-company ID:', req.params.id);
        return res.status(400).json({ message: 'Invalid user-company ID' });
      }

      // Default to true if is_primary is not provided
      let is_primary = true;
      if (req.body && typeof req.body.is_primary === 'boolean') {
        is_primary = req.body.is_primary;
      }

      console.log('Using is_primary value:', is_primary);

      // First, validate that this user-company association exists and user has permissions
      try {
        // Import userCompanies from the schema
        const { userCompanies } = await import('@shared/schema');

        console.log(`Validating user-company association with ID ${userCompanyId} for user ${req.user.id}`);
        const [directUserCompany] = await db
          .select()
          .from(userCompanies)
          .where(eq(userCompanies.id, userCompanyId));

        console.log('Direct DB lookup result for user-company:', directUserCompany);

        if (!directUserCompany) {
          console.log(`User-company with ID ${userCompanyId} not found`);
          return res.status(404).json({ message: 'User-company association not found' });
        }

        // Check if the user has permission to update this user-company
        if (directUserCompany.user_id !== req.user.id && req.user.role !== 'saas_admin') {
          console.log(`Permission denied: User ${req.user.id} cannot update company of user ${directUserCompany.user_id}`);
          return res.status(403).json({ message: 'You do not have permission to update this user-company' });
        }

        console.log('User has permission to update this association');

        // Update the user-company primary status
        console.log(`Calling storage.updateUserCompanyPrimary(${userCompanyId}, ${is_primary})`);
        const updatedUserCompany = await storage.updateUserCompanyPrimary(userCompanyId, is_primary);

        if (!updatedUserCompany) {
          console.log('Storage returned undefined for updated user-company');
          return res.status(500).json({ message: 'Failed to update user-company primary status' });
        }

        console.log('Successfully updated user-company primary status:', updatedUserCompany);
        return res.status(200).json(updatedUserCompany);
      } catch (dbError) {
        console.error('Database error while validating or updating user-company:', dbError);
        return res.status(500).json({
          message: 'Database error occurred',
          error: dbError instanceof Error ? dbError.message : String(dbError)
        });
      }
    } catch (error) {
      console.error('Error updating user-company primary status:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Customer routes
  app.post('/api/customers', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      console.log('Customer creation request received:', {
        user_company_id: req.user?.company_id,
        requested_company_id: req.body.company_id,
        user_role: req.user?.role,
        user_id: req.user?.id
      });

      const result = insertCustomerSchema.safeParse(req.body);

      if (!result.success) {
        console.log('Customer validation failed:', result.error.errors);
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if user has direct access or needs association check
      let hasCompanyAccess = false;

      // Admin or direct company match
      if (req.user?.role === 'saas_admin' || req.user?.company_id === result.data.company_id) {
        hasCompanyAccess = true;
      } else {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        console.log(`User ${req.user?.id} company associations:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        hasCompanyAccess = userCompanies.some(uc =>
          uc.company_id === result.data.company_id ||
          (uc.company && uc.company.id === result.data.company_id)
        );
      }

      // If user has no access to the requested company, deny the request
      if (!hasCompanyAccess) {
        console.log('Access denied: User has no association with requested company:', {
          user_id: req.user?.id,
          requested_company_id: result.data.company_id
        });
        return res.status(403).json({
          message: 'Access denied to this company',
          details: 'You can only create customers for companies you have access to.'
        });
      }

      // Check if a customer with the same phone number already exists for this company
      const existingCustomers = await storage.getCustomersByCompany(result.data.company_id);

      // Normalize the phone number for comparison (always with country code)
      const normalizePhone = (phone: string) => {
        // If the phone number starts with +91, keep it as is
        if (phone.startsWith('+91')) {
          return phone;
        }
        // If it's a 10-digit number without country code, add +91
        if (/^\d{10}$/.test(phone)) {
          return `+91${phone}`;
        }
        // Otherwise, return the original phone number
        return phone;
      };

      const normalizedInputPhone = normalizePhone(result.data.phone);

      // Check for duplicate by comparing normalized phone numbers
      const duplicatePhone = existingCustomers.find(c =>
        normalizePhone(c.phone) === normalizedInputPhone
      );

      if (duplicatePhone) {
        console.log('Duplicate phone number detected:', result.data.phone);
        return res.status(400).json({
          message: 'Phone number already used by a customer.',
          error: 'This phone number is already registered with another customer. Please use a different phone number.',
          field: 'phone'
        });
      }

      console.log('Creating customer for company:', result.data.company_id);

      // Generate company-specific customer reference code
      const companyId = result.data.company_id;

      // Get prefix from company_prefix_settings
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'customer');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for customer`);

      // Get the highest existing customer reference code for this company
      const highestSerial = await getHighestCustomerSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const customerReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated customer reference code: ${customerReferenceCode} for company ${companyId}`);

      // Add the reference code to the customer data
      const customerData = {
        ...result.data,
        customer_reference_code: customerReferenceCode
      };

      console.log(`Creating customer with reference code "${customerReferenceCode}"`);
      const customer = await storage.createCustomer(customerData);
      console.log('Customer created successfully:', customer.id);

      return res.status(201).json(customer);
    } catch (error) {
      console.error('Create customer error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/customers', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);

      // Log the request
      console.log(`Get customers request for company ${companyId} by user ${req.user?.id}`);

      // Check if user has direct company access or needs association check
      if (req.user?.role === 'saas_admin' || req.user?.company_id === companyId) {
        console.log(`Direct access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      } else {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        console.log(`User ${req.user?.id} companies:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Get customers for the specified company only
      // The storage.getCustomersByCompany function now includes additional filtering
      const customers = await storage.getCustomersByCompany(companyId);

      // Log the final result for debugging
      console.log(`Returning ${customers.length} customers for company ${companyId}`);

      // Return the filtered customers
      return res.json(customers);
    } catch (error) {
      console.error('Get customers error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/customers/:customerId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const customerId = parseInt(req.params.customerId, 10);

      // Check if company ID is provided in query parameters first, then body
      let requestedCompanyId: number | null = null;
      if (req.query.companyId) {
        requestedCompanyId = parseInt(req.query.companyId as string, 10);
      } else if (req.body.company_id) {
        requestedCompanyId = parseInt(req.body.company_id, 10);
      }

      console.log('Customer update request received:', {
        customerId,
        user_company_id: req.user?.company_id,
        requested_company_id: requestedCompanyId,
        query_company_id: req.query.companyId,
        body_company_id: req.body.company_id,
        user_role: req.user?.role
      });

      // First verify the customer exists
      const existingCustomer = await storage.getCustomer(customerId);

      if (!existingCustomer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Use the customer's actual company ID for the update
      const actualCompanyId = existingCustomer.company_id;

      // Determine which company ID to use for access control
      // If a specific company ID was requested, use that for access control
      // Otherwise fall back to the user's current company context
      const effectiveCompanyId = requestedCompanyId || req.user!.company_id;

      console.log('Customer update company IDs:', {
        actualCompanyId,
        effectiveCompanyId,
        userCompanyId: req.user!.company_id
      });

      // Check if user has access to this customer's company
      let hasAccess = false;

      // SaaS admins can access any customer
      if (req.user?.role === 'saas_admin') {
        hasAccess = true;
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match with the customer's company
      else if (actualCompanyId === effectiveCompanyId) {
        // Check if user has access to this company
        if (req.user?.company_id === effectiveCompanyId) {
          hasAccess = true;
          console.log(`Access granted: User ${req.user?.id} has direct access to company ${effectiveCompanyId}`);
        } else {
          // Check user's company associations
          const userCompanies = await storage.getUserCompanies(req.user!.id);
          hasAccess = userCompanies.some(uc =>
            uc.company_id === effectiveCompanyId ||
            (uc.company && uc.company.id === effectiveCompanyId)
          );

          if (hasAccess) {
            console.log(`Access granted: User ${req.user?.id} has association with company ${effectiveCompanyId}`);
          }
        }
      }

      if (!hasAccess) {
        console.log(`Access denied: User ${req.user?.id} has no access to customer ${customerId} in company ${actualCompanyId}`);
        return res.status(403).json({ message: 'Access denied to this customer' });
      }

      // Validate the request body
      const validFields = ['full_name', 'email', 'phone', 'address', 'notes', 'active', 'kyc_verified', 'credit_score'];
      const updateData = Object.keys(req.body)
        .filter(key => validFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = req.body[key];
          return obj;
        }, {} as Partial<InsertCustomer>);

      // Always use the existing company ID to avoid changing it
      updateData.company_id = actualCompanyId;

      // If customer_reference_code is not provided in the update, check if the existing customer has one
      if (!updateData.customer_reference_code) {
        // Check if the existing customer has a reference code
        if (!existingCustomer.customer_reference_code || existingCustomer.customer_reference_code.trim() === '') {
          // If not, generate a company-specific reference code
          const companyPrefix = await getCompanyName(actualCompanyId);
          const highestSerial = await getHighestCustomerSerial(actualCompanyId, companyPrefix);
          const nextSerial = highestSerial + 1;
          const serialString = nextSerial.toString().padStart(3, '0');
          const customerReferenceCode = `${companyPrefix}-${serialString}`;

          updateData.customer_reference_code = customerReferenceCode;
          console.log(`Setting customer_reference_code to "${customerReferenceCode}" for customer without a reference code`);
        } else {
          // If it already has a reference code, preserve it
          updateData.customer_reference_code = existingCustomer.customer_reference_code;
        }
      }

      // If phone number is being updated, check if it already exists for another customer
      if (updateData.phone && updateData.phone !== existingCustomer.phone) {
        const existingCustomers = await storage.getCustomersByCompany(actualCompanyId);

        // Normalize the phone number for comparison (always with country code)
        const normalizePhone = (phone: string) => {
          // If the phone number starts with +91, keep it as is
          if (phone.startsWith('+91')) {
            return phone;
          }
          // If it's a 10-digit number without country code, add +91
          if (/^\d{10}$/.test(phone)) {
            return `+91${phone}`;
          }
          // Otherwise, return the original phone number
          return phone;
        };

        const normalizedInputPhone = normalizePhone(updateData.phone);

        // Check for duplicate by comparing normalized phone numbers
        const duplicatePhone = existingCustomers.find(c =>
          normalizePhone(c.phone) === normalizedInputPhone && c.id !== customerId
        );

        if (duplicatePhone) {
          console.log('Duplicate phone number detected during update:', updateData.phone);
          return res.status(400).json({
            message: 'Phone number already in use',
            error: 'This phone number is already registered with another customer. Please use a different phone number.',
            field: 'phone'
          });
        }
      }

      console.log('Updating customer with data:', {
        customerId,
        companyId: actualCompanyId,
        updateData
      });

      // Update the customer
      const updatedCustomer = await storage.updateCustomer(customerId, actualCompanyId, updateData);

      if (!updatedCustomer) {
        return res.status(500).json({ message: "Failed to update customer" });
      }

      return res.status(200).json(updatedCustomer);
    } catch (error) {
      console.error('Update customer error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/customers/:customerId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const customerId = parseInt(req.params.customerId, 10);

      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }

      // Check if a specific company ID was provided in the query parameters
      const requestedCompanyId = req.query.companyId ? parseInt(req.query.companyId as string, 10) : null;

      // Get the customer first
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Get the user's company ID from their context
      const userCompanyId = req.user!.company_id;

      // Determine which company ID to use for access control
      // If a specific company ID was requested, use that for access control
      // Otherwise fall back to the user's current company context
      const effectiveCompanyId = requestedCompanyId || userCompanyId;

      // Log the request for debugging
      console.log('Customer access request:', {
        customerId,
        customerCompanyId: customer.company_id,
        requestedCompanyId,
        userCompanyId,
        effectiveCompanyId,
        userRole: req.user?.role
      });

      // Check if user has access to this customer's company
      let hasAccess = false;

      // SaaS admins can access any customer
      if (req.user?.role === 'saas_admin') {
        hasAccess = true;
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match with the customer's company
      else if (customer.company_id === effectiveCompanyId) {
        hasAccess = true;
        console.log(`Access granted: Customer belongs to company ${effectiveCompanyId}`);
      }
      // Check if the user has access to the customer's company through associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        console.log(`User ${req.user?.id} has access to companies:`, userCompanies.map(uc => uc.company_id));

        hasAccess = userCompanies.some(uc =>
          uc.company_id === customer.company_id ||
          (uc.company && uc.company.id === customer.company_id)
        );

        if (hasAccess) {
          console.log(`Access granted: User ${req.user?.id} has association with company ${customer.company_id}`);
        }
      }

      if (!hasAccess) {
        console.log(`Access denied: User ${req.user?.id} has no access to customer ${customerId} in company ${customer.company_id}`);
        return res.status(403).json({ message: 'Access denied to this customer' });
      }

      console.log(`Access granted: User ${req.user?.id} has access to customer ${customerId}`);
      return res.status(200).json(customer);
    } catch (error) {
      console.error('Get customer error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/customers/:customerId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const customerId = parseInt(req.params.customerId, 10);

      // Check if company ID is provided in query parameters
      let requestedCompanyId: number | null = null;
      if (req.query.companyId) {
        requestedCompanyId = parseInt(req.query.companyId as string, 10);
      }

      if (isNaN(customerId)) {
        return res.status(400).json({ message: "Invalid customer ID" });
      }

      // First verify the customer exists
      const existingCustomer = await storage.getCustomer(customerId);

      if (!existingCustomer) {
        return res.status(404).json({ message: "Customer not found" });
      }

      // Use the customer's actual company ID for the delete operation
      const actualCompanyId = existingCustomer.company_id;

      // Determine which company ID to use for access control
      // If a specific company ID was requested, use that for access control
      // Otherwise fall back to the user's current company context
      const effectiveCompanyId = requestedCompanyId || req.user!.company_id;

      console.log('Customer delete request:', {
        customerId,
        actualCompanyId,
        requestedCompanyId,
        effectiveCompanyId,
        userCompanyId: req.user!.company_id,
        userRole: req.user?.role
      });

      // Check if user has access to this customer's company
      let hasAccess = false;

      // SaaS admins can access any customer
      if (req.user?.role === 'saas_admin') {
        hasAccess = true;
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match with the customer's company
      else if (actualCompanyId === effectiveCompanyId) {
        // Check if user has access to this company
        if (req.user?.company_id === effectiveCompanyId) {
          hasAccess = true;
          console.log(`Access granted: User ${req.user?.id} has direct access to company ${effectiveCompanyId}`);
        } else {
          // Check user's company associations
          const userCompanies = await storage.getUserCompanies(req.user!.id);
          hasAccess = userCompanies.some(uc =>
            uc.company_id === effectiveCompanyId ||
            (uc.company && uc.company.id === effectiveCompanyId)
          );

          if (hasAccess) {
            console.log(`Access granted: User ${req.user?.id} has association with company ${effectiveCompanyId}`);
          }
        }
      }

      if (!hasAccess) {
        console.log(`Access denied: User ${req.user?.id} has no access to customer ${customerId} in company ${actualCompanyId}`);
        return res.status(403).json({ message: 'Access denied to this customer' });
      }

      console.log(`Deleting customer ${customerId} from company ${actualCompanyId}`);
      const result = await storage.deleteCustomer(customerId, actualCompanyId);

      if (!result.success) {
        // Check if the customer has loans
        if (result.loansCount && result.loansCount > 0) {
          return res.status(400).json({
            message: result.error,
            loansCount: result.loansCount,
            hint: 'Delete all associated loans before deleting this customer'
          });
        }

        return res.status(404).json({ message: result.error || "Customer not found or could not be deleted" });
      }

      return res.status(200).json({ success: true, message: "Customer deleted successfully" });
    } catch (error) {
      console.error('Delete customer error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Agent routes
  app.post('/api/agents', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      console.log("Received agent creation data:", JSON.stringify(req.body));

      // Validate phone number format
      const phoneNumber = req.body.phone;
      if (!phoneNumber) {
        return res.status(400).json({ message: 'Phone number is required' });
      }

      // Check if phone number has the correct format (+91 followed by 10 digits)
      const phoneRegex = /^\+91\d{10}$/;
      if (!phoneRegex.test(phoneNumber)) {
        return res.status(400).json({
          message: 'Phone number must be exactly 10 digits with +91 country code',
          field: 'phone'
        });
      }

      // Get all existing agents for this company to check for duplicates
      const existingAgents = await storage.getAgentsByCompany(req.body.company_id);

      // Check for duplicate email or phone in the same company
      const duplicateEmail = existingAgents.find(agent => agent.email === req.body.email);
      const duplicatePhone = existingAgents.find(agent => agent.phone === phoneNumber);

      if (duplicateEmail) {
        console.log(`Agent with email ${req.body.email} already exists in company ${req.body.company_id}`);
        return res.status(400).json({
          message: 'Agent email already in use. Please change it.',
          field: 'email'
        });
      }

      if (duplicatePhone) {
        console.log('Duplicate phone number detected within the same company:', phoneNumber);
        return res.status(400).json({
          message: 'Phone number already in use',
          error: 'Agent already using this phone number. Please change it.',
          field: 'phone'
        });
      }

      // Check if a user with the same email exists globally (not just in this company)
      const existingUserWithEmail = await storage.getUserByEmail(req.body.email);

      // Check if a user with the same phone exists globally
      const existingUserWithPhone = await storage.getUserByPhone(phoneNumber);

      // Determine which existing user to use (if any)
      let existingUser = existingUserWithEmail || existingUserWithPhone;

      // If we found both email and phone matches but they're different users, prioritize email match
      if (existingUserWithEmail && existingUserWithPhone &&
          existingUserWithEmail.id !== existingUserWithPhone.id) {
        existingUser = existingUserWithEmail;
        console.log(`Found different users with matching email and phone. Using email match: ${existingUserWithEmail.id}`);
      }

      // If a user with this email/phone exists globally but not in this company, we need to add a company association
      let newUser;
      if (existingUser) {
        // Check if this user is already an agent in this company
        const existingAgentInCompany = await storage.getAgentByUserAndCompany(existingUser.id, req.body.company_id);

        if (existingAgentInCompany) {
          console.log(`User ${existingUser.id} is already an agent in company ${req.body.company_id}`);
          return res.status(400).json({
            message: 'This user is already an agent in your company',
            field: 'email'
          });
        }

        // Check if this user is already an agent in another company
        const existingAgentCompanies = await storage.getAgentsByUserId(existingUser.id);

        if (existingAgentCompanies && existingAgentCompanies.length > 0) {
          // User is already an agent in another company, create a new user instead
          console.log(`User ${existingUser.id} is already an agent in another company. Creating a new user.`);

          const userData = {
            username: req.body.email,
            email: req.body.email,
            password: Math.random().toString(36).slice(-8), // Generate a random password
            role: 'agent',
            full_name: req.body.full_name,
            phone: phoneNumber,
            company_id: req.body.company_id // Set company_id for the user
          };

          const userResult = insertUserSchema.safeParse(userData);

          if (!userResult.success) {
            console.log("User validation errors:", userResult.error.errors);
            return res.status(400).json({ message: 'Invalid input for user', errors: userResult.error.errors });
          }

          // Create a new user
          newUser = await storage.createUser(userResult.data);
        } else {
          // User exists but is not an agent, associate them with this company
          console.log(`User ${existingUser.id} exists but is not an agent. Associating with company ${req.body.company_id}`);
          newUser = existingUser;

          // Add company association if it doesn't exist
          const existingAssociation = await storage.getUserCompanyByIds(newUser.id, req.body.company_id);
          if (!existingAssociation) {
            await storage.createUserCompany({
              user_id: newUser.id,
              company_id: req.body.company_id,
              role: 'agent'
            });
          }
        }
      } else {
        // Create a new user for the agent
        console.log(`No existing user found. Creating a new user for agent in company ${req.body.company_id}`);
        const userData = {
          username: req.body.email,
          email: req.body.email,
          password: Math.random().toString(36).slice(-8), // Generate a random password
          role: 'agent',
          full_name: req.body.full_name,
          phone: phoneNumber,
          company_id: req.body.company_id // Set company_id for the user
        };

        const userResult = insertUserSchema.safeParse(userData);

        if (!userResult.success) {
          console.log("User validation errors:", userResult.error.errors);
          return res.status(400).json({ message: 'Invalid input for user', errors: userResult.error.errors });
        }

        // Create the user
        newUser = await storage.createUser(userResult.data);
      }

      // Now create the agent with the user ID
      let validatedAgentData;
      try {
        // Generate company-specific agent reference code
        const companyId = req.body.company_id;

        // Get prefix from company_prefix_settings
        const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'agent');
        console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for agent`);

        // Get the highest existing agent reference code for this company
        const highestSerial = await storage.getHighestAgentSerial(companyId, prefix);
        // Use the higher of the highest existing serial or the start number from settings
        const nextSerial = Math.max(highestSerial + 1, startNumber);
        const serialString = nextSerial.toString().padStart(3, '0');
        const agentReferenceCode = `${prefix}-${serialString}`;

        console.log(`Generated agent reference code: ${agentReferenceCode} for company ${companyId}`);

        const agentData = {
          company_id: req.body.company_id,
          user_id: newUser.id,
          commission_rate: req.body.commission_rate,
          territory: req.body.territory,
          active: req.body.active !== false,
          notes: req.body.notes,
          agent_reference_code: agentReferenceCode
        };

        console.log("Agent data before validation:", JSON.stringify(agentData));

        const agentResult = insertAgentSchema.safeParse(agentData);

        if (!agentResult.success) {
          console.log("Agent validation errors:", agentResult.error.errors);
          // If agent creation fails and we created a new user, delete the user
          if (!existingUser) {
            await storage.deleteUser(newUser.id);
          }
          return res.status(400).json({ message: 'Invalid input for agent', errors: agentResult.error.errors });
        }

        validatedAgentData = agentResult.data;
        console.log("Agent data after validation:", JSON.stringify(validatedAgentData));
      } catch (validationError) {
        console.error("Error during agent data validation:", validationError);
        // If there's an error during validation and we created a new user, delete the user
        if (!existingUser) {
          await storage.deleteUser(newUser.id);
        }
        return res.status(400).json({ message: 'Error validating agent data. Please check the commission_rate field.' });
      }

      // Ensure user can only create agents for their company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== validatedAgentData.company_id) {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === validatedAgentData.company_id ||
          (uc.company && uc.company.id === validatedAgentData.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${validatedAgentData.company_id}`);
          // Also delete the user we created if it's a new user
          if (!existingUser) {
            await storage.deleteUser(newUser.id);
          }
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${validatedAgentData.company_id}`);
      }

      // Final check if this user is already an agent in this company
      const existingAgent = await storage.getAgentByUserAndCompany(newUser.id, req.body.company_id);

      if (existingAgent) {
        console.log(`User ${newUser.id} is already an agent in company ${req.body.company_id}`);
        return res.status(400).json({
          message: 'This user is already an agent in your company',
          field: 'email'
        });
      }

      const agent = await storage.createAgent(validatedAgentData);

      // Return the agent with the user data
      return res.status(201).json({
        ...agent,
        full_name: newUser.full_name,
        email: newUser.email,
        phone: newUser.phone
      });
    } catch (error) {
      console.error('Create agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/agents', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const agents = await storage.getAgentsByCompany(companyId);
      return res.json(agents);
    } catch (error) {
      console.error('Get agents error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/agents/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const agentId = parseInt(req.params.agentId, 10);
      const companyId = parseInt(req.body.company_id, 10);

      // Ensure user can only update agents from their company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Get the current agent and user for validation
      const currentAgent = await storage.getAgent(agentId);
      if (!currentAgent) {
        return res.status(404).json({ message: 'Agent not found' });
      }

      // Get the user associated with this agent
      const currentUser = await storage.getUser(currentAgent.user_id);
      if (!currentUser) {
        return res.status(404).json({ message: 'User associated with this agent not found' });
      }

      // Get users associated with this company through the user_companies table
      const userCompanies = await storage.getUserCompanies(req.user!.id);
      const companyUserIds = userCompanies
        .filter(uc => uc.company_id === companyId)
        .map(uc => uc.user_id);

      console.log(`Found ${companyUserIds.length} users associated with company ${companyId} for validation checks.`);

      // Validate email if it's being updated
      if (req.body.email && req.body.email !== currentUser.email) {
        const email = req.body.email;

        // Check for duplicate email only among users in the same company (excluding the current user)
        const duplicateEmail = await storage.getUserByEmailAndCompany(email, companyId);

        if (duplicateEmail && duplicateEmail.id !== currentUser.id) {
          console.log(`Duplicate email detected within the same company: ${email}`);
          return res.status(400).json({
            message: 'Email already in use',
            error: 'This email is already registered with another user in your company. Please use a different email address.',
            field: 'email'
          });
        }
      }

      // Validate phone number if it's being updated
      if (req.body.phone && req.body.phone !== currentUser.phone) {
        const phoneNumber = req.body.phone;

        // Check if phone number has the correct format (+91 followed by 10 digits)
        const phoneRegex = /^\+91\d{10}$/;
        if (!phoneRegex.test(phoneNumber)) {
          return res.status(400).json({
            message: 'Phone number must be exactly 10 digits with +91 country code',
            field: 'phone'
          });
        }

        // Check for duplicate phone number only among users in the same company (excluding the current user)
        const duplicatePhone = await storage.getUserByPhoneAndCompany(phoneNumber, companyId);

        // If the duplicate phone belongs to the current user, it's not a duplicate
        const isDuplicate = duplicatePhone && duplicatePhone.id !== currentUser.id;

        if (isDuplicate) {
          console.log('Duplicate phone number detected within the same company:', phoneNumber);
          return res.status(400).json({
            message: 'Phone number already in use',
            error: 'This phone number is already registered with another user in your company. Please use a different phone number.',
            field: 'phone'
          });
        }
      }

      // Validate the request body for agent fields
      const validAgentFields = ['company_id', 'user_id', 'commission_rate', 'territory', 'active', 'notes'];

      // Also allow user fields to be updated
      const validUserFields = ['full_name', 'email', 'phone'];

      // Create update data object with all valid fields
      const updateData = Object.keys(req.body)
        .filter(key => [...validAgentFields, ...validUserFields].includes(key))
        .reduce((obj, key) => {
          obj[key] = req.body[key];
          return obj;
        }, {} as Partial<InsertAgent> & { full_name?: string, email?: string, phone?: string });

      // Get the existing agent to retrieve the user_id if not provided
      if (!updateData.user_id) {
        const existingAgent = await storage.getAgent(agentId);
        if (existingAgent) {
          updateData.user_id = existingAgent.user_id;
        }
      }

      const updatedAgent = await storage.updateAgent(agentId, companyId, updateData);
      if (!updatedAgent) {
        return res.status(404).json({ message: "Agent not found" });
      }

      return res.status(200).json(updatedAgent);
    } catch (error) {
      console.error('Update agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/agents/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const agentId = parseInt(req.params.agentId, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(agentId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid agent ID or company ID" });
      }

      // Ensure user can only delete agents from their company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const success = await storage.deleteAgent(agentId, companyId);
      if (!success) {
        return res.status(404).json({ message: "Agent not found or could not be deleted. The agent may have associated collections." });
      }

      return res.status(200).json({ success: true, message: "Agent deleted successfully" });
    } catch (error) {
      console.error('Delete agent error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Loan routes
  app.post('/api/loans', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Manually convert string dates to Date objects before validation
      const requestData = {
        ...req.body,
        start_date: req.body.start_date ? new Date(req.body.start_date) : undefined,
        end_date: req.body.end_date ? new Date(req.body.end_date) : undefined
      };

      console.log('Processing loan data:', requestData);
      const result = insertLoanSchema.safeParse(requestData);

      if (!result.success) {
        console.log('Validation errors:', result.error.errors);
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Ensure user can only create loans for their company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== result.data.company_id) {
        // Check if user has access through company associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === result.data.company_id ||
          (uc.company && uc.company.id === result.data.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${result.data.company_id}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Verify that the customer exists and belongs to the specified company
      const customer = await storage.getCustomer(result.data.customer_id);

      if (!customer) {
        console.log(`Customer not found: ID ${result.data.customer_id}`);
        return res.status(400).json({ message: 'Customer not found' });
      }

      // Strict validation to ensure customer belongs to the specified company
      if (customer.company_id !== result.data.company_id) {
        console.log(`Customer company mismatch: Customer ${result.data.customer_id} belongs to company ${customer.company_id}, but loan is for company ${result.data.company_id}`);
        return res.status(400).json({
          message: 'Customer does not belong to the specified company',
          details: `Customer belongs to company ${customer.company_id}, but loan is for company ${result.data.company_id}`
        });
      }

      console.log(`Verified customer ${customer.id} (${customer.full_name}) belongs to company ${result.data.company_id}`);

      // Generate company-specific loan reference code
      const companyId = result.data.company_id;

      // Get prefix from company_prefix_settings
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'loan');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for loan`);

      // Get the highest existing loan reference code for this company
      const loanStorage = new LoanStorage();
      const highestSerial = await loanStorage.getHighestLoanSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const loanReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated loan reference code: ${loanReferenceCode} for company ${companyId}`);

      // Add the reference code to the loan data
      const loanDataWithReferenceCode = {
        ...result.data,
        loan_reference_code: loanReferenceCode
      };

      console.log(`Creating loan with reference code "${loanReferenceCode}"`);
      const loan = await storage.createLoan(loanDataWithReferenceCode);
      console.log(`Loan created with ID: ${loan.id} for company ${loan.company_id} and reference code: ${loan.loan_reference_code}`);

      // Automatically generate collections for the loan
      try {
        console.log(`[AUTO-COLLECTIONS] Automatically generating collections for loan ${loan.id} (company ${loan.company_id})`);
        console.log(`[AUTO-COLLECTIONS] Loan details:`, JSON.stringify({
          id: loan.id,
          company_id: loan.company_id,
          customer_id: loan.customer_id,
          amount: loan.amount,
          interest_rate: loan.interest_rate,
          term: loan.term,
          terms_frequency: loan.terms_frequency,
          payment_frequency: loan.payment_frequency,
          start_date: loan.start_date,
          status: loan.status
        }));

        // Import necessary dependencies
        const { db } = await import('./db');
        const { collections } = await import('@shared/schema');

        // Calculate payment amount based on loan details
        const amount = parseFloat(loan.amount);
        const interestRate = parseFloat(loan.interest_rate);
        const term = loan.term;
        const paymentFrequency = loan.payment_frequency || 'monthly';
        const interestType = loan.interest_type || 'flat';
        const isUpfrontInterest = loan.is_upfront_interest === true;
        const startDate = new Date(loan.start_date);

        // Calculate payment amount
        let paymentAmount = 0;
        if (interestType === 'flat') {
          // For flat interest, use only principal for collections to match payment schedule
          paymentAmount = amount / term;
        } else if (interestType === 'reducing') {
          // For reducing interest, use only principal for collections to match payment schedule
          paymentAmount = amount / term;
        } else if (isUpfrontInterest) {
          // For upfront interest, the interest is deducted from the principal upfront
          paymentAmount = amount / term;
        } else {
          // Default to simple division if interest type is not specified
          paymentAmount = amount / term;
        }

        // Round payment amount to 2 decimal places
        paymentAmount = Math.round(paymentAmount * 100) / 100;
        console.log(`[AUTO-COLLECTIONS] Calculated payment amount: ${paymentAmount}`);

        // Get the company name first
        const companyName = await getCompanyName(loan.company_id);
        console.log(`[AUTO-COLLECTIONS] Using company name: ${companyName} for collections`);

        // Generate collection records
        const pendingCollections = [];
        for (let i = 0; i < term; i++) {
          // Calculate due date based on payment frequency
          const dueDate = new Date(startDate);

          // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
          if (paymentFrequency === 'daily') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
          } else if (paymentFrequency === 'weekly') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
          } else if (paymentFrequency === 'biweekly') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
          } else if (paymentFrequency === 'monthly') {
            dueDate.setMonth(dueDate.getMonth() + (i+1));
          } else {
            // Default to monthly if not specified
            dueDate.setMonth(dueDate.getMonth() + (i+1));
          }

          pendingCollections.push({
            company_id: loan.company_id,
            loan_id: loan.id,
            customer_id: loan.customer_id,
            agent_id: null, // Can be assigned later
            amount: paymentAmount.toString(),
            original_amount: paymentAmount.toString(), // Store original amount
            scheduled_date: dueDate, // Use Date object directly
            collection_date: null,
            status: 'pending' as const, // Create as pending
            payment_method: null,
            receipt_id: null,
            notes: `Auto-generated for payment #${i + 1}`,
            emi_number: i + 1,
            time_of_day: loan.payment_time_of_day || null,
            fine_amount: '0',
            company_collection_string: companyName
          });
        }

        // --- Begin: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---
        // Get prefix from company_prefix_settings
        const { prefix, startNumber } = await getPrefixFromSettings(loan.company_id, 'collection');
        console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for collection`);

        const storageModule = await import('./storage/collection.storage');
        const collectionStorage = new storageModule.CollectionStorage();
        let highestSerial = await collectionStorage.getHighestCollectionSerial(loan.company_id, prefix);
        // Use the higher of the highest existing serial or the start number from settings
        let nextSerial = Math.max(highestSerial + 1, startNumber);
        for (let i = 0; i < pendingCollections.length; i++) {
          const serial = nextSerial.toString().padStart(3, '0');
          pendingCollections[i].company_collection_string = `${prefix}-${serial}`;
          nextSerial++;
        }
        // --- End: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---

        console.log('[AUTO-COLLECTIONS] Inserting collections:', JSON.stringify(pendingCollections));

        // Insert all pending collections in a batch
        const createdCollections = await db.insert(collections).values(pendingCollections).returning();
        console.log(`[AUTO-COLLECTIONS] Successfully created ${createdCollections.length} collection records`);

        console.log('[AUTO-COLLECTIONS] Successfully completed automatic collection generation');
      } catch (error) {
        console.error('[AUTO-COLLECTIONS] Error in automatic collection generation:', error);
      }

      // Create double-entry accounting records for the loan disbursement
      try {
        // First check if system accounts are initialized
        const companyAccounts = await storage.getAccountsByCompany(loan.company_id);

        if (!companyAccounts || companyAccounts.length === 0) {
          console.log(`No accounts found for company ${loan.company_id}, initializing system accounts...`);
          // Initialize system accounts for the company
          await initializeSystemAccounts(loan.company_id, req.user?.id);
          console.log(`System accounts initialized for company ${loan.company_id}`);
        }

        // Get the cash/bank account and loan receivable account with fallback names
        const cashAccount = await getAccountByCode(loan.company_id, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
        // If Cash account not found, try Bank account
        const cashOrBankAccount = cashAccount || await getAccountByCode(loan.company_id, SYSTEM_ACCOUNT_CODES.BANK, "Bank");
        const loanReceivableAccount = await getAccountByCode(loan.company_id, SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE, "Loan Receivable");

        console.log(`Cash/Bank account found: ${!!cashOrBankAccount}, ID: ${cashOrBankAccount?.id}, Name: ${cashOrBankAccount?.account_name}`);
        console.log(`Loan Receivable account found: ${!!loanReceivableAccount}, ID: ${loanReceivableAccount?.id}, Name: ${loanReceivableAccount?.account_name}`);

        if (!cashOrBankAccount || !loanReceivableAccount) {
          console.error(`Required accounts not found for company ${loan.company_id} after initialization attempt`);
          return res.status(201).json({
            ...loan,
            warning: "Loan created but financial records could not be generated due to missing chart of accounts"
          });
        } else {
          // Create a journal entry for the loan disbursement
          const journalAmount = Number(loan.disbursement_amount || loan.amount);

          console.log(`Creating journal entry for loan ${loan.id} with amount: ${journalAmount}`);
          console.log(`Credit account: ${cashOrBankAccount.id} (${cashOrBankAccount.account_name})`);
          console.log(`Debit account: ${loanReceivableAccount.id} (${loanReceivableAccount.account_name})`);

          console.log(`Attempting to create journal entry for loan ${loan.id} (Amount: ${journalAmount})`);

          // Debug information for accounts
          console.log(`Using accounts for journal entry:`);
          console.log(`- Credit (Cash/Bank): ID ${cashOrBankAccount.id}, Name: ${cashOrBankAccount.account_name}, Code: ${cashOrBankAccount.account_code}`);
          console.log(`- Debit (Loan Receivable): ID ${loanReceivableAccount.id}, Name: ${loanReceivableAccount.account_name}, Code: ${loanReceivableAccount.account_code}`);

          const journalEntry = await createJournalEntry({
            company_id: loan.company_id,
            branch_id: loan.branch_id || null,
            transaction_date: loan.start_date || new Date(),
            description: `Loan disbursement - ${loan.id}`,
            reference_type: 'loan',
            reference_id: loan.id,
            created_by: req.user?.id,
            entries: {
              // Cash/Bank decreases (credit)
              credit: {
                account_id: cashOrBankAccount.id,
                amount: journalAmount
              },
              // Loan Receivable increases (debit)
              debit: {
                account_id: loanReceivableAccount.id,
                amount: journalAmount
              }
            }
          });

          console.log(`Journal entry created successfully, transaction IDs: ${journalEntry.map(t => t.id).join(', ')}`);
          console.log(`Journal transactions: ${journalEntry.length} (${journalEntry.filter(t => t.transaction_type === 'debit').length} debits, ${journalEntry.filter(t => t.transaction_type === 'credit').length} credits)`);


          console.log(`Created journal entry for loan disbursement ${loan.id}`);
          return res.status(201).json(loan);
        }
      } catch (journalError) {
        console.error('Failed to create journal entry for loan disbursement:', journalError);
        console.error('Journal entry error details:', (journalError as Error).message);
        console.error('Journal entry error stack:', (journalError as Error).stack);

        // Check for specific error types to provide better feedback
        const errorMessage = (journalError as Error).message;
        let warningMessage = "Loan created but financial records could not be generated. Please contact support.";

        if (errorMessage.includes('account')) {
          warningMessage = "Loan created but financial records could not be generated due to missing or invalid accounts.";
        } else if (errorMessage.includes('amount')) {
          warningMessage = "Loan created but financial records could not be generated due to invalid amount.";
        } else if (errorMessage.includes('no company_id')) {
          warningMessage = "Loan created but financial records could not be generated due to missing company information.";
        }

        // We don't fail the loan creation if the journal entry fails
        return res.status(201).json({
          ...loan,
          warning: warningMessage,
          error_details: errorMessage // Include more details for debugging
        });
      }
    } catch (error) {
      console.error('Create loan error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/customers/:customerId/loans', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const customerId = parseInt(req.params.customerId, 10);
      const companyId = req.user?.company_id;

      if (!companyId && req.user?.role !== 'saas_admin') {
        return res.status(403).json({ message: 'Access denied' });
      }

      // For saas_admin, company_id should be provided as query param
      const effectiveCompanyId = req.user?.role === 'saas_admin'
        ? parseInt(req.query.company_id as string, 10)
        : companyId;

      if (!effectiveCompanyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const loans = await storage.getLoansByCustomer(customerId, effectiveCompanyId);

      return res.json(loans);
    } catch (error) {
      console.error('Get loans error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get all loans for a company
  app.get('/api/companies/:companyId/loans', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);

      // Ensure user has access to this company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const loans = await storage.getLoansByCompany(companyId);

      return res.json(loans);
    } catch (error) {
      console.error('Get company loans error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get a specific loan by ID
  app.get('/api/loans/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const loanId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string || req.user?.company_id?.toString() || '0', 10);

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      // Ensure user has access to this company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const loan = await storage.getLoan(loanId, companyId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      return res.json(loan);
    } catch (error) {
      console.error('Get loan error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update all loan reference codes to "''"
  app.post('/api/loans/update-reference-codes', authMiddleware, requireRole(['saas_admin', 'company_admin']), async (req: AuthRequest, res: Response) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Get company ID from request or user context
      const companyId = req.body.company_id || req.user.company_id;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      // Ensure user has access to this company
      if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Get all loans for the company
      const companyLoans = await storage.getLoansByCompany(companyId);
      console.log(`Found ${companyLoans.length} loans for company ${companyId}`);

      if (companyLoans.length === 0) {
        return res.status(200).json({
          message: 'No loans found for this company',
          updated: 0
        });
      }

      // Get company prefix for loan reference codes
      const companyPrefix = await getCompanyName(companyId);
      console.log(`Generated company prefix for loan reference codes: ${companyPrefix}`);

      // Create a LoanStorage instance to use getHighestLoanSerial
      const loanStorage = new LoanStorage();

      // Update each loan with a company-specific reference code
      let updatedCount = 0;
      const errors = [];
      let highestSerial = await loanStorage.getHighestLoanSerial(companyId, companyPrefix);

      for (const loan of companyLoans) {
        try {
          // Only update loans that don't already have a reference code or have an empty reference code
          if (!loan.loan_reference_code || loan.loan_reference_code.trim() === '') {
            // Generate the next sequential reference code
            highestSerial++;
            const serialString = highestSerial.toString().padStart(3, '0');
            const loanReferenceCode = `${companyPrefix}-${serialString}`;

            console.log(`Generating reference code ${loanReferenceCode} for loan ID ${loan.id}`);

            await storage.updateLoan(
              loan.id,
              companyId,
              { loan_reference_code: loanReferenceCode }
            );

            updatedCount++;
          }
        } catch (error) {
          console.error(`Error updating loan ID ${loan.id}:`, error);
          errors.push({
            loanId: loan.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return res.status(200).json({
        message: `Updated ${updatedCount} loans with company-specific reference codes`,
        totalLoans: companyLoans.length,
        updatedLoans: updatedCount,
        companyPrefix: companyPrefix,
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error updating loan reference codes:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Update a loan
  app.patch('/api/loans/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Parse and validate IDs
      const loanId = parseInt(req.params.id, 10);
      if (isNaN(loanId)) {
        return res.status(400).json({ message: 'Invalid loan ID format' });
      }

      // Get company ID from request body
      const companyId = parseInt(req.body.company_id, 10);
      if (isNaN(companyId)) {
        return res.status(400).json({ message: 'Invalid or missing company ID' });
      }

      // Check if the loan exists first
      const existingLoan = await storage.getLoan(loanId);
      if (!existingLoan) {
        console.log(`Loan with ID ${loanId} not found`);
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Verify that the loan belongs to the specified company
      if (existingLoan.company_id !== companyId) {
        console.log(`Company mismatch: Loan belongs to company ${existingLoan.company_id}, but request is for company ${companyId}`);
        return res.status(403).json({ message: 'Access denied: Loan does not belong to specified company' });
      }

      // Ensure user can only update loans from companies they have access to
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        // For non-saas_admin users, check if they have access to the company through associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      console.log('Update loan request body:', req.body);

      // Validate the request body - add term_frequency field
      const validFields = [
        'company_id',
        'customer_id',
        'amount',
        'interest_rate',
        'interest_type',
        'term',
        'terms_frequency',
        'is_upfront_interest',
        'start_date',
        'end_date',
        'status',
        'notes',
        'disbursed_amount',
        'total_repayable',
        'installment_amount',
        'loan_reference_code'
      ];

      // Log all received fields for debugging
      console.log('All received fields:', Object.keys(req.body));

      // Process date fields and make a clean update object
      const rawUpdateData = Object.keys(req.body)
        .filter(key => validFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = req.body[key];
          return obj;
        }, {} as Record<string, any>);

      // Always convert date strings to Date objects, regardless of format
      if (rawUpdateData.start_date) {
        try {
          // Force conversion to date object
          rawUpdateData.start_date = new Date(rawUpdateData.start_date);
          console.log('Converted start_date to Date:', rawUpdateData.start_date);
        } catch (err) {
          console.error('Error converting start_date:', err);
          return res.status(400).json({ message: 'Invalid start date format' });
        }
      }

      if (rawUpdateData.end_date) {
        try {
          // Force conversion to date object
          rawUpdateData.end_date = new Date(rawUpdateData.end_date);
          console.log('Converted end_date to Date:', rawUpdateData.end_date);
        } catch (err) {
          console.error('Error converting end_date:', err);
          return res.status(400).json({ message: 'Invalid end date format' });
        }
      }

      // Always keep amount and interest_rate as strings
      if (rawUpdateData.amount) {
        rawUpdateData.amount = String(rawUpdateData.amount);
      }
      if (rawUpdateData.interest_rate) {
        rawUpdateData.interest_rate = String(rawUpdateData.interest_rate);
      }

      // Ensure is_upfront_interest is a boolean value
      if ('is_upfront_interest' in rawUpdateData) {
        if (typeof rawUpdateData.is_upfront_interest === 'string') {
          rawUpdateData.is_upfront_interest = rawUpdateData.is_upfront_interest === 'true';
        }
        console.log('Processed is_upfront_interest:', rawUpdateData.is_upfront_interest);
      }

      // Convert term to number explicitly
      if (rawUpdateData.term) {
        rawUpdateData.term = Number(rawUpdateData.term);
      }

      // Ensure other calculated amounts are stored as strings
      ['disbursed_amount', 'total_repayable', 'installment_amount'].forEach(field => {
        if (rawUpdateData[field]) {
          rawUpdateData[field] = String(rawUpdateData[field]);
        }
      });

      console.log('Final update data to be sent to storage:', rawUpdateData);

      const updateData = rawUpdateData as Partial<InsertLoan>;

      // If loan_reference_code is not provided in the update, check if the existing loan has one
      if (!updateData.loan_reference_code) {
        // Check if the existing loan has a reference code
        if (!existingLoan.loan_reference_code || existingLoan.loan_reference_code.trim() === '') {
          // If not, generate a company-specific reference code
          const companyPrefix = await getCompanyName(companyId);
          const loanStorage = new LoanStorage();
          const highestSerial = await loanStorage.getHighestLoanSerial(companyId, companyPrefix);
          const nextSerial = highestSerial + 1;
          const serialString = nextSerial.toString().padStart(3, '0');
          const loanReferenceCode = `${companyPrefix}-${serialString}`;

          updateData.loan_reference_code = loanReferenceCode;
          console.log(`Setting loan_reference_code to "${loanReferenceCode}" for loan without a reference code`);
        }
      }

      // Update the loan with proper company ownership verification
      const updatedLoan = await storage.updateLoan(loanId, companyId, updateData);
      if (!updatedLoan) {
        console.log(`No loan returned after update for ID ${loanId}`);
        return res.status(404).json({ message: "Loan not found or update failed" });
      }

      // Check if the loan status was changed to active or approved
      if (updateData.status && (updateData.status === 'active' || updateData.status === 'approved')) {
        console.log(`[AUTO-COLLECTIONS] Loan status changed to ${updateData.status}, checking if collections need to be generated`);

        // Check if collections already exist
        const existingCollections = await storage.getCollectionsByLoan(loanId, companyId);

        if (!existingCollections || existingCollections.length === 0) {
          console.log(`[AUTO-COLLECTIONS] No collections found for loan ${loanId}, generating them automatically`);

          try {
            // Import necessary dependencies
            const { db } = await import('./db');
            const { collections } = await import('@shared/schema');

            // Get the loan details
            const loanDetails = await storage.getLoan(loanId, companyId);
            if (!loanDetails) {
              throw new Error(`Loan with ID ${loanId} not found`);
            }

            // Calculate payment amount based on loan details
            const amount = parseFloat(loanDetails.amount);
            const interestRate = parseFloat(loanDetails.interest_rate);
            const term = loanDetails.term;
            const paymentFrequency = loanDetails.payment_frequency || 'monthly';
            const interestType = loanDetails.interest_type || 'flat';
            const isUpfrontInterest = loanDetails.is_upfront_interest === true;
            const startDate = new Date(loanDetails.start_date);

            // Calculate payment amount
            let paymentAmount = 0;
            if (interestType === 'flat') {
              // For flat interest, use only principal for collections to match payment schedule
              paymentAmount = amount / term;
            } else if (interestType === 'reducing') {
              // For reducing interest, use only principal for collections to match payment schedule
              paymentAmount = amount / term;
            } else if (isUpfrontInterest) {
              // For upfront interest, the interest is deducted from the principal upfront
              paymentAmount = amount / term;
            } else {
              // Default to simple division if interest type is not specified
              paymentAmount = amount / term;
            }

            // Round payment amount to 2 decimal places
            paymentAmount = Math.round(paymentAmount * 100) / 100;
            console.log(`[AUTO-COLLECTIONS] Calculated payment amount: ${paymentAmount}`);

            // Get prefix from company_prefix_settings for collection string
            const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'collection');
            console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for collection`);

            // Generate collection records
            const pendingCollections = [];
            for (let i = 0; i < term; i++) {
              // Calculate due date based on payment frequency
              const dueDate = new Date(startDate);

              // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
              if (paymentFrequency === 'daily') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
              } else if (paymentFrequency === 'weekly') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
              } else if (paymentFrequency === 'biweekly') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
              } else if (paymentFrequency === 'monthly') {
                dueDate.setMonth(dueDate.getMonth() + (i+1));
              } else {
                // Default to monthly if not specified
                dueDate.setMonth(dueDate.getMonth() + (i+1));
              }

              pendingCollections.push({
                company_id: loanDetails.company_id,
                loan_id: loanDetails.id,
                customer_id: loanDetails.customer_id,
                agent_id: null, // Can be assigned later
                amount: paymentAmount.toString(),
                original_amount: paymentAmount.toString(), // Store original amount
                scheduled_date: dueDate, // Use Date object directly
                collection_date: null,
                status: 'pending' as const, // Create as pending
                payment_method: null,
                receipt_id: null,
                notes: `Auto-generated for payment #${i + 1}`,
                emi_number: i + 1,
                time_of_day: loanDetails.payment_time_of_day || null,
                fine_amount: '0',
                company_collection_string: '' // Will be set later with proper serial number
              });
            }

            // --- Begin: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---
            const storageModule = await import('./storage/collection.storage');
            const collectionStorage = new storageModule.CollectionStorage();
            let highestSerial = await collectionStorage.getHighestCollectionSerial(companyId, prefix);
            // Use the higher of the highest existing serial or the start number from settings
            let nextSerial = Math.max(highestSerial + 1, startNumber);
            for (let i = 0; i < pendingCollections.length; i++) {
              const serial = nextSerial.toString().padStart(3, '0');
              pendingCollections[i].company_collection_string = `${prefix}-${serial}`;
              nextSerial++;
            }
            // --- End: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---

            // Insert all pending collections
            const createdCollections = await db.insert(collections).values(pendingCollections).returning();
            console.log(`[AUTO-COLLECTIONS] Successfully created ${createdCollections.length} collection records`);

            console.log('[AUTO-COLLECTIONS] Successfully completed automatic collection generation');
          } catch (error) {
            console.error('[AUTO-COLLECTIONS] Error in automatic collection generation:', error);
          }
        } else {
          console.log(`[AUTO-COLLECTIONS] Collections already exist for loan ${loanId}, skipping automatic generation`);
        }
      }

      console.log('Loan successfully updated:', updatedLoan);
      return res.status(200).json(updatedLoan);
    } catch (error) {
      console.error('Update loan error:', error);
      return res.status(500).json({
        message: 'Server error during loan update',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Delete a loan
  app.delete('/api/loans/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const loanId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);
      const forceDelete = req.query.forceDelete === 'true';

      if (isNaN(loanId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid loan ID or company ID" });
      }

      // Ensure user can only delete loans from companies they have access to
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        // For non-saas_admin users, check if they have access to the company through associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      let result;

      if (forceDelete) {
        // Force delete - also removes associated collections
        result = await storage.deleteLoanWithCollections(loanId, companyId);

        if (!result.success) {
          const errorMessage = result.error || "Failed to delete loan and its collections";
          return res.status(404).json({
            message: errorMessage,
            error: result.error
          });
        }

        return res.status(200).json({
          success: true,
          message: `Loan deleted successfully along with ${result.collectionsDeleted || 0} associated collections`
        });
      } else {
        // Standard delete - only works if no collections exist
        result = await storage.deleteLoan(loanId, companyId);

        if (!result.success) {
          // If there are collections, provide a specific message
          if (result.collectionsCount && result.collectionsCount > 0) {
            return res.status(409).json({
              message: `Cannot delete loan because it has ${result.collectionsCount} associated collections. Delete the collections first or use force delete.`,
              hasCollections: true,
              collectionsCount: result.collectionsCount
            });
          }

          // Otherwise return the general error
          return res.status(404).json({
            message: result.error || "Loan could not be deleted",
            error: result.error
          });
        }

        return res.status(200).json({
          success: true,
          message: "Loan deleted successfully"
        });
      }
    } catch (error) {
      console.error('Delete loan error:', error);
      return res.status(500).json({
        message: error instanceof Error ? error.message : 'Server error while deleting loan'
      });
    }
  });

  // Direct collection generation endpoint
  app.post('/api/loans/:id/generate-collections', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const loanId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(loanId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid loan ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Get the loan to verify it exists
      const loan = await storage.getLoan(loanId, companyId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Calculate payment amount and dates
      const amount = parseFloat(loan.amount);
      const interestRate = parseFloat(loan.interest_rate);
      const term = loan.term;
      const startDate = new Date(loan.start_date);
      const paymentFrequency = loan.payment_frequency || 'monthly';

      // Calculate payment amount based on loan type and interest type
      const interestType = loan.interest_type || 'flat';
      const isUpfrontInterest = loan.is_upfront_interest === true;

      // Calculate total interest
      let totalInterest = amount * (interestRate / 100);

      // For reducing balance, we need a more complex calculation
      if (interestType === 'reducing') {
        // This is a simplified calculation - in a real app, you'd use a more accurate formula
        totalInterest = (amount * (interestRate / 100) * (term + 1)) / (2 * 12);
      }

      // Calculate payment amount - use only principal for collections to match payment schedule
      const paymentAmount = amount / term;

      // Get prefix from company_prefix_settings for collection string
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'collection');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for collection`);

      // Generate collections for each payment period
      const collections = [];

      for (let i = 0; i < term; i++) {
        // Calculate due date based on payment frequency
        const dueDate = new Date(startDate);

        // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
        if (paymentFrequency === 'daily') {
          dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
        } else if (paymentFrequency === 'weekly') {
          dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
        } else if (paymentFrequency === 'biweekly') {
          dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
        } else if (paymentFrequency === 'monthly') {
          dueDate.setMonth(dueDate.getMonth() + (i+1));
        } else {
          // Default to monthly
          dueDate.setMonth(dueDate.getMonth() + (i+1));
        }

        // Create collection
        const collection = await storage.createCollection({
          company_id: loan.company_id,
          loan_id: loan.id,
          customer_id: loan.customer_id,
          agent_id: null,
          amount: paymentAmount.toFixed(2),
          original_amount: paymentAmount.toFixed(2),
          scheduled_date: dueDate,
          collection_date: null,
          status: 'pending',
          payment_method: null,
          receipt_id: null,
          notes: `Auto-generated for payment #${i + 1}`,
          emi_number: i + 1,
          time_of_day: null,
          fine_amount: '0',
          company_collection_string: `${prefix}-${(startNumber + i).toString().padStart(3, '0')}`
        });

        collections.push(collection);
      }

      return res.status(201).json({
        message: `Generated ${collections.length} collection records for loan ${loanId}`,
        collections: collections
      });
    } catch (error) {
      console.error('Error generating collections:', error);
      return res.status(500).json({
        message: error instanceof Error ? error.message : 'Server error while generating collections'
      });
    }
  });

  // Payment Schedule routes removed - using Collections instead
  /*
  app.get('/api/payment-schedules', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.query.companyId as string, 10);
      const loanId = req.query.loanId ? parseInt(req.query.loanId as string, 10) : undefined;

      if (isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        console.log(`User ${req.user?.id} companies:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      let schedules;
      if (loanId && !isNaN(loanId)) {
        // Get schedules for a specific loan
        schedules = await storage.getPaymentSchedulesByLoan(loanId, companyId);
      } else {
        // Get all schedules for the company
        schedules = await storage.getPaymentSchedulesByCompany(companyId);
      }

      return res.json(schedules);
    } catch (error) {
      console.error('Error fetching payment schedules:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/payment-schedules/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const scheduleId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(scheduleId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid schedule ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const schedule = await storage.getPaymentSchedule(scheduleId, companyId);

      if (!schedule) {
        return res.status(404).json({ message: 'Payment schedule not found' });
      }

      return res.json(schedule);
    } catch (error) {
      console.error('Error fetching payment schedule:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.post('/api/payment-schedules', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      console.log('Payment schedule creation request body:', req.body);

      // Prepare the data - convert string dates to Date objects before validation
      const preparedData = { ...req.body };

      // Parse due_date if it exists and is a string
      if (preparedData.due_date && typeof preparedData.due_date === 'string') {
        preparedData.due_date = new Date(preparedData.due_date);
        console.log('Converted due_date to Date:', preparedData.due_date);
      }

      const result = insertPaymentScheduleSchema.safeParse(preparedData);

      if (!result.success) {
        console.log('Payment schedule validation errors:', result.error.errors);
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === result.data.company_id) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${result.data.company_id}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === result.data.company_id ||
          (uc.company && uc.company.id === result.data.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${result.data.company_id}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${result.data.company_id}`);
      }

      const schedule = await storage.createPaymentSchedule(result.data);
      return res.status(201).json(schedule);
    } catch (error) {
      console.error('Error creating payment schedule:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/payment-schedules/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const scheduleId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(scheduleId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid schedule ID or company ID" });
      }

      // Ensure user can only update their company's data
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      // Check if specific status update is requested
      if (req.body.status && typeof req.body.status === 'string') {
        const updatedSchedule = await storage.updatePaymentScheduleStatus(scheduleId, companyId, req.body.status);

        if (!updatedSchedule) {
          return res.status(404).json({ message: 'Payment schedule not found' });
        }

        return res.json(updatedSchedule);
      } else {
        // Handle general data update
        // Prepare the data - convert string dates to Date objects before validation
        const updateData = { ...req.body };

        // Parse due_date if it exists and is a string
        if (updateData.due_date && typeof updateData.due_date === 'string') {
          updateData.due_date = new Date(updateData.due_date);
        }

        const updatedSchedule = await storage.updatePaymentSchedule(scheduleId, companyId, updateData);

        if (!updatedSchedule) {
          return res.status(404).json({ message: 'Payment schedule not found' });
        }

        return res.json(updatedSchedule);
      }
    } catch (error) {
      console.error('Error updating payment schedule:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/payment-schedules/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const scheduleId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(scheduleId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid schedule ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const success = await storage.deletePaymentSchedule(scheduleId, companyId);

      if (!success) {
        return res.status(404).json({ message: 'Payment schedule not found or could not be deleted' });
      }

      return res.status(200).json({ success: true, message: "Payment schedule deleted successfully" });
    } catch (error) {
      console.error('Error deleting payment schedule:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
  */

  app.post('/api/loans/:id/generate-collections', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const loanId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(loanId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid loan ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        console.log(`User ${req.user?.id} companies:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Get the loan to verify it exists
      const loan = await storage.getLoan(loanId, companyId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Calculate payment amount based on loan details
      const amount = parseFloat(loan.amount);
      const interestRate = parseFloat(loan.interest_rate);
      const term = loan.term;
      const paymentFrequency = loan.payment_frequency || 'monthly';
      const interestType = loan.interest_type || 'flat';
      const isUpfrontInterest = loan.is_upfront_interest === true;
      const startDate = new Date(loan.start_date);

      // Calculate payment amount
      let paymentAmount = 0;
      if (interestType === 'flat') {
        // For flat interest, use only principal for collections to match payment schedule
        paymentAmount = amount / term;
      } else if (interestType === 'reducing') {
        // For reducing interest, use only principal for collections to match payment schedule
        paymentAmount = amount / term;
      } else if (isUpfrontInterest) {
        // For upfront interest, the interest is deducted from the principal upfront
        paymentAmount = amount / term;
      } else {
        // Default to simple division if interest type is not specified
        paymentAmount = amount / term;
      }

      // Round payment amount to 2 decimal places
      paymentAmount = Math.round(paymentAmount * 100) / 100;
      console.log(`[AUTO-COLLECTIONS] Calculated payment amount: ${paymentAmount}`);

      try {
        // Import necessary dependencies
        const { db } = await import('./db');
        const { collections } = await import('@shared/schema');

        console.log('[AUTO-COLLECTIONS] Creating collections...');

        // Get company name for collection string
        const companyCollectionString = await getCompanyName(companyId);

        // Generate collection records
        const pendingCollections = [];
        for (let i = 0; i < term; i++) {
          // Calculate due date based on payment frequency
          const dueDate = new Date(startDate);

          // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
          if (paymentFrequency === 'daily') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
          } else if (paymentFrequency === 'weekly') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
          } else if (paymentFrequency === 'biweekly') {
            dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
          } else if (paymentFrequency === 'monthly') {
            dueDate.setMonth(dueDate.getMonth() + (i+1));
          } else {
            // Default to monthly if not specified
            dueDate.setMonth(dueDate.getMonth() + (i+1));
          }

          pendingCollections.push({
            company_id: loan.company_id,
            loan_id: loan.id,
            customer_id: loan.customer_id,
            agent_id: null, // Can be assigned later
            amount: paymentAmount.toString(),
            original_amount: paymentAmount.toString(), // Store original amount
            scheduled_date: dueDate, // Use Date object directly
            collection_date: null,
            status: 'pending' as const, // Create as pending
            payment_method: null,
            receipt_id: null,
            notes: `Auto-generated for payment #${i + 1}`,
            emi_number: i + 1,
            time_of_day: loan.payment_time_of_day || null,
            fine_amount: '0',
            company_collection_string: companyCollectionString
          });
        }

        // --- Begin: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---
        // Get prefix from company_prefix_settings
        const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'collection');
        console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for collection`);

        const storageModule = await import('./storage/collection.storage');
        const collectionStorage = new storageModule.CollectionStorage();
        let highestSerial = await collectionStorage.getHighestCollectionSerial(companyId, prefix);
        // Use the higher of the highest existing serial or the start number from settings
        let nextSerial = Math.max(highestSerial + 1, startNumber);
        for (let i = 0; i < pendingCollections.length; i++) {
          const serial = nextSerial.toString().padStart(3, '0');
          pendingCollections[i].company_collection_string = `${prefix}-${serial}`;
          nextSerial++;
        }
        // --- End: Generate company_collection_string with serial for batch (AUTO-COLLECTIONS) ---

        // Insert all pending collections
        const createdCollections = await db.insert(collections).values(pendingCollections).returning();
        console.log(`[AUTO-COLLECTIONS] Successfully created ${createdCollections.length} collection records`);

        return res.status(201).json({
          message: `Successfully generated ${createdCollections.length} collections for loan #${loanId}`,
          collections: createdCollections
        });
      } catch (error) {
        console.error('[AUTO-COLLECTIONS] Error creating collections:', error);
        return res.status(500).json({
          message: error instanceof Error ? error.message : 'Server error while generating collections'
        });
      }
    } catch (error) {
      console.error('Error generating collections:', error);
      return res.status(500).json({
        message: error instanceof Error ? error.message : 'Server error while generating collections'
      });
    }
  });

  // Collection routes
  app.post('/api/collections', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      console.log('Collection creation request body:', req.body);

      // Prepare the data - convert string dates to Date objects before validation
      const preparedData = { ...req.body };

      // Parse scheduled_date if it exists and is a string (either ISO or date string)
      if (preparedData.scheduled_date && typeof preparedData.scheduled_date === 'string') {
        preparedData.scheduled_date = new Date(preparedData.scheduled_date);
        console.log('Converted scheduled_date to Date:', preparedData.scheduled_date);
      }

      const result = insertCollectionSchema.safeParse(preparedData);

      if (!result.success) {
        console.log('Collection validation errors:', result.error.errors);
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Ensure user can only create collections for their company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== result.data.company_id) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      const collection = await storage.createCollection(result.data);

      // Create double-entry accounting records for the collection
      try {
        // Get the cash/bank account and loan receivable account
        const cashAccount = await getAccountByCode(collection.company_id, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
        // If Cash account not found, try Bank account
        const cashOrBankAccount = cashAccount || await getAccountByCode(collection.company_id, SYSTEM_ACCOUNT_CODES.BANK, "Bank");
        const loanReceivableAccount = await getAccountByCode(collection.company_id, SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE, "Loan Receivable");
        const interestIncomeAccount = await getAccountByCode(collection.company_id, SYSTEM_ACCOUNT_CODES.INTEREST_INCOME, "Interest Income");

        console.log(`Cash/Bank account found: ${!!cashOrBankAccount}, ID: ${cashOrBankAccount?.id}, Name: ${cashOrBankAccount?.account_name}`);
        console.log(`Loan Receivable account found: ${!!loanReceivableAccount}, ID: ${loanReceivableAccount?.id}, Name: ${loanReceivableAccount?.account_name}`);
        console.log(`Interest Income account found: ${!!interestIncomeAccount}, ID: ${interestIncomeAccount?.id}, Name: ${interestIncomeAccount?.account_name}`);

        if (!cashOrBankAccount || !loanReceivableAccount || !interestIncomeAccount) {
          console.error(`Required accounts not found for company ${collection.company_id}`);
        } else {
          // Get the loan to determine principal vs interest
          const loan = await storage.getLoan(collection.loan_id, collection.company_id);

          if (loan) {
            // Use amount if principal_amount is not available
            const principalAmount = Number(collection.amount);
            const interestAmount = 0; // In a real system, this would be calculated properly
            const totalAmount = principalAmount + interestAmount;

            // Create a journal entry for the collection
            const entries: Record<string, {account_id: number, amount: number}> = {
              // Cash/Bank increases (debit)
              debit: {
                account_id: cashOrBankAccount.id,
                amount: totalAmount
              }
            };

            // If there's principal, reduce Loan Receivable (credit)
            if (principalAmount > 0) {
              entries.creditPrincipal = {
                account_id: loanReceivableAccount.id,
                amount: principalAmount
              };
            }

            // If there's interest, record Interest Income (credit)
            if (interestAmount > 0) {
              entries.creditInterest = {
                account_id: interestIncomeAccount.id,
                amount: interestAmount
              };
            }

            console.log(`Attempting to create journal entry for collection ${collection.id} (Amount: ${collection.amount})`);

            // Debug information for accounts
            console.log(`Using accounts for journal entry:`);
            console.log(`- Debit (Cash/Bank): ID ${cashOrBankAccount.id}, Name: ${cashOrBankAccount.account_name}, Code: ${cashOrBankAccount.account_code}`);
            console.log(`- Credit (Loan Receivable): ID ${loanReceivableAccount.id}, Name: ${loanReceivableAccount.account_name}, Code: ${loanReceivableAccount.account_code}`);
            if (interestAmount > 0) {
              console.log(`- Credit (Interest Income): ID ${interestIncomeAccount.id}, Name: ${interestIncomeAccount.account_name}, Code: ${interestIncomeAccount.account_code}`);
            }

            // Log the entries that will be used
            console.log('Journal entries being created:', JSON.stringify(entries, null, 2));

            const journalEntries = await createJournalEntry({
              company_id: collection.company_id,
              branch_id: collection.branch_id,
              transaction_date: collection.collection_date || new Date(),
              description: `Collection for loan ${loan.loan_number || loan.id}`,
              reference_type: 'collection',
              reference_id: collection.id,
              created_by: req.user?.id,
              entries
            });

            console.log(`Journal entry created successfully, transaction IDs: ${journalEntries.map(t => t.id).join(', ')}`);
            console.log(`Journal transactions: ${journalEntries.length} (${journalEntries.filter(t => t.transaction_type === 'debit').length} debits, ${journalEntries.filter(t => t.transaction_type === 'credit').length} credits)`);
            console.log(`Created journal entry for collection ${collection.id}`);
          } else {
            console.error(`Could not find loan ${collection.loan_id} for collection ${collection.id}`);
          }
        }
      } catch (journalError) {
        console.error('Failed to create journal entry for collection:', journalError);
        console.error('Journal entry error details:', (journalError as Error).message);
        console.error('Journal entry error stack:', (journalError as Error).stack);

        // Check for specific error types to provide better feedback
        const errorMessage = (journalError as Error).message;
        let warningMessage = "Collection created but financial records could not be generated. Please contact support.";

        if (errorMessage.includes('account')) {
          warningMessage = "Collection created but financial records could not be generated due to missing or invalid accounts.";
        } else if (errorMessage.includes('amount')) {
          warningMessage = "Collection created but financial records could not be generated due to invalid amount.";
        } else if (errorMessage.includes('no company_id')) {
          warningMessage = "Collection created but financial records could not be generated due to missing company information.";
        }

        collection.warning = warningMessage;
        collection.error_details = errorMessage; // Include more details for debugging
      }

      return res.status(201).json(collection);
    } catch (error) {
      console.error('Create collection error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Handler for /api/companies/:companyId/collections
  app.get('/api/companies/:companyId/collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const loanId = req.query.loanId ? parseInt(req.query.loanId as string, 10) : undefined;
      const status = req.query.status as string | undefined;

      console.log(`Fetching collections for company ${companyId}${loanId ? ` and loan ${loanId}` : ''}${status ? ` with status ${status}` : ''}`);

      let collections;
      if (loanId && !isNaN(loanId)) {
        // Get collections for a specific loan with status filter applied at the database level
        collections = await storage.getCollectionsByLoan(loanId, companyId, status);
        console.log(`Found ${collections.length} collections for loan ${loanId}${status ? ` with status '${status}'` : ''}`);
      } else {
        // Get all collections for the company with optional status filter
        collections = await storage.getCollectionsByCompany(companyId, status);
        console.log(`Found ${collections.length} collections for company ${companyId}${status ? ` with status '${status}'` : ''}`);

        // Log a few sample collections for debugging
        if (collections.length > 0) {
          console.log('Sample collections:');
          collections.slice(0, 3).forEach((c, i) => {
            console.log(`Collection ${i+1}: ID=${c.id}, Loan=${c.loan_id}, Amount=${c.amount}, Status=${c.status}, Date=${c.scheduled_date}`);
          });
        } else {
          console.log('No collections found for this company');

          // Check if there are any loans for this company
          const loans = await storage.getLoansByCompany(companyId);
          console.log(`Company has ${loans.length} loans`);

          if (loans.length > 0) {
            // Log a few sample loans
            console.log('Sample loans:');
            loans.slice(0, 3).forEach((l, i) => {
              console.log(`Loan ${i+1}: ID=${l.id}, Customer=${l.customer_id}, Amount=${l.amount}, Status=${l.status}`);
            });
          }
        }
      }

      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      return res.json(collections);
    } catch (error) {
      console.error('Get collections error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Handler for /api/collections - for backward compatibility
  app.get('/api/collections', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.query.companyId as string, 10);
      const loanId = req.query.loanId ? parseInt(req.query.loanId as string, 10) : undefined;
      const status = req.query.status as string | undefined;

      if (isNaN(companyId)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Check if user has access to this company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        // For non-saas_admin users, check if they have access to the company through associations
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      console.log(`[Legacy API] Fetching collections for company ${companyId}${loanId ? ` and loan ${loanId}` : ''}${status ? ` with status ${status}` : ''}`);

      let collections;
      if (loanId && !isNaN(loanId)) {
        // Get collections for a specific loan with status filter applied at the database level
        collections = await storage.getCollectionsByLoan(loanId, companyId, status);
        console.log(`Found ${collections.length} collections for loan ${loanId}${status ? ` with status '${status}'` : ''}`);
      } else {
        // Get all collections for the company with optional status filter
        collections = await storage.getCollectionsByCompany(companyId, status);
        console.log(`Found ${collections.length} collections for company ${companyId}${status ? ` with status '${status}'` : ''}`);
      }

      // Set cache control headers to prevent caching
      res.setHeader('Cache-Control', 'no-cache, no-store, must-revalidate');
      res.setHeader('Pragma', 'no-cache');
      res.setHeader('Expires', '0');

      return res.json(collections);
    } catch (error) {
      console.error('Get collections error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.id, 10);

      // Get the collection to check if it exists
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Get the user's company ID from their context
      const userCompanyId = req.user?.company_id;

      // Check if user has access to this collection's company
      if (req.user?.role !== 'saas_admin' && collection.company_id !== userCompanyId) {
        // For non-saas_admin users, check if they have access to the collection's company
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === collection.company_id ||
          (uc.company && uc.company.id === collection.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${collection.company_id}`);
          return res.status(403).json({ message: 'Access denied to this collection' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${collection.company_id}`);
      }

      return res.status(200).json(collection);
    } catch (error) {
      console.error('Get collection error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/collections/:id/status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.id, 10);
      const { status, companyId, payment_method, notes, fine_amount } = req.body;

      if (!status || !['pending', 'completed', 'overdue', 'cancelled'].includes(status)) {
        return res.status(400).json({ message: 'Invalid status' });
      }

      // Get the collection to check if it exists and to verify company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Collection can be completed regardless of order
      // No sequential validation needed

      // Get the user's company ID from their context
      const userCompanyId = req.user?.company_id;

      // Check if user has access to this collection's company
      if (req.user?.role !== 'saas_admin' && collection.company_id !== userCompanyId) {
        // For non-saas_admin users, check if they have access to the collection's company
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === collection.company_id ||
          (uc.company && uc.company.id === collection.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${collection.company_id}`);
          return res.status(403).json({ message: 'Access denied to this collection' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${collection.company_id}`);
      }

      // If completing the collection, validate payment method
      if (status === 'completed' && !payment_method) {
        return res.status(400).json({ message: 'Payment method is required when completing a collection' });
      }

      if (payment_method && !['cash', 'upi', 'bank_transfer'].includes(payment_method)) {
        return res.status(400).json({ message: 'Invalid payment method' });
      }

      // Use the collection's company_id for the update
      const effectiveCompanyId = collection.company_id;

      // If completing the collection, also update payment details if provided
      let updatedCollection;

      if (status === 'completed') {
        // Parse fine amount (if provided)
        let parsedFineAmount = null;
        if (fine_amount) {
          parsedFineAmount = parseFloat(fine_amount);
          if (isNaN(parsedFineAmount)) {
            parsedFineAmount = 0; // Default to 0 if invalid
          }
        }

        try {
          // Import PaymentProcessor dynamically to avoid circular dependencies
          const { PaymentProcessor } = await import('./utils/paymentProcessor');

          // Create a payment record for this collection
          const payment = await PaymentProcessor.createPaymentFromCollection(
            collection,
            "cash", // Always use cash as payment method
            req.user?.id || null,
            parsedFineAmount,
            null, // Transaction reference
            notes || null
          );

          console.log(`Created cash payment record: ${payment.id}, receipt: ${payment.receipt_number}`);

          // Update collection with payment details
          updatedCollection = await storage.updateCollection(
            collectionId,
            effectiveCompanyId,
            {
              status: status as any,
              collection_date: new Date(),
              payment_method: "cash", // Always set to cash
              receipt_id: payment.receipt_number,
              notes: notes || null,
              fine_amount: parsedFineAmount ? String(parsedFineAmount) : null
            }
          );

          // Create double-entry accounting records for completing the collection
          try {
            // Get the loan to determine total amount
            const loan = await storage.getLoan(collection.loan_id, effectiveCompanyId);

            if (!loan) {
              console.error(`Loan ${collection.loan_id} not found for collection ${collection.id}`);
            } else {
              console.log(`Found loan #${loan.id} for collection #${collection.id}`);

              // Get the accounts needed for the journal entry
              const cashAccount = await getAccountByCode(effectiveCompanyId, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
              const cashOrBankAccount = cashAccount || await getAccountByCode(effectiveCompanyId, SYSTEM_ACCOUNT_CODES.BANK, "Bank");
              const loanReceivableAccount = await getAccountByCode(effectiveCompanyId, SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE, "Loan Receivable");
              const interestIncomeAccount = await getAccountByCode(effectiveCompanyId, SYSTEM_ACCOUNT_CODES.INTEREST_INCOME, "Interest Income");

              console.log(`Cash/Bank account found: ${!!cashOrBankAccount}, ID: ${cashOrBankAccount?.id}, Name: ${cashOrBankAccount?.account_name}`);
              console.log(`Loan Receivable account found: ${!!loanReceivableAccount}, ID: ${loanReceivableAccount?.id}, Name: ${loanReceivableAccount?.account_name}`);
              console.log(`Interest Income account found: ${!!interestIncomeAccount}, ID: ${interestIncomeAccount?.id}, Name: ${interestIncomeAccount?.account_name}`);

              if (!cashOrBankAccount || !loanReceivableAccount || !interestIncomeAccount) {
                console.error(`Required accounts not found for company ${effectiveCompanyId}`);
              } else {
                // Calculate the amounts - ensure they are valid numbers
                const principalAmount = parseFloat(collection.amount);
                const interestAmount = parsedFineAmount ? parseFloat(String(parsedFineAmount)) : 0;
                const totalAmount = principalAmount + interestAmount;

                if (isNaN(principalAmount) || isNaN(totalAmount)) {
                  throw new Error(`Invalid amount values: principal=${collection.amount}, fine=${parsedFineAmount}`);
                }

                console.log(`Collection amounts: principal=${principalAmount}, interest=${interestAmount}, total=${totalAmount}`);

                // Set up the journal entries with explicit account IDs and amounts
                const entries: Record<string, {account_id: number, amount: number}> = {};

                // Cash increases (debit)
                entries.debit = {
                  account_id: cashOrBankAccount.id,
                  amount: totalAmount
                };

                console.log(`Adding debit entry: account_id=${entries.debit.account_id}, amount=${entries.debit.amount}`);

                // Principal amount to reduce loan receivable (credit)
                if (principalAmount > 0) {
                  entries.creditPrincipal = {
                    account_id: loanReceivableAccount.id,
                    amount: principalAmount
                  };
                  console.log(`Adding credit principal entry: account_id=${entries.creditPrincipal.account_id}, amount=${entries.creditPrincipal.amount}`);
                }

                // Fine/interest amount to interest income (credit)
                if (interestAmount > 0) {
                  entries.creditInterest = {
                    account_id: interestIncomeAccount.id,
                    amount: interestAmount
                  };
                  console.log(`Adding credit interest entry: account_id=${entries.creditInterest.account_id}, amount=${entries.creditInterest.amount}`);
                }

                // Prepare the journal entry data
                const journalEntryData = {
                  company_id: effectiveCompanyId,
                  branch_id: collection.branch_id || null,
                  transaction_date: new Date(),
                  description: `Collection payment processed for loan ${loan.id}`,
                  reference_type: 'collection' as const,
                  reference_id: payment.id,
                  created_by: req.user?.id,
                  entries
                };

                console.log('Journal entry data being sent:', JSON.stringify(journalEntryData, null, 2));

                // Import functions directly to avoid any import issues
                const { createJournalEntry } = await import('./financialManagement');

                // Create the journal entry with proper error handling
                try {
                  const transactions = await createJournalEntry(journalEntryData);
                  console.log(`SUCCESS: Created journal entry for collection payment ${collection.id}`);
                  console.log(`Created ${transactions.length} transactions: ${transactions.map(t => t.id).join(', ')}`);
                } catch (innerError) {
                  console.error('Journal entry creation failed with error:', innerError);
                  console.error('Error details:', innerError instanceof Error ? innerError.message : 'Unknown error');
                  console.error('Error stack:', innerError instanceof Error ? innerError.stack : 'No stack available');
                  throw innerError; // Re-throw to be caught by outer catch
                }
              }
            }
          } catch (journalError) {
            console.error('Failed to create journal entry for collection payment:', journalError);
            console.error('Error details:', journalError instanceof Error ? journalError.message : 'Unknown error');
            console.error('Error stack:', journalError instanceof Error ? journalError.stack : 'No stack available');
            // We don't fail the collection update if the journal entry fails, but we log detailed errors
          }
        } catch (error) {
          console.error('Error creating payment record:', error);
          return res.status(500).json({ message: 'Failed to create payment record' });
        }
      } else {
        // Just update status
        updatedCollection = await storage.updateCollectionStatus(
          collectionId,
          effectiveCompanyId,
          status as any
        );
      }

      if (!updatedCollection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      return res.json(updatedCollection);
    } catch (error) {
      console.error('Update collection status error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.id, 10);

      console.log('Update collection request body:', req.body);

      // First, get the collection to check if it exists and to verify company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Get the user's company ID from their context
      const userCompanyId = req.user?.company_id;

      // Check if user has access to this collection's company
      if (req.user?.role !== 'saas_admin' && collection.company_id !== userCompanyId) {
        // For non-saas_admin users, check if they have access to the collection's company
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === collection.company_id ||
          (uc.company && uc.company.id === collection.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${collection.company_id}`);
          return res.status(403).json({ message: 'Access denied to this collection' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${collection.company_id}`);
      }

      // Validate the request body
      const validFields = [
        'company_id',
        'loan_id',
        'customer_id',
        'agent_id',
        'amount',
        'scheduled_date',
        'collection_date',
        'status',
        'payment_method',
        'receipt_id',
        'notes'
      ];

      // Cast types properly for database compatibility
      const rawUpdateData = Object.keys(req.body)
        .filter(key => validFields.includes(key))
        .reduce((obj, key) => {
          obj[key] = req.body[key];
          return obj;
        }, {} as Record<string, any>);

      // Process date fields
      if (rawUpdateData.scheduled_date && typeof rawUpdateData.scheduled_date === 'string') {
        rawUpdateData.scheduled_date = new Date(rawUpdateData.scheduled_date);
        console.log('Converted scheduled_date to Date:', rawUpdateData.scheduled_date);
      }
      if (rawUpdateData.collection_date && typeof rawUpdateData.collection_date === 'string') {
        rawUpdateData.collection_date = new Date(rawUpdateData.collection_date);
        console.log('Converted collection_date to Date:', rawUpdateData.collection_date);
      }

      // Keep amount as a string for now (will be handled by Drizzle)
      if (rawUpdateData.amount && typeof rawUpdateData.amount === 'number') {
        rawUpdateData.amount = String(rawUpdateData.amount);
        console.log('Converted amount to string:', rawUpdateData.amount);
      }

      // Don't allow changing company_id, loan_id, or customer_id
      delete rawUpdateData.company_id;
      delete rawUpdateData.loan_id;
      delete rawUpdateData.customer_id;

      const updateData = rawUpdateData as Partial<InsertCollection>;

      // Use the collection's company_id for the update
      const updatedCollection = await storage.updateCollection(collectionId, collection.company_id, updateData);
      if (!updatedCollection) {
        return res.status(404).json({ message: "Collection not found" });
      }

      return res.status(200).json(updatedCollection);
    } catch (error) {
      console.error('Update collection error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/collections/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.id, 10);

      // First, get the collection to check if it exists and to verify company access
      const collection = await storage.getCollection(collectionId);

      if (!collection) {
        return res.status(404).json({ message: 'Collection not found' });
      }

      // Get the user's company ID from their context
      const userCompanyId = req.user?.company_id;

      // Check if user has access to this collection's company
      if (req.user?.role !== 'saas_admin' && collection.company_id !== userCompanyId) {
        // For non-saas_admin users, check if they have access to the collection's company
        const userCompanies = await storage.getUserCompanies(req.user!.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === collection.company_id ||
          (uc.company && uc.company.id === collection.company_id)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${collection.company_id}`);
          return res.status(403).json({ message: 'Access denied to this collection' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${collection.company_id}`);
      }

      // Use the collection's company_id for the delete operation
      const success = await storage.deleteCollection(collectionId, collection.company_id);
      if (!success) {
        return res.status(404).json({ message: "Collection not found or could not be deleted" });
      }

      return res.status(200).json({ success: true, message: "Collection deleted successfully" });
    } catch (error) {
      console.error('Delete collection error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Use PaymentProcessor imported at the top level

  // Batch Payment Processing - Process multiple collections at once
  app.post('/api/collections/batch-complete', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);

      // Validate request data
      const batchCompleteSchema = z.object({
        company_id: z.number(),
        collection_ids: z.array(z.number()),
        payment_method: z.string(),
        amount: z.number(),
        notes: z.string().optional().nullable(),
        customer_id: z.number(),
        loan_id: z.number()
      });

      const validatedData = batchCompleteSchema.parse({
        ...req.body,
        company_id: Number(companyId),
      });

      if (validatedData.collection_ids.length === 0) {
        return res.status(400).json({ error: 'No collections selected' });
      }

      // Collections can be completed in any order
      // No sequential validation needed

      // Generate receipt number
      const receiptNumber = await PaymentProcessor.generateReceiptNumber(validatedData.company_id);

      // PERFORMANCE OPTIMIZATION: Do everything in a single transaction
      const result = await db.transaction(async (tx) => {
        // Get all collections first to avoid multiple queries
        const collectionsToUpdate = await tx
          .select()
          .from(collections)
          .where(
            and(
              eq(collections.company_id, validatedData.company_id),
              inArray(collections.id, validatedData.collection_ids)
            )
          );

        if (collectionsToUpdate.length === 0) {
          throw new Error('No collections found with the provided IDs');
        }

        // Prepare batch update data
        const updateValues = collectionsToUpdate.map(collection => ({
          id: collection.id,
          status: 'completed' as const,
          collection_date: new Date(),
          payment_method: validatedData.payment_method,
          receipt_id: receiptNumber,
          notes: validatedData.notes || null
        }));

        // Update all collections in a single batch operation
        const updatedCollections = [];
        for (const updateData of updateValues) {
          const [updated] = await tx
            .update(collections)
            .set({
              status: updateData.status,
              collection_date: updateData.collection_date,
              payment_method: updateData.payment_method,
              receipt_id: updateData.receipt_id,
              notes: updateData.notes
            })
            .where(eq(collections.id, updateData.id))
            .returning();

          if (updated) {
            updatedCollections.push(updated);
          }
        }

        // Create a single payment record for all collections
        if (updatedCollections.length > 0) {
          // Use the first collection as the base for the payment record
          const firstCollection = updatedCollections[0];

          // Create payment record with the existing receipt number
          await PaymentProcessor.createPaymentFromCollection(
            { ...firstCollection, receipt_id: receiptNumber, amount: validatedData.amount.toString() },
            validatedData.payment_method,
            userId,
            null, // fine amount
            null, // transaction reference
            validatedData.notes || `Batch payment for ${updatedCollections.length} collections`,
            receiptNumber // Pass the receipt number to avoid generating a new one
          );
        }

        return {
          success: true,
          collectionIds: updatedCollections.map(c => c.id),
          collectionsCount: updatedCollections.length,
          receiptNumber
        };
      });

      res.status(200).json({
        success: true,
        message: 'Payment processed successfully',
        collectionIds: result.collectionIds,
        collectionsCount: result.collectionsCount,
        receiptNumber: result.receiptNumber
      });
    } catch (error) {
      if (error instanceof ZodError) {
        return res.status(400).json({ error: formatZodError(error) });
      }
      console.error('Failed to process batch payment:', error);
      res.status(500).json({
        error: 'Failed to process batch payment',
        message: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Payment Receipt Routes
  app.get('/api/payments/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const paymentId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(paymentId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid payment ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Use PaymentProcessor imported at the top level

      const payment = await PaymentProcessor.getPayment(paymentId, companyId);
      if (!payment) {
        return res.status(404).json({ message: "Payment record not found" });
      }

      return res.json(payment);
    } catch (error) {
      console.error('Get payment error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/payments/:id/pdf', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const paymentId = parseInt(req.params.id, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(paymentId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid payment ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Use PaymentProcessor imported at the top level
      const payment = await PaymentProcessor.getPayment(paymentId, companyId);
      if (!payment) {
        return res.status(404).json({ message: "Payment record not found" });
      }

      // Get collection data
      const collection = await storage.getCollection(payment.collection_id, companyId);
      if (!collection) {
        return res.status(404).json({ message: "Collection record not found" });
      }

      // Get customer data
      const customer = await storage.getCustomer(collection.customer_id, companyId);
      if (!customer) {
        return res.status(404).json({ message: "Customer record not found" });
      }

      // Generate PDF
      const { PDFGenerator } = await import('./utils/pdfGenerator');
      const pdfBuffer = await PDFGenerator.generateReceipt(payment, collection, customer);

      // Set response headers
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="receipt-${payment.receipt_number}.pdf"`);

      // Send PDF
      res.send(pdfBuffer);
    } catch (error) {
      console.error('Generate PDF error:', error);
      return res.status(500).json({ message: 'Failed to generate receipt PDF' });
    }
  });

  app.get('/api/collections/:collectionId/payments', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.collectionId, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(collectionId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid collection ID or company ID" });
      }

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log(`Access granted: User ${req.user?.id} is a saas_admin`);
      }
      // Direct company match
      else if (req.user?.company_id === companyId) {
        console.log(`Access granted: User ${req.user?.id} has direct access to company ${companyId}`);
      }
      // Check user's company associations
      else {
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Use PaymentProcessor imported at the top level

      const payments = await PaymentProcessor.getPaymentsByCollection(collectionId, companyId);

      return res.json(payments);
    } catch (error) {
      console.error('Get collection payments error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
  app.get('/api/companies/:companyId/recent-collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const limit = parseInt(req.query.limit as string || '5', 10);
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Recent collections request for company ${companyId} by user ${req.user?.id} with period ${period}`);

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log('Access granted: user is saas_admin');
      } else {
        // Check if the user has access to this company
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        // Check if the user has an association with this company
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }      const recentCollections = await storage.getRecentCollections(companyId, limit, period);

      return res.json(recentCollections);
    } catch (error) {
      console.error('Get recent collections error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Partner/Investor routes
  app.post('/api/partners', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      console.log("Received partner data:", JSON.stringify(req.body));

      // Process the date fields before validation
      const formData = { ...req.body };

      // Convert date strings to Date objects if they exist
      if (formData.partnership_start_date && typeof formData.partnership_start_date === 'string') {
        formData.partnership_start_date = new Date(formData.partnership_start_date);
        console.log("Converted start date:", formData.partnership_start_date);
      }

      if (formData.partnership_end_date && typeof formData.partnership_end_date === 'string') {
        formData.partnership_end_date = new Date(formData.partnership_end_date);
        console.log("Converted end date:", formData.partnership_end_date);
      }

      // Handle investment_amount - convert empty string to null
      console.log("Investment amount before:", typeof formData.investment_amount, formData.investment_amount);

      if (formData.investment_amount === '') {
        // Convert empty string to null for the database
        formData.investment_amount = null;
        console.log("Converted empty investment_amount to null");
      } else if (formData.investment_amount && typeof formData.investment_amount === 'number') {
        // Convert number to string if needed
        formData.investment_amount = String(formData.investment_amount);
        console.log("Converted investment_amount from number to string:", formData.investment_amount);
      }

      // Generate company-specific partner reference code
      const companyId = formData.company_id;

      // Get prefix from company_prefix_settings
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'partner');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for partner`);

      // Get the highest existing partner reference code for this company
      const partnerStorage = new PartnerStorage();
      const highestSerial = await partnerStorage.getHighestPartnerSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const partnerReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated partner reference code: ${partnerReferenceCode} for company ${companyId}`);

      // Add the reference code to the partner data
      formData.partner_reference_code = partnerReferenceCode;
      console.log("Added partner_reference_code:", formData.partner_reference_code);

      console.log("Investment amount after:", typeof formData.investment_amount, formData.investment_amount);
      console.log("Final processed data:", JSON.stringify(formData));

      const result = insertPartnerSchema.safeParse(formData);

      if (!result.success) {
        console.log("Validation errors:", result.error.errors);
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Allow saas_admin and company_admin to create partners for any company
      if (req.user?.role !== 'saas_admin' && req.user?.role !== 'company_admin' && req.user?.company_id !== result.data.company_id) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      console.log("Data after validation:", JSON.stringify(result.data));
      const partner = await storage.createPartner(result.data);

      return res.status(201).json(partner);
    } catch (error) {
      console.error('Create partner error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.post('/api/companies/:companyId/partners', authMiddleware, requirePrefixSettings, async (req: AuthRequest, res: Response) => {
    try {
      console.log('POST /api/companies/:companyId/partners - Request received');
      const companyId = parseInt(req.params.companyId, 10);

      if (isNaN(companyId)) {
        console.error('Invalid company ID:', req.params.companyId);
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      console.log(`Company ID: ${companyId}`);

      // Allow saas_admin and company_admin to create partners for any company
      if (req.user?.role !== 'saas_admin' && req.user?.role !== 'company_admin' && req.user?.company_id !== companyId) {
        console.log(`Access denied: User ${req.user?.id} with role ${req.user?.role} tried to access company ${companyId}`);
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      // Generate company-specific partner reference code
      // Get prefix from company_prefix_settings
      const { prefix, startNumber } = await getPrefixFromSettings(companyId, 'partner');
      console.log(`Retrieved prefix from settings: ${prefix} with start number: ${startNumber} for partner`);

      // Get the highest existing partner reference code for this company
      const partnerStorage = new PartnerStorage();
      const highestSerial = await partnerStorage.getHighestPartnerSerial(companyId, prefix);
      // Use the higher of the highest existing serial or the start number from settings
      const nextSerial = Math.max(highestSerial + 1, startNumber);
      const serialString = nextSerial.toString().padStart(3, '0');
      const partnerReferenceCode = `${prefix}-${serialString}`;

      console.log(`Generated partner reference code: ${partnerReferenceCode} for company ${companyId}`);

      // Process the request body
      const partnerData = {
        ...req.body,
        company_id: companyId,
        partner_reference_code: partnerReferenceCode
      };
      console.log('Partner data:', partnerData);
      console.log('Added partner_reference_code:', partnerData.partner_reference_code);

      // Convert date strings to Date objects if they exist
      if (partnerData.partnership_start_date && typeof partnerData.partnership_start_date === 'string') {
        partnerData.partnership_start_date = new Date(partnerData.partnership_start_date);
      }
      if (partnerData.partnership_end_date && typeof partnerData.partnership_end_date === 'string') {
        partnerData.partnership_end_date = new Date(partnerData.partnership_end_date);
      }

      // Handle investment_amount - convert empty string to null
      console.log("Investment amount before:", typeof partnerData.investment_amount, partnerData.investment_amount);

      if (partnerData.investment_amount === '') {
        // Convert empty string to null for the database
        partnerData.investment_amount = null;
        console.log("Converted empty investment_amount to null");
      } else if (partnerData.investment_amount && typeof partnerData.investment_amount === 'number') {
        // Convert number to string if needed
        partnerData.investment_amount = String(partnerData.investment_amount);
        console.log("Converted investment_amount from number to string:", partnerData.investment_amount);
      }

      try {
        // Import the pool directly
        const { pool } = await import('./db');

        // Prepare the query
        const keys = Object.keys(partnerData);
        const values = Object.values(partnerData);
        const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');
        const columns = keys.join(', ');

        // Direct query to the database
        const query = `
          INSERT INTO partners (${columns})
          VALUES (${placeholders})
          RETURNING *
        `;

        const result = await pool.query(query, values);
        console.log(`Partner created with ID: ${result.rows[0].id}`);

        return res.status(201).json(result.rows[0]);
      } catch (error) {
        console.error('Partners creation error:', error);
        return res.status(500).json({
          message: 'Error creating partner',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Create partner error:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.get('/api/companies/:companyId/partners', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      console.log('GET /api/companies/:companyId/partners - Request received');
      const companyId = parseInt(req.params.companyId, 10);

      if (isNaN(companyId)) {
        console.error('Invalid company ID:', req.params.companyId);
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      console.log(`Company ID: ${companyId}`);

      // Allow saas_admin and company_admin to access any company's partners
      if (req.user?.role !== 'saas_admin' && req.user?.role !== 'company_admin' && req.user?.company_id !== companyId) {
        console.log(`Access denied: User ${req.user?.id} with role ${req.user?.role} tried to access company ${companyId}`);
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      // Use direct database query with the imported pool
      try {
        // Import the pool directly
        const { pool } = await import('./db');

        // Direct query to the database
        const query = 'SELECT * FROM partners WHERE company_id = $1';
        console.log('Executing query:', query);

        const result = await pool.query(query, [companyId]);
        console.log(`Direct query retrieved ${result.rows.length} partners`);

        return res.json(result.rows);
      } catch (error) {
        console.error('Partners query error:', error);
        return res.status(500).json({
          message: 'Error retrieving partners',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Get partners error:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.patch('/api/partners/:partnerId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const partnerId = parseInt(req.params.partnerId, 10);
      const companyId = parseInt(req.body.company_id, 10);

      // Allow saas_admin and company_admin to update any company's partners
      if (req.user?.role !== 'saas_admin' && req.user?.role !== 'company_admin' && req.user?.company_id !== companyId) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      console.log("Update partner - received data:", JSON.stringify(req.body));

      // Process the request body to handle dates
      const formData = { ...req.body };

      // Convert date strings to Date objects if they exist
      if (formData.partnership_start_date && typeof formData.partnership_start_date === 'string') {
        formData.partnership_start_date = new Date(formData.partnership_start_date);
        console.log("Converted start date:", formData.partnership_start_date);
      }

      if (formData.partnership_end_date && typeof formData.partnership_end_date === 'string') {
        formData.partnership_end_date = new Date(formData.partnership_end_date);
        console.log("Converted end date:", formData.partnership_end_date);
      }

      // Handle investment_amount - convert empty string to null
      console.log("Investment amount before:", typeof formData.investment_amount, formData.investment_amount);

      if (formData.investment_amount === '') {
        // Convert empty string to null for the database
        formData.investment_amount = null;
        console.log("Converted empty investment_amount to null");
      } else if (formData.investment_amount && typeof formData.investment_amount === 'number') {
        // Convert number to string if needed
        formData.investment_amount = String(formData.investment_amount);
        console.log("Converted investment_amount from number to string:", formData.investment_amount);
      }

      console.log("Investment amount after:", typeof formData.investment_amount, formData.investment_amount);
      console.log("Final processed data:", JSON.stringify(formData));

      // Validate the request body
      const validFields = ['company_id', 'name', 'email', 'phone', 'address', 'investment_amount', 'partnership_start_date', 'partnership_end_date', 'contact_person', 'website', 'agreement_details', 'active', 'type', 'notes'];
      const updateData = Object.keys(formData)
        .filter(key => validFields.includes(key))
        .reduce((obj: Record<string, any>, key: string) => {
          obj[key] = formData[key];
          return obj;
        }, {} as Partial<InsertPartner>);

      try {
        // Import the pool directly
        const { pool } = await import('./db');

        // First check if the partner exists and belongs to the company
        const checkQuery = `
          SELECT * FROM partners
          WHERE id = $1 AND company_id = $2
        `;

        const checkResult = await pool.query(checkQuery, [partnerId, companyId]);

        if (checkResult.rows.length === 0) {
          return res.status(404).json({
            message: `Partner with id=${partnerId} not found for company id=${companyId}`
          });
        }

        // Prepare the update query
        const keys = Object.keys(updateData);
        const values = Object.values(updateData);

        if (keys.length === 0) {
          return res.json(checkResult.rows[0]); // Nothing to update
        }

        const setClause = keys.map((key, i) => `${key} = $${i + 3}`).join(', ');

        // Direct query to the database
        const updateQuery = `
          UPDATE partners
          SET ${setClause}, updated_at = NOW()
          WHERE id = $1 AND company_id = $2
          RETURNING *
        `;

        const updateResult = await pool.query(updateQuery, [partnerId, companyId, ...values]);
        console.log(`Partner updated with ID: ${updateResult.rows[0].id}`);

        return res.status(200).json(updateResult.rows[0]);
      } catch (error) {
        console.error('Direct database update error:', error);
        return res.status(500).json({
          message: 'Error updating partner',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Update partner error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/partners/:partnerId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const partnerId = parseInt(req.params.partnerId, 10);
      const companyId = parseInt(req.query.companyId as string, 10);

      if (isNaN(partnerId) || isNaN(companyId)) {
        return res.status(400).json({ message: "Invalid partner ID or company ID" });
      }

      // Allow saas_admin and company_admin to delete any company's partners
      if (req.user?.role !== 'saas_admin' && req.user?.role !== 'company_admin' && req.user?.company_id !== companyId) {
        return res.status(403).json({ message: 'Access denied to this company' });
      }

      try {
        // Import the pool directly
        const { pool } = await import('./db');

        // First check if the partner exists and belongs to the company
        const checkQuery = `
          SELECT * FROM partners
          WHERE id = $1 AND company_id = $2
        `;

        const checkResult = await pool.query(checkQuery, [partnerId, companyId]);

        if (checkResult.rows.length === 0) {
          return res.status(404).json({
            message: `Partner with id=${partnerId} not found for company id=${companyId}`
          });
        }

        // Delete the partner
        const deleteQuery = `
          DELETE FROM partners
          WHERE id = $1 AND company_id = $2
        `;

        await pool.query(deleteQuery, [partnerId, companyId]);
        console.log(`Partner deleted with ID: ${partnerId}`);

        return res.status(200).json({ success: true, message: "Partner deleted successfully" });
      } catch (error) {
        console.error('Direct database delete error:', error);
        return res.status(500).json({
          message: 'Error deleting partner',
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    } catch (error) {
      console.error('Delete partner error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Dashboard routes
  app.get('/api/companies/:companyId/dashboard-metrics', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {      const companyId = parseInt(req.params.companyId, 10);
      // Get period parameter from query (default to 30 days)
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Dashboard metrics request for company ${companyId} by user ${req.user?.id} with period ${period}`);

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log('Access granted: user is saas_admin');
      } else {
        // Check if the user has access to this company (regardless of primary status)
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        // Check if the user has an association with this company
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        console.log(`User ${req.user?.id} companies:`, userCompanies.map(uc => ({
          id: uc.id,
          company_id: uc.company_id,
          is_primary: uc.is_primary
        })));

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }      // Get metrics including trend data
      const metrics = await storage.getDashboardMetrics(companyId, { days: period });

      return res.json(metrics);
    } catch (error) {
      console.error('Get dashboard metrics error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/collection-trends', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Collection trends request for company ${companyId} by user ${req.user?.id}`);

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log('Access granted: user is saas_admin');
      } else {
        // Check if the user has access to this company
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        // Check if the user has an association with this company
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      // Get collections from the database
      const collections = await storage.getCollectionsByCompany(companyId);

      // Generate date labels and initialize data structure
      const days = period;
      const trendData = [];
      const today = new Date();

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date();
        date.setDate(today.getDate() - i);
        // Format to YYYY-MM-DD for comparison
        const formattedDateForCompare = date.toISOString().split('T')[0];
        // Format for display
        const formattedDateForDisplay = date.toLocaleDateString('en-IN', {
          month: 'short',
          day: 'numeric'
        });

        // Sum collections for this date
        const dailyTotal = collections.reduce((sum, collection) => {
          const collectionDate = new Date(collection.created_at).toISOString().split('T')[0];
          if (collectionDate === formattedDateForCompare && collection.status === 'completed') {
            // Parse the amount to a number before adding
            const amount = typeof collection.amount === 'string' ? parseFloat(collection.amount) : collection.amount;
            return sum + amount;
          }
          return sum;
        }, 0);

        trendData.push({
          date: formattedDateForDisplay,
          amount: dailyTotal
        });
      }

      return res.json(trendData);
    } catch (error) {
      console.error('Get collection trends error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
  app.get('/api/companies/:companyId/top-agents', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);
      const limit = parseInt(req.query.limit as string || '5', 10);
      const period = parseInt(req.query.period as string || '30', 10);

      console.log(`Top agents request for company ${companyId} by user ${req.user?.id} with period ${period}`);

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log('Access granted: user is saas_admin');
      } else {
        // Check if the user has access to this company
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        // Check if the user has an association with this company
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }      const topAgents = await storage.getTopAgents(companyId, limit, period);

      return res.json(topAgents);
    } catch (error) {
      console.error('Get top agents error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Reseller routes
  app.post('/api/resellers', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const result = insertResellerSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const reseller = await storage.createReseller(result.data);

      return res.status(201).json(reseller);
    } catch (error) {
      console.error('Create reseller error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/resellers/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const resellerId = parseInt(req.params.id, 10);

      // SaaS admin can access any reseller, other users can only access if they are the reseller
      const reseller = await storage.getReseller(resellerId);

      if (!reseller) {
        return res.status(404).json({ message: 'Reseller not found' });
      }

      if (req.user?.role !== 'saas_admin' && req.user?.id !== reseller.user_id) {
        return res.status(403).json({ message: 'Access denied to this reseller' });
      }

      return res.json(reseller);
    } catch (error) {
      console.error('Get reseller error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Referral routes
  app.post('/api/referrals', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Generate a random referral code if not provided
      const referralData = {
        ...req.body,
        referrer_id: req.user?.id,
        referral_code: req.body.referral_code || nanoid(8),
        status: 'pending'
      };

      const result = insertReferralSchema.safeParse(referralData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const referral = await storage.createReferral(result.data);

      return res.status(201).json(referral);
    } catch (error) {
      console.error('Create referral error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/referrals', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user?.id) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const referrals = await storage.getReferralsByUser(req.user.id);

      return res.json(referrals);
    } catch (error) {
      console.error('Get referrals error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Public route for getting subscription plans (for registration)
  app.get('/api/public/subscription-plans', async (req: Request, res: Response) => {
    try {
      const plans = await storage.getSubscriptionPlans();
      return res.json(plans);
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Partner program metrics
  // Branches API endpoints
  app.post('/api/branches', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const validatedData = insertBranchSchema.parse(req.body);
      const branch = await db
        .insert(branches)
        .values({
          ...validatedData,
          company_id: Number(validatedData.company_id),
        })
        .returning();

      res.status(201).json(branch[0]);
    } catch (error) {
      console.error('Error creating branch:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  app.get('/api/companies/:companyId/branches', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = Number(req.params.companyId);
      const userCompany = await getUserCompanyRole(req.user.id, companyId);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this company' });
      }

      const branchList = await db
        .select()
        .from(branches)
        .where(eq(branches.company_id, companyId));

      res.status(200).json(branchList);
    } catch (error) {
      console.error('Error fetching branches:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [branch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, branch.company_id);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this branch' });
      }

      res.status(200).json(branch);
    } catch (error) {
      console.error('Error fetching branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [existingBranch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!existingBranch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingBranch.company_id);

      if (!userCompany || !['company_admin', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to update this branch' });
      }

      const updateData = req.body;
      const [updatedBranch] = await db
        .update(branches)
        .set(updateData)
        .where(eq(branches.id, branchId))
        .returning();

      res.status(200).json(updatedBranch);
    } catch (error) {
      console.error('Error updating branch:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  // Route to fix missing collection transactions
  app.post('/api/companies/:companyId/fix-collection-transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId, 10);

      if (isNaN(companyId)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Run the fix
      const result = await fixMissingCollectionTransactions(companyId);

      return res.status(200).json({
        success: true,
        message: `Fixed ${result.fixed} collections with ${result.errors} errors`,
        details: result.details
      });
    } catch (error) {
      console.error('Error fixing collection transactions:', error);
      return res.status(500).json({
        message: 'Server error while fixing collection transactions',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.delete('/api/branches/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.id);
      const [existingBranch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!existingBranch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingBranch.company_id);

      if (!userCompany || !['company_admin', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to delete this branch' });
      }

      await db
        .delete(branches)
        .where(eq(branches.id, branchId));

      res.status(200).json({ message: 'Branch deleted successfully' });
    } catch (error) {
      console.error('Error deleting branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  // Groups API endpoints
  app.post('/api/groups', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const validatedData = insertGroupSchema.parse(req.body);
      const companyId = Number(validatedData.company_id);

      const userCompany = await getUserCompanyRole(req.user.id, companyId);

      if (!userCompany || !['company_admin', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to create groups for this company' });
      }

      const group = await db
        .insert(groups)
        .values({
          ...validatedData,
          company_id: companyId,
          branch_id: validatedData.branch_id ? Number(validatedData.branch_id) : null,
        })
        .returning();

      res.status(201).json(group[0]);
    } catch (error) {
      console.error('Error creating group:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  app.get('/api/branches/:branchId/groups', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const branchId = Number(req.params.branchId);
      const [branch] = await db
        .select()
        .from(branches)
        .where(eq(branches.id, branchId));

      if (!branch) {
        return res.status(404).json({ message: 'Branch not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, branch.company_id);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this branch' });
      }

      const groupList = await db
        .select()
        .from(groups)
        .where(eq(groups.branch_id, branchId));

      res.status(200).json(groupList);
    } catch (error) {
      console.error('Error fetching groups for branch:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/companies/:companyId/groups', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = Number(req.params.companyId);
      const userCompany = await getUserCompanyRole(req.user.id, companyId);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this company' });
      }

      const groupList = await db
        .select()
        .from(groups)
        .where(eq(groups.company_id, companyId));

      res.status(200).json(groupList);
    } catch (error) {
      console.error('Error fetching groups for company:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/groups/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const groupId = Number(req.params.id);
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, group.company_id);

      if (!userCompany) {
        return res.status(403).json({ message: 'You do not have access to this group' });
      }

      res.status(200).json(group);
    } catch (error) {
      console.error('Error fetching group:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.patch('/api/groups/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const groupId = Number(req.params.id);
      const [existingGroup] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!existingGroup) {
        return res.status(404).json({ message: 'Group not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingGroup.company_id);

      if (!userCompany || !['company_admin', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to update this group' });
      }

      const updateData = req.body;
      if (updateData.branch_id !== undefined) {
        updateData.branch_id = updateData.branch_id ? Number(updateData.branch_id) : null;
      }

      const [updatedGroup] = await db
        .update(groups)
        .set(updateData)
        .where(eq(groups.id, groupId))
        .returning();

      res.status(200).json(updatedGroup);
    } catch (error) {
      console.error('Error updating group:', error);
      if (error instanceof ZodError) {
        res.status(400).json({ errors: formatZodError(error) });
      } else {
        res.status(500).json({ message: 'Internal server error' });
      }
    }
  });

  app.delete('/api/groups/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const groupId = Number(req.params.id);
      const [existingGroup] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!existingGroup) {
        return res.status(404).json({ message: 'Group not found' });
      }

      const userCompany = await getUserCompanyRole(req.user.id, existingGroup.company_id);

      if (!userCompany || !['company_admin', 'saas_admin'].includes(userCompany.role)) {
        return res.status(403).json({ message: 'You do not have permission to delete this group' });
      }

      await db
        .delete(groups)
        .where(eq(groups.id, groupId));

      res.status(200).json({ message: 'Group deleted successfully' });
    } catch (error) {
      console.error('Error deleting group:', error);
      res.status(500).json({ message: 'Internal server error' });
    }
  });

  app.get('/api/partner-program-metrics', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const metrics = await storage.getPartnerProgramMetrics();

      return res.json(metrics);
    } catch (error) {
      console.error('Get partner program metrics error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Subscription plans endpoints
  app.get('/api/subscription-plans', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const plans = await storage.getSubscriptionPlans();
      return res.json(plans);
    } catch (error) {
      console.error('Error getting subscription plans:', error);
      return res.status(500).json({ message: 'Failed to get subscription plans' });
    }
  });

  app.get('/api/subscription-plans/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const plan = await storage.getSubscriptionPlan(id);

      if (!plan) {
        return res.status(404).json({ message: 'Subscription plan not found' });
      }

      return res.json(plan);
    } catch (error) {
      console.error('Error getting subscription plan:', error);
      return res.status(500).json({ message: 'Failed to get subscription plan' });
    }
  });

  app.post('/api/subscription-plans', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const result = insertSubscriptionPlanSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const plan = await storage.createSubscriptionPlan(result.data);
      return res.status(201).json(plan);
    } catch (error) {
      console.error('Error creating subscription plan:', error);
      return res.status(500).json({ message: 'Failed to create subscription plan' });
    }
  });

  app.patch('/api/subscription-plans/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const planData = req.body;

      const updatedPlan = await storage.updateSubscriptionPlan(id, planData);

      if (!updatedPlan) {
        return res.status(404).json({ message: 'Subscription plan not found' });
      }

      return res.json(updatedPlan);
    } catch (error) {
      console.error('Error updating subscription plan:', error);
      return res.status(500).json({ message: 'Failed to update subscription plan' });
    }
  });

  app.delete('/api/subscription-plans/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const result = await storage.deleteSubscriptionPlan(id);

      if (!result) {
        return res.status(400).json({ message: 'Cannot delete a plan that is currently in use by subscriptions' });
      }

      return res.json({ message: 'Subscription plan deleted successfully' });
    } catch (error) {
      console.error('Error deleting subscription plan:', error);
      return res.status(500).json({ message: 'Failed to delete subscription plan' });
    }
  });

  // Subscriptions endpoints
  app.get('/api/subscriptions', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const subscriptions = await storage.getAllSubscriptions();
      return res.json(subscriptions);
    } catch (error) {
      console.error('Error getting subscriptions:', error);
      return res.status(500).json({ message: 'Failed to get subscriptions' });
    }
  });

  app.get('/api/subscriptions/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const subscription = await storage.getSubscription(id);

      if (!subscription) {
        return res.status(404).json({ message: 'Subscription not found' });
      }

      return res.json(subscription);
    } catch (error) {
      console.error('Error getting subscription:', error);
      return res.status(500).json({ message: 'Failed to get subscription' });
    }
  });

  app.get('/api/companies/:companyId/subscriptions', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      console.log(`Subscriptions request for company ${companyId} by user ${req.user?.id}`);

      // SaaS admins can access any company
      if (req.user?.role === 'saas_admin') {
        console.log('Access granted: user is saas_admin');
      } else {
        // Check if the user has access to this company
        const userCompanies = await storage.getUserCompanies(req.user!.id);

        // Check if the user has an association with this company
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user?.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }

        console.log(`Access granted: User ${req.user?.id} is associated with company ${companyId}`);
      }

      const subscriptions = await storage.getSubscriptionsByCompany(companyId);
      return res.json(subscriptions);
    } catch (error) {
      console.error('Error getting company subscriptions:', error);
      return res.status(500).json({ message: 'Failed to get company subscriptions' });
    }
  });

  app.post('/api/subscriptions', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      // Convert string date fields to actual Date objects
      const dateFields = ['start_date', 'end_date', 'last_payment_date', 'next_payment_date'];
      const processedData = { ...req.body };

      dateFields.forEach(field => {
        if (processedData[field] && typeof processedData[field] === 'string') {
          processedData[field] = new Date(processedData[field]);
        }
      });

      const result = insertSubscriptionSchema.safeParse(processedData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const subscription = await storage.createSubscription(result.data);
      return res.status(201).json(subscription);
    } catch (error) {
      console.error('Error creating subscription:', error);
      return res.status(500).json({ message: 'Failed to create subscription' });
    }
  });

  app.patch('/api/subscriptions/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Convert string date fields to actual Date objects
      const dateFields = ['start_date', 'end_date', 'last_payment_date', 'next_payment_date'];
      const processedData = { ...req.body };

      dateFields.forEach(field => {
        if (processedData[field] && typeof processedData[field] === 'string') {
          processedData[field] = new Date(processedData[field]);
        }
      });

      const updatedSubscription = await storage.updateSubscription(id, processedData);

      if (!updatedSubscription) {
        return res.status(404).json({ message: 'Subscription not found' });
      }

      return res.json(updatedSubscription);
    } catch (error) {
      console.error('Error updating subscription:', error);
      return res.status(500).json({ message: 'Failed to update subscription' });
    }
  });

  app.delete('/api/subscriptions/:id', authMiddleware, requireRole(['saas_admin']), async (req: Request, res: Response) => {
    try {
      const id = parseInt(req.params.id);
      const result = await storage.deleteSubscription(id);

      if (!result) {
        return res.status(500).json({ message: 'Failed to delete subscription' });
      }

      return res.json({ message: 'Subscription deleted successfully' });
    } catch (error) {
      console.error('Error deleting subscription:', error);
      return res.status(500).json({ message: 'Failed to delete subscription' });
    }
  });

  // Dynamic Form Template routes
  app.get('/api/companies/:companyId/form-templates', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const onlyActive = req.query.active === 'true';
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;

      let templates;

      if (branchId) {
        // If branch ID is provided, get forms for that branch
        templates = await storage.getFormTemplatesByBranch(branchId);
        // Make sure templates belong to the requested company as an extra security measure
        templates = templates.filter(template => template.company_id === companyId);
      } else {
        // Otherwise get all forms for the company
        templates = await storage.getFormTemplatesByCompany(companyId);
      }

      // Filter for active templates if requested
      const filteredTemplates = onlyActive
        ? templates.filter(template => template.is_active)
        : templates;

      return res.json(filteredTemplates);
    } catch (error) {
      console.error('Error fetching form templates:', error);
      return res.status(500).json({ message: 'Failed to fetch form templates' });
    }
  });

  app.get('/api/companies/:companyId/form-templates/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      const template = await storage.getFormTemplate(id, companyId);

      if (!template) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      return res.json(template);
    } catch (error) {
      console.error('Error fetching form template:', error);
      return res.status(500).json({ message: 'Failed to fetch form template' });
    }
  });

  app.post('/api/companies/:companyId/form-templates', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const templateData = { ...req.body, company_id: companyId };

      const result = insertFormTemplateSchema.safeParse(templateData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: formatZodError(result.error) });
      }

      const template = await storage.createFormTemplate(result.data);
      return res.status(201).json(template);
    } catch (error) {
      console.error('Error creating form template:', error);
      return res.status(500).json({ message: 'Failed to create form template' });
    }
  });

  app.put('/api/companies/:companyId/form-templates/:id', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the template exists and belongs to the company
      const existingTemplate = await storage.getFormTemplate(id, companyId);

      if (!existingTemplate) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      const updatedTemplate = await storage.updateFormTemplate(id, companyId, req.body);

      if (!updatedTemplate) {
        return res.status(500).json({ message: 'Failed to update form template' });
      }

      return res.json(updatedTemplate);
    } catch (error) {
      console.error('Error updating form template:', error);
      return res.status(500).json({ message: 'Failed to update form template' });
    }
  });

  app.delete('/api/companies/:companyId/form-templates/:id', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the template exists and belongs to the company
      const existingTemplate = await storage.getFormTemplate(id, companyId);

      if (!existingTemplate) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      // Check if the template is in use
      const isInUse = await storage.isFormTemplateInUse(id);

      if (isInUse) {
        return res.status(400).json({
          message: 'Cannot delete form template that is in use by existing loans or configurations',
          error: 'template_in_use'
        });
      }

      const result = await storage.deleteFormTemplate(id, companyId);

      if (!result) {
        return res.status(500).json({ message: 'Failed to delete form template' });
      }

      return res.json({ message: 'Form template deleted successfully' });
    } catch (error) {
      console.error('Error deleting form template:', error);
      return res.status(500).json({ message: 'Failed to delete form template' });
    }
  });

  // Toggle form template active status
  app.patch('/api/companies/:companyId/form-templates/:id/toggle-active', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the template exists and belongs to the company
      const existingTemplate = await storage.getFormTemplate(id, companyId);

      if (!existingTemplate) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      // Toggle the active status
      const updatedTemplate = await storage.updateFormTemplate(id, companyId, {
        is_active: !existingTemplate.is_active
      });

      return res.json(updatedTemplate);
    } catch (error) {
      console.error('Error toggling form template status:', error);
      return res.status(500).json({ message: 'Failed to update form template status' });
    }
  });

  // Loan Configuration routes
  // Get all loan configurations for a company
  app.get('/api/companies/:companyId/loan-configurations', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const configurations = await storage.getLoanConfigurationsByCompany(companyId);
      return res.json(configurations);
    } catch (error) {
      errorLogger.logError(
        'Failed to fetch loan configurations',
        'loan-configurations-route',
        { error, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch loan configurations')
      );
    }
  });

  // Get active loan configurations (legacy route, kept for backward compatibility)
  app.get('/api/companies/:companyId/loan-configurations/active', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      errorLogger.logInfo(
        `Fetching active loan configurations for company ${companyId}`,
        'loan-configurations-route'
      );

      const configurations = await storage.getActiveLoanConfigurations(companyId);
      return res.json(configurations);
    } catch (error) {
      errorLogger.logError(
        'Failed to fetch active loan configurations',
        'active-loan-configurations-route',
        { error, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch loan configurations')
      );
    }
  });

  // Get active form templates (new simplified approach)
  app.get('/api/companies/:companyId/active-templates', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      errorLogger.logInfo(
        `Fetching active templates for company ${companyId}`,
        'templates-route'
      );

      const templates = await storage.getActiveFormTemplates(companyId);
      return res.json(templates);
    } catch (error) {
      errorLogger.logError(
        'Failed to fetch active templates',
        'active-templates-route',
        { error, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch active templates')
      );
    }
  });

  // Get a specific loan configuration
  app.get('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      const configuration = await storage.getLoanConfiguration(id, companyId);

      if (!configuration) {
        errorLogger.logWarning(
          `Loan configuration not found`,
          'get-loan-configuration-route',
          { id, companyId }
        );
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      return res.json(configuration);
    } catch (error) {
      errorLogger.logError(
        'Failed to fetch loan configuration',
        'get-loan-configuration-route',
        { error, id: req.params.id, companyId: req.params.companyId }
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error, 'Failed to fetch loan configuration')
      );
    }
  });

  // Create a new loan configuration
  app.post('/api/companies/:companyId/loan-configurations', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Validate the request body
      const result = insertLoanConfigurationSchema.safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid loan configuration data', errors: formatZodError(result.error) });
      }

      // Verify the template exists and belongs to the company
      const template = await storage.getFormTemplate(result.data.template_id, companyId);

      if (!template) {
        return res.status(400).json({ message: 'Form template not found or does not belong to this company' });
      }

      // Create the loan configuration
      const configuration = await storage.createLoanConfiguration(result.data);
      return res.status(201).json(configuration);
    } catch (error) {
      console.error('Error creating loan configuration:', error);
      return res.status(500).json({ message: 'Failed to create loan configuration' });
    }
  });

  // Update a loan configuration
  app.put('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id, companyId);

      if (!existingConfig) {
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // Validate the request body
      const result = insertLoanConfigurationSchema.partial().safeParse({
        ...req.body,
        company_id: companyId
      });

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid loan configuration data', errors: formatZodError(result.error) });
      }

      // If the template ID is being updated, verify it exists and belongs to the company
      if (result.data.template_id) {
        const template = await storage.getFormTemplate(result.data.template_id, companyId);

        if (!template) {
          return res.status(400).json({ message: 'Form template not found or does not belong to this company' });
        }
      }

      // Update the loan configuration
      const updatedConfig = await storage.updateLoanConfiguration(id, companyId, result.data);
      return res.json(updatedConfig);
    } catch (error) {
      console.error('Error updating loan configuration:', error);
      return res.status(500).json({ message: 'Failed to update loan configuration' });
    }
  });

  // Delete a loan configuration
  app.delete('/api/companies/:companyId/loan-configurations/:id', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id, companyId);

      if (!existingConfig) {
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // Check if the configuration is in use
      const isInUse = await storage.isLoanConfigurationInUse(id, companyId);

      if (isInUse) {
        return res.status(400).json({
          message: 'Cannot delete loan configuration that is in use by existing loans',
          error: 'configuration_in_use'
        });
      }

      // Delete the loan configuration
      const success = await storage.deleteLoanConfiguration(id, companyId);

      if (!success) {
        return res.status(500).json({ message: 'Failed to delete loan configuration' });
      }

      return res.json({ message: 'Loan configuration deleted successfully' });
    } catch (error) {
      console.error('Error deleting loan configuration:', error);
      return res.status(500).json({ message: 'Failed to delete loan configuration' });
    }
  });

  // Toggle a loan configuration's active status
  app.patch('/api/companies/:companyId/loan-configurations/:id/toggle-active', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the configuration exists and belongs to the company
      const existingConfig = await storage.getLoanConfiguration(id, companyId);

      if (!existingConfig) {
        return res.status(404).json({ message: 'Loan configuration not found' });
      }

      // Toggle the active status
      const updatedConfig = await storage.updateLoanConfiguration(id, companyId, {
        is_active: !existingConfig.is_active
      });

      return res.json(updatedConfig);
    } catch (error) {
      console.error('Error toggling loan configuration status:', error);
      return res.status(500).json({ message: 'Failed to update loan configuration status' });
    }
  });

  // Direct form template access (for viewing)
  app.get('/api/form-templates/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.id);

      // Get all the companies the user has access to
      const { userId } = ensureUserAuth(req);
      const userCompanies = await storage.getUserCompanies(userId);
      const companyIds = userCompanies.map(uc => uc.company_id);

      // Check if template exists for any of the user's companies
      let foundTemplate = null;

      for (const companyId of companyIds) {
        const template = await storage.getFormTemplate(templateId, companyId);
        if (template) {
          foundTemplate = template;
          break;
        }
      }

      // If template is not found for any company user has access to
      if (!foundTemplate) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      return res.json(foundTemplate);
    } catch (error) {
      console.error('Error fetching form template:', error);
      return res.status(500).json({ message: 'Failed to fetch form template' });
    }
  });

  // Form Fields routes
  app.get('/api/form-templates/:templateId/fields', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.templateId);

      // Skip company check to allow access from different companies
      // This allows for form template reuse across companies if needed

      // Get all the companies the user has access to
      const { userId } = ensureUserAuth(req);
      const userCompanies = await storage.getUserCompanies(userId);
      const companyIds = userCompanies.map(uc => uc.company_id);

      // Check if template exists for any of the user's companies
      let templateFound = false;

      for (const companyId of companyIds) {
        const template = await storage.getFormTemplate(templateId, companyId);
        if (template) {
          templateFound = true;
          break;
        }
      }

      // If template is not found for any company user has access to
      if (!templateFound) {
        console.log(`Template ${templateId} not found for any company user ${userId} has access to`);
        // Return empty array instead of 404 to make client-side handling more graceful
        return res.json([]);
      }

      const fields = await storage.getFormFieldsByTemplate(templateId);
      return res.json(fields || []);
    } catch (error) {
      console.error('Error fetching form fields:', error);
      // Return empty array on error to make client-side handling more graceful
      return res.json([]);
    }
  });

  app.post('/api/form-templates/:templateId/fields', authMiddleware, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.templateId);

      // Use our helper to ensure user is authenticated
      const { userId } = ensureUserAuth(req);

      // Get all the companies the user has access to
      const userCompanies = await storage.getUserCompanies(userId);
      const companyIds = userCompanies.map(uc => uc.company_id);

      // Check if template exists for any of the user's companies
      let templateCompanyId = null;
      let templateFound = false;

      for (const companyId of companyIds) {
        const template = await storage.getFormTemplate(templateId, companyId);
        if (template) {
          templateFound = true;
          templateCompanyId = companyId;
          break;
        }
      }

      // If template is not found for any company user has access to
      if (!templateFound) {
        console.log(`Template ${templateId} not found for any company user ${userId} has access to`);
        return res.status(404).json({ message: 'Form template not found for any of your companies. Make sure you have the right permissions.' });
      }

      const fieldData = { ...req.body, template_id: templateId };

      const result = insertFormFieldSchema.safeParse(fieldData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: formatZodError(result.error) });
      }

      const field = await storage.createFormField(result.data);
      return res.status(201).json(field);
    } catch (error) {
      console.error('Error creating form field:', error);
      return res.status(500).json({ message: 'Failed to create form field' });
    }
  });

  app.put('/api/form-fields/:id', authMiddleware, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Use our helper to ensure user is authenticated with company context
      const { companyId } = ensureUserAuth(req);

      // First fetch the field to get the template_id
      const field = await storage.getFormField(id);

      if (!field) {
        return res.status(404).json({ message: 'Form field not found' });
      }

      // Then fetch the template to check if user has access to the company
      const template = await storage.getFormTemplate(field.template_id, companyId);

      if (!template) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      const updatedField = await storage.updateFormField(id, req.body);

      if (!updatedField) {
        return res.status(500).json({ message: 'Failed to update form field' });
      }

      return res.json(updatedField);
    } catch (error) {
      console.error('Error updating form field:', error);
      return res.status(500).json({ message: 'Failed to update form field' });
    }
  });

  app.delete('/api/form-fields/:id', authMiddleware, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Use our helper to ensure user is authenticated with company context
      const { companyId } = ensureUserAuth(req);

      // First fetch the field to get the template_id
      const field = await storage.getFormField(id);

      if (!field) {
        return res.status(404).json({ message: 'Form field not found' });
      }

      // Then fetch the template to check if user has access to the company
      const template = await storage.getFormTemplate(field.template_id, companyId);

      if (!template) {
        return res.status(404).json({ message: 'Form template not found' });
      }

      const result = await storage.deleteFormField(id);

      if (!result) {
        return res.status(500).json({ message: 'Failed to delete form field' });
      }

      return res.json({ message: 'Form field deleted successfully' });
    } catch (error) {
      console.error('Error deleting form field:', error);
      return res.status(500).json({ message: 'Failed to delete form field' });
    }
  });

  // Form Submissions routes
  app.get('/api/companies/:companyId/loans/:loanId/form-submissions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const loanId = parseInt(req.params.loanId);

      const submissions = await storage.getFormSubmissionsByLoan(loanId, companyId);
      return res.json(submissions);
    } catch (error) {
      console.error('Error fetching form submissions:', error);
      return res.status(500).json({ message: 'Failed to fetch form submissions' });
    }
  });

  app.post('/api/companies/:companyId/loans/:loanId/form-submissions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const loanId = parseInt(req.params.loanId);

      // Use our helper to ensure user is authenticated
      const { userId } = ensureUserAuth(req);

      // Check if loan exists and belongs to the company
      const loan = await storage.getLoan(loanId, companyId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      const submissionData = {
        ...req.body,
        company_id: companyId,
        loan_id: loanId,
        submitted_by: userId
      };

      const result = insertFormSubmissionSchema.safeParse(submissionData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: formatZodError(result.error) });
      }

      const submission = await storage.createFormSubmission(result.data);
      return res.status(201).json(submission);
    } catch (error) {
      console.error('Error creating form submission:', error);
      return res.status(500).json({ message: 'Failed to create form submission' });
    }
  });

  app.get('/api/companies/:companyId/form-submissions/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      const submission = await storage.getFormSubmission(id, companyId);

      if (!submission) {
        return res.status(404).json({ message: 'Form submission not found' });
      }

      return res.json(submission);
    } catch (error) {
      console.error('Error fetching form submission:', error);
      return res.status(500).json({ message: 'Failed to fetch form submission' });
    }
  });

  app.put('/api/companies/:companyId/form-submissions/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the submission exists and belongs to the company
      const existingSubmission = await storage.getFormSubmission(id, companyId);

      if (!existingSubmission) {
        return res.status(404).json({ message: 'Form submission not found' });
      }

      const updatedSubmission = await storage.updateFormSubmission(id, companyId, req.body);

      if (!updatedSubmission) {
        return res.status(500).json({ message: 'Failed to update form submission' });
      }

      return res.json(updatedSubmission);
    } catch (error) {
      console.error('Error updating form submission:', error);
      return res.status(500).json({ message: 'Failed to update form submission' });
    }
  });

  app.delete('/api/companies/:companyId/form-submissions/:id', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'manager']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const id = parseInt(req.params.id);

      // Make sure the submission exists and belongs to the company
      const existingSubmission = await storage.getFormSubmission(id, companyId);

      if (!existingSubmission) {
        return res.status(404).json({ message: 'Form submission not found' });
      }

      const result = await storage.deleteFormSubmission(id, companyId);

      if (!result) {
        return res.status(500).json({ message: 'Failed to delete form submission' });
      }

      return res.json({ message: 'Form submission deleted successfully' });
    } catch (error) {
      console.error('Error deleting form submission:', error);
      return res.status(500).json({ message: 'Failed to delete form submission' });
    }
  });

  // Reports API

  // Expense Management Routes
  app.post('/api/companies/:companyId/expenses', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Add company_id to the request body
      const dataWithCompanyId = {
        ...req.body,
        company_id: companyId
      };

      // Validate expense data with company_id added
      const result = insertExpenseSchema.safeParse(dataWithCompanyId);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid expense data',
          errors: formatZodError(result.error)
        });
      }

      // Create expense
      const expense = await storage.createExpense(result.data);

      // Create double-entry accounting records for the expense
      try {
        // Get the cash/bank account and appropriate expense account
        const cashAccount = await getAccountByCode(expense.company_id, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
        // If Cash account not found, try Bank account
        const cashOrBankAccount = cashAccount || await getAccountByCode(expense.company_id, SYSTEM_ACCOUNT_CODES.BANK, "Bank");

        console.log(`Cash/Bank account found: ${!!cashOrBankAccount}, ID: ${cashOrBankAccount?.id}, Name: ${cashOrBankAccount?.account_name}`);

        // Get the appropriate expense account based on the expense type
        // Default to General Expenses if no specific account exists
        let expenseAccountCode = SYSTEM_ACCOUNT_CODES.GENERAL_EXPENSES;

        // Map expense type to specific expense accounts
        switch(expense.expense_type) {
          case 'rent':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.RENT_EXPENSE;
            break;
          case 'salary':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.SALARY_EXPENSE;
            break;
          case 'utilities':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.UTILITIES_EXPENSE;
            break;
          case 'marketing':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.MARKETING_EXPENSE;
            break;
          case 'transport':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.TRANSPORT_EXPENSE;
            break;
          case 'office_supplies':
            expenseAccountCode = SYSTEM_ACCOUNT_CODES.OFFICE_SUPPLIES_EXPENSE;
            break;
          // Add more mappings as needed
        }

        const expenseAccount = await getAccountByCode(expense.company_id, expenseAccountCode, "General Expense");

        console.log(`Expense account found: ${!!expenseAccount}, ID: ${expenseAccount?.id}, Name: ${expenseAccount?.account_name}`);

        if (!cashOrBankAccount || !expenseAccount) {
          console.error(`Required accounts not found for company ${expense.company_id}`);
        } else {
          // Create a journal entry for the expense
          await createJournalEntry({
            company_id: expense.company_id,
            branch_id: expense.branch_id,
            transaction_date: expense.expense_date || new Date(),
            description: expense.description || `Expense: ${expense.expense_type}`,
            reference_type: 'expense',
            reference_id: expense.id,
            created_by: req.user?.id,
            entries: {
              // Cash/Bank decreases (credit)
              credit: {
                account_id: cashOrBankAccount.id,
                amount: Number(expense.amount)
              },
              // Expense increases (debit)
              debit: {
                account_id: expenseAccount.id,
                amount: Number(expense.amount)
              }
            }
          });

          console.log(`Created journal entry for expense ${expense.id}`);
        }
      } catch (journalError) {
        console.error('Failed to create journal entry for expense:', journalError);
        // We don't fail the expense creation if the journal entry fails
      }

      return res.status(201).json(expense);
    } catch (error) {
      console.error('Create expense error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/expenses', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract filter parameters
      const filters: ExpenseFilters = {};

      if (req.query.startDate && typeof req.query.startDate === 'string') {
        filters.startDate = req.query.startDate;
      }

      if (req.query.endDate && typeof req.query.endDate === 'string') {
        filters.endDate = req.query.endDate;
      }

      if (req.query.type && typeof req.query.type === 'string') {
        filters.type = req.query.type;
      }

      if (req.query.branchId && typeof req.query.branchId === 'string') {
        filters.branchId = parseInt(req.query.branchId);
      }

      if (req.query.minAmount && typeof req.query.minAmount === 'string') {
        filters.minAmount = parseFloat(req.query.minAmount);
      }

      if (req.query.maxAmount && typeof req.query.maxAmount === 'string') {
        filters.maxAmount = parseFloat(req.query.maxAmount);
      }

      if (req.query.paymentMethod && typeof req.query.paymentMethod === 'string') {
        filters.paymentMethod = req.query.paymentMethod;
      }

      // Get expenses with filters
      const expenses = await storage.getExpenses(companyId, filters);

      return res.json(expenses);
    } catch (error) {
      console.error('Get expenses error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Get expense
      const expense = await storage.getExpenseById(id);

      if (!expense) {
        return res.status(404).json({ message: 'Expense not found' });
      }

      // Verify the user has access to this expense's company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== expense.company_id) {
        return res.status(403).json({ message: 'Access denied to this expense' });
      }

      return res.json(expense);
    } catch (error) {
      console.error('Get expense error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Get existing expense
      const existingExpense = await storage.getExpenseById(id);

      if (!existingExpense) {
        return res.status(404).json({ message: 'Expense not found' });
      }

      // Verify the user has access to this expense's company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== existingExpense.company_id) {
        return res.status(403).json({ message: 'Access denied to this expense' });
      }

      // Validate the update data
      const result = insertExpenseSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid expense data',
          errors: formatZodError(result.error)
        });
      }

      // Update expense
      const updatedExpense = await storage.updateExpense(id, result.data);

      return res.json(updatedExpense);
    } catch (error) {
      console.error('Update expense error:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  app.delete('/api/expenses/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const id = parseInt(req.params.id);

      // Get existing expense
      const existingExpense = await storage.getExpenseById(id);

      if (!existingExpense) {
        return res.status(404).json({ message: 'Expense not found' });
      }

      // Verify the user has access to this expense's company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== existingExpense.company_id) {
        return res.status(403).json({ message: 'Access denied to this expense' });
      }

      // Delete expense
      await storage.deleteExpense(id);

      return res.json({ message: 'Expense deleted successfully' });
    } catch (error) {
      console.error('Delete expense error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Report Routes
  app.get('/api/companies/:companyId/reports/daily-collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract filter parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;
      const status = req.query.status as string;
      const agentId = req.query.agentId ? parseInt(req.query.agentId as string) : undefined;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;
      const paymentMethod = req.query.paymentMethod as string;

      // Log request details for debugging
      errorLogger.logInfo(
        `Daily collections report request for company ${companyId}`,
        'daily-collections-route',
        {
          query: {
            startDate,
            endDate,
            status,
            agentId,
            branchId,
            paymentMethod
          },
          userId: req.user.id
        }
      );

      // Validate required parameters
      if (!startDate || !endDate) {
        errorLogger.logWarning(
          `Missing date parameters for daily collection report`,
          'daily-collections-route',
          { companyId, startDate, endDate }
        );
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Validate date format
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        errorLogger.logWarning(
          `Invalid date format in daily collection report request`,
          'daily-collections-route',
          { companyId, startDate, endDate }
        );
        return res.status(400).json({ message: 'Invalid date format. Please use YYYY-MM-DD format.' });
      }

      // Generate report
      const report = await storage.getDailyCollectionsReport(
        companyId,
        startDate,
        endDate,
        status,
        agentId,
        branchId,
        paymentMethod
      );

      errorLogger.logDebug(
        `Daily collections report generated successfully`,
        'daily-collections-route',
        {
          companyId,
          recordCount: report.rawData.length,
          dailyDataCount: report.dailyData.length
        }
      );

      return res.json(report);
    } catch (error) {
      errorLogger.logError(
        `Failed to generate daily collections report`,
        'daily-collections-route',
        error as Error
      );
      return res.status(500).json(
        errorLogger.formatErrorResponse(error as Error, 'Failed to generate collections report')
      );
    }
  });

  app.get('/api/companies/:companyId/reports/day-sheet', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const date = req.query.date as string;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;

      // Validate required parameters
      if (!date) {
        return res.status(400).json({ message: 'Date is required' });
      }

      // Generate report
      const report = await storage.getDaySheetReport(
        companyId,
        date,
        branchId
      );

      return res.json(report);
    } catch (error) {
      console.error('Day sheet report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/reports/customer/:customerId', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const customerId = parseInt(req.params.customerId);

      // Extract optional date range
      const startDate = req.query.startDate as string | undefined;
      const endDate = req.query.endDate as string | undefined;

      // Generate report
      const report = await storage.getCustomerReport(
        companyId,
        customerId,
        startDate,
        endDate
      );

      return res.json(report);
    } catch (error) {
      console.error('Customer report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  app.get('/api/companies/:companyId/reports/agent/:agentId', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const agentId = parseInt(req.params.agentId);

      // Extract optional date range
      const startDate = req.query.startDate as string | undefined;
      const endDate = req.query.endDate as string | undefined;

      // Generate report
      const report = await storage.getAgentReport(
        companyId,
        agentId,
        startDate,
        endDate
      );

      return res.json(report);
    } catch (error) {
      console.error('Agent report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Profit & Loss Report - New endpoint
  app.get('/api/companies/:companyId/reports/profit-loss', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Generate report
      const report = await storage.getProfitLossReport(
        companyId,
        startDate,
        endDate,
        branchId
      );

      return res.json(report);
    } catch (error) {
      console.error('Profit/loss report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Profit & Loss Report - Legacy endpoint for backward compatibility
  app.get('/api/companies/:companyId/financial-reports/profit-loss', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;
      const branchId = req.query.branchId ? parseInt(req.query.branchId as string) : undefined;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Generate report
      const report = await storage.getProfitLossReport(
        companyId,
        startDate,
        endDate,
        branchId
      );

      return res.json(report);
    } catch (error) {
      console.error('Profit/loss report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Trial Balance Report - New endpoint
  app.get('/api/companies/:companyId/reports/trial-balance', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const asOfDate = req.query.asOfDate as string;

      // Import the trial balance function directly to avoid circular dependencies
      const { getTrialBalanceReport } = await import('./financialManagement');

      // Generate report
      const report = await getTrialBalanceReport(companyId, asOfDate);

      return res.json(report);
    } catch (error) {
      console.error('Trial Balance report error:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Trial Balance Report - Legacy endpoint for backward compatibility
  app.get('/api/companies/:companyId/financial-reports/trial-balance', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const asOfDate = req.query.asOfDate as string;

      // Import the trial balance function directly to avoid circular dependencies
      const { getTrialBalanceReport } = await import('./financialManagement');

      // Generate report
      const report = await getTrialBalanceReport(companyId, asOfDate);

      return res.json(report);
    } catch (error) {
      console.error('Trial Balance report error:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Balance Sheet Report - New endpoint
  app.get('/api/companies/:companyId/reports/balance-sheet', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const asOfDate = req.query.asOfDate as string;

      // Validate required parameters
      if (!asOfDate) {
        return res.status(400).json({ message: 'As of date is required' });
      }

      // Generate report
      const report = await storage.getBalanceSheetReport(companyId, asOfDate);

      return res.json(report);
    } catch (error) {
      console.error('Balance Sheet report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Balance Sheet Report - Legacy endpoint for backward compatibility
  app.get('/api/companies/:companyId/financial-reports/balance-sheet', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const asOfDate = req.query.asOfDate as string;

      // Validate required parameters
      if (!asOfDate) {
        return res.status(400).json({ message: 'As of date is required' });
      }

      // Generate report
      const report = await storage.getBalanceSheetReport(companyId, asOfDate);

      return res.json(report);
    } catch (error) {
      console.error('Balance Sheet report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Cash Flow Report - New endpoint
  app.get('/api/companies/:companyId/reports/cash-flow', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Generate report
      const report = await storage.getCashFlowReport(companyId, startDate, endDate);

      return res.json(report);
    } catch (error) {
      console.error('Cash Flow report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Cash Flow Report - Legacy endpoint for backward compatibility
  app.get('/api/companies/:companyId/financial-reports/cash-flow', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Generate report
      const report = await storage.getCashFlowReport(companyId, startDate, endDate);

      return res.json(report);
    } catch (error) {
      console.error('Cash Flow report error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Account Statement Report - New endpoint
  app.get('/api/companies/:companyId/reports/accounts', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Validate date format
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        errorLogger.logWarning(
          `Invalid date format in accounts report request`,
          'accounts-report-route',
          { companyId, startDate, endDate }
        );
        return res.status(400).json({ message: 'Invalid date format. Please use YYYY-MM-DD format.' });
      }

      // Check if end date is after start date
      if (endDateObj < startDateObj) {
        return res.status(400).json({ message: 'End date must be after start date' });
      }

      // Generate report
      try {
        const report = await storage.getAccountBalanceReport(companyId, startDate, endDate);
        return res.json(report);
      } catch (reportError) {
        errorLogger.logError(
          `Error generating account balance report`,
          'accounts-report-route',
          reportError as Error
        );
        return res.status(500).json({
          message: 'Failed to generate account balance report',
          error: reportError instanceof Error ? reportError.message : 'Unknown error'
        });
      }
    } catch (error) {
      errorLogger.logError('Account Statement report error:', 'accounts-report-route', error as Error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Account Statement Report - Legacy endpoint for backward compatibility
  app.get('/api/companies/:companyId/financial-reports/accounts', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract parameters
      const startDate = req.query.startDate as string;
      const endDate = req.query.endDate as string;

      // Validate required parameters
      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      // Validate date format
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);

      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        errorLogger.logWarning(
          `Invalid date format in accounts report request (legacy endpoint)`,
          'accounts-report-route-legacy',
          { companyId, startDate, endDate }
        );
        return res.status(400).json({ message: 'Invalid date format. Please use YYYY-MM-DD format.' });
      }

      // Check if end date is after start date
      if (endDateObj < startDateObj) {
        return res.status(400).json({ message: 'End date must be after start date' });
      }

      // Generate report
      try {
        const report = await storage.getAccountBalanceReport(companyId, startDate, endDate);
        return res.json(report);
      } catch (reportError) {
        errorLogger.logError(
          `Error generating account balance report (legacy endpoint)`,
          'accounts-report-route-legacy',
          reportError as Error
        );
        return res.status(500).json({
          message: 'Failed to generate account balance report',
          error: reportError instanceof Error ? reportError.message : 'Unknown error'
        });
      }
    } catch (error) {
      errorLogger.logError('Account Statement report error (legacy endpoint):', 'accounts-report-route-legacy', error as Error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // =====================
  // FINANCIAL MANAGEMENT ROUTES
  // =====================

  // Account Management Routes
  app.get('/api/companies/:companyId/accounts', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accounts = await storage.getAccountsByCompany(companyId);
      res.json(accounts);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  // Initialize system accounts for a company (admin only)
  app.post('/api/companies/:companyId/initialize-system-accounts', authMiddleware, requireCompanyAccess, requireRole(['company_admin', 'saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Check if accounts already exist
      const existingAccounts = await storage.getAccountsByCompany(companyId);

      if (existingAccounts && existingAccounts.length > 0) {
        return res.status(400).json({
          message: 'System accounts already initialized for this company',
          accountsCount: existingAccounts.length
        });
      }

      // Initialize system accounts
      await initializeSystemAccounts(companyId, req.user?.id);

      // Get the newly created accounts
      const accounts = await storage.getAccountsByCompany(companyId);

      return res.status(201).json({
        message: 'System accounts initialized successfully',
        accountsCount: accounts.length,
        accounts
      });
    } catch (error) {
      console.error('Error initializing system accounts:', error);
      res.status(500).json({ message: 'Failed to initialize system accounts' });
    }
  });

  // Test endpoint for journal entry creation - simplified auth for testing
  app.post('/api/companies/:companyId/test-journal-entry', async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const testType = req.query.type as string || 'expense'; // Default to expense, options: 'loan', 'collection'

      // Get accounts for testing
      const accounts = await storage.getAccountsByCompany(companyId);

      if (!accounts || accounts.length === 0) {
        console.log(`No accounts found for company ${companyId}, initializing system accounts...`);
        // Initialize system accounts for the company
        await initializeSystemAccounts(companyId, req.user?.id);
        console.log(`System accounts initialized for company ${companyId}`);
      }

      // Get the necessary accounts for test transactions
      const cashAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.CASH, "Cash");
      const expenseAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.OFFICE_SUPPLIES_EXPENSE, "Office Supplies Expense");
      const loanReceivableAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE, "Loan Receivable");
      const interestIncomeAccount = await getAccountByCode(companyId, SYSTEM_ACCOUNT_CODES.INTEREST_INCOME, "Interest Income");

      // Verify required accounts based on test type
      if (testType === 'expense' && (!cashAccount || !expenseAccount)) {
        return res.status(500).json({
          message: 'Required accounts for expense test not found',
          cashAccount: cashAccount ? `Found: ${cashAccount.id} - ${cashAccount.account_name}` : 'Not found',
          expenseAccount: expenseAccount ? `Found: ${expenseAccount.id} - ${expenseAccount.account_name}` : 'Not found',
          availableAccounts: accounts.map(a => `${a.id} - ${a.account_code} - ${a.account_name}`)
        });
      } else if (testType === 'loan' && (!cashAccount || !loanReceivableAccount)) {
        return res.status(500).json({
          message: 'Required accounts for loan test not found',
          cashAccount: cashAccount ? `Found: ${cashAccount.id} - ${cashAccount.account_name}` : 'Not found',
          loanReceivableAccount: loanReceivableAccount ? `Found: ${loanReceivableAccount.id} - ${loanReceivableAccount.account_name}` : 'Not found',
          availableAccounts: accounts.map(a => `${a.id} - ${a.account_code} - ${a.account_name}`)
        });
      } else if (testType === 'collection' && (!cashAccount || !loanReceivableAccount || !interestIncomeAccount)) {
        return res.status(500).json({
          message: 'Required accounts for collection test not found',
          cashAccount: cashAccount ? `Found: ${cashAccount.id} - ${cashAccount.account_name}` : 'Not found',
          loanReceivableAccount: loanReceivableAccount ? `Found: ${loanReceivableAccount.id} - ${loanReceivableAccount.account_name}` : 'Not found',
          interestIncomeAccount: interestIncomeAccount ? `Found: ${interestIncomeAccount.id} - ${interestIncomeAccount.account_name}` : 'Not found',
          availableAccounts: accounts.map(a => `${a.id} - ${a.account_code} - ${a.account_name}`)
        });
      }

      // Create a test journal entry
      try {
        const testAmount = 100;
        let journalEntry;
        let description;
        let reference_type;

        if (testType === 'loan') {
          console.log(`Creating test LOAN journal entry with cash account ${cashAccount!.id} and loan receivable account ${loanReceivableAccount!.id}`);
          description = 'Test loan disbursement';
          reference_type = 'loan';

          journalEntry = await createJournalEntry({
            company_id: companyId,
            branch_id: null,
            transaction_date: new Date(),
            description,
            reference_type,
            reference_id: 999, // Dummy reference ID
            created_by: req.user?.id,
            entries: {
              // Loan Receivable increases (debit)
              debit: {
                account_id: loanReceivableAccount!.id,
                amount: testAmount
              },
              // Cash decreases (credit)
              credit: {
                account_id: cashAccount!.id,
                amount: testAmount
              }
            }
          });
        } else if (testType === 'collection') {
          console.log(`Creating test COLLECTION journal entry with cash account ${cashAccount!.id}, loan receivable account ${loanReceivableAccount!.id} and interest income account ${interestIncomeAccount!.id}`);
          description = 'Test loan collection';
          reference_type = 'collection';

          // For collection, we have principal payment (90) and interest payment (10)
          journalEntry = await createJournalEntry({
            company_id: companyId,
            branch_id: null,
            transaction_date: new Date(),
            description,
            reference_type,
            reference_id: 999, // Dummy reference ID
            created_by: req.user?.id,
            entries: {
              // Cash increases (debit)
              debit: {
                account_id: cashAccount!.id,
                amount: testAmount
              },
              // Loan Receivable decreases (credit) - principal component
              creditPrincipal: {
                account_id: loanReceivableAccount!.id,
                amount: 90 // Principal
              },
              // Interest Income increases (credit) - interest component
              creditInterest: {
                account_id: interestIncomeAccount!.id,
                amount: 10 // Interest
              }
            }
          });
        } else {
          // Default expense entry
          console.log(`Creating test EXPENSE journal entry with cash account ${cashAccount!.id} and expense account ${expenseAccount!.id}`);
          description = 'Test expense journal entry';
          reference_type = 'expense';

          journalEntry = await createJournalEntry({
            company_id: companyId,
            branch_id: null,
            transaction_date: new Date(),
            description,
            reference_type,
            reference_id: 999, // Dummy reference ID
            created_by: req.user?.id,
            entries: {
              // Expense increases (debit)
              debit: {
                account_id: expenseAccount!.id,
                amount: testAmount
              },
              // Cash decreases (credit)
              credit: {
                account_id: cashAccount!.id,
                amount: testAmount
              }
            }
          });
        }

        // Get all transactions for the company after creating this journal entry
        const result = await storage.getTransactionsByCompany(companyId, {});
        const allTransactions = result.transactions;

        // Get the newly created transactions (last 2)
        const newTransactions = allTransactions.slice(-2);

        // Organize transactions by account
        const accountsWithTransactions = await Promise.all(
          Array.from(new Set(allTransactions.map(t => t.account_id))).map(async accountId => {
            const account = await storage.getAccount(accountId, companyId);
            if (!account) return null;

            const transactions = allTransactions.filter(t => t.account_id === accountId);
            const totalDebits = transactions
              .filter(t => t.transaction_type === 'debit')
              .reduce((sum, t) => sum + parseFloat(t.amount), 0);

            const totalCredits = transactions
              .filter(t => t.transaction_type === 'credit')
              .reduce((sum, t) => sum + parseFloat(t.amount), 0);

            return {
              account: {
                id: account.id,
                name: account.account_name,
                code: account.account_code,
                type: account.account_type,
                category: account.category
              },
              transactionCount: transactions.length,
              totalDebits,
              totalCredits,
              balance: totalDebits - totalCredits
            };
          })
        ).then(accounts => accounts.filter(a => a !== null));

        return res.status(201).json({
          message: `Test ${testType} journal entry created successfully`,
          journalEntry,
          testType,
          description,
          reference_type,
          accounts: {
            cash: cashAccount ? {
              id: cashAccount.id,
              name: cashAccount.account_name,
              code: cashAccount.account_code
            } : null,
            expense: expenseAccount ? {
              id: expenseAccount.id,
              name: expenseAccount.account_name,
              code: expenseAccount.account_code
            } : null,
            loanReceivable: loanReceivableAccount ? {
              id: loanReceivableAccount.id,
              name: loanReceivableAccount.account_name,
              code: loanReceivableAccount.account_code
            } : null,
            interestIncome: interestIncomeAccount ? {
              id: interestIncomeAccount.id,
              name: interestIncomeAccount.account_name,
              code: interestIncomeAccount.account_code
            } : null
          },
          transactionStats: {
            totalTransactions: allTransactions.length,
            debitTransactions: allTransactions.filter(t => t.transaction_type === 'debit').length,
            creditTransactions: allTransactions.filter(t => t.transaction_type === 'credit').length,
            byReferenceType: {
              expense: allTransactions.filter(t => t.reference_type === 'expense').length,
              loan: allTransactions.filter(t => t.reference_type === 'loan').length,
              collection: allTransactions.filter(t => t.reference_type === 'collection').length,
              investment: allTransactions.filter(t => t.reference_type === 'investment').length,
              withdrawal: allTransactions.filter(t => t.reference_type === 'withdrawal').length,
              transfer: allTransactions.filter(t => t.reference_type === 'transfer').length,
              adjustment: allTransactions.filter(t => t.reference_type === 'adjustment').length
            }
          },
          accountSummary: accountsWithTransactions,
          debugInfo: {
            transactionTypes: newTransactions.map(t => ({
              id: t.id,
              type: t.transaction_type,
              reference_type: t.reference_type,
              account_id: t.account_id,
              amount: t.amount,
              date: t.transaction_date
            }))
          }
        });
      } catch (journalError) {
        console.error('Failed to create test journal entry:', journalError);
        return res.status(500).json({
          message: 'Failed to create test journal entry',
          error: (journalError as Error).message
        });
      }
    } catch (error) {
      console.error('Test journal entry error:', error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  app.get('/api/companies/:companyId/accounts/:accountId', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountId = parseInt(req.params.accountId);
      const account = await storage.getAccount(accountId, companyId);

      if (!account) {
        return res.status(404).json({ message: 'Account not found' });
      }

      res.json(account);
    } catch (error) {
      console.error('Error fetching account:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  app.post('/api/companies/:companyId/accounts', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountData = {
        ...req.body,
        company_id: companyId
      };

      const newAccount = await storage.createAccount(accountData);
      res.status(201).json(newAccount);
    } catch (error) {
      console.error('Error creating account:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  app.patch('/api/companies/:companyId/accounts/:accountId', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountId = parseInt(req.params.accountId);

      const updatedAccount = await storage.updateAccount(accountId, companyId, req.body);

      if (!updatedAccount) {
        return res.status(404).json({ message: 'Account not found' });
      }

      res.json(updatedAccount);
    } catch (error) {
      console.error('Error updating account:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  app.delete('/api/companies/:companyId/accounts/:accountId', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountId = parseInt(req.params.accountId);

      // Check if account has transactions before deleting
      const transactions = await storage.getTransactionsByAccount(accountId, companyId);

      if (transactions && transactions.length > 0) {
        return res.status(400).json({
          message: 'Cannot delete account with existing transactions. Deactivate the account instead.'
        });
      }

      const result = await storage.deleteAccount(accountId, companyId);

      if (!result) {
        return res.status(400).json({
          message: 'Cannot delete this account. It may be a system account or have child accounts.'
        });
      }

      res.status(204).send();
    } catch (error) {
      console.error('Error deleting account:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  // Account transactions
  app.get('/api/companies/:companyId/accounts/:accountId/transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accountId = parseInt(req.params.accountId);

      const transactions = await storage.getTransactionsByAccount(accountId, companyId);
      res.json(transactions);
    } catch (error) {
      console.error('Error fetching account transactions:', error);
      res.status(500).json({ message: 'Server error' });
    }
  });

  // Transaction Management Routes
  app.get('/api/companies/:companyId/transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Extract pagination and filter parameters from query
      const page = req.query.page ? parseInt(req.query.page as string) : 1;
      const limit = req.query.limit ? parseInt(req.query.limit as string) : 10;
      const startDate = req.query.startDate as string | undefined;
      const endDate = req.query.endDate as string | undefined;
      const accountType = req.query.accountType as string | undefined;
      const transactionType = req.query.transactionType as string | undefined;
      const referenceType = req.query.referenceType as string | undefined;
      const searchTerm = req.query.search as string | undefined;

      // Fetch transactions with pagination and filtering
      const result = await storage.getTransactionsByCompany(companyId, {
        page,
        limit,
        startDate,
        endDate,
        accountType,
        transactionType,
        referenceType,
        searchTerm
      });

      // Return the data along with pagination metadata
      return res.json({
        transactions: result.transactions,
        pagination: {
          page,
          limit,
          totalCount: result.totalCount,
          totalPages: Math.ceil(result.totalCount / limit)
        }
      });
    } catch (error) {
      console.error('Failed to fetch transactions:', error);
      return res.status(500).json({ message: 'Failed to fetch transactions' });
    }
  });

  // Get a specific transaction
  app.get('/api/companies/:companyId/transactions/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const transactionId = parseInt(req.params.id);

      const transaction = await storage.getTransaction(transactionId, companyId);

      if (!transaction) {
        return res.status(404).json({ message: 'Transaction not found' });
      }

      // Enrich with account information
      if (transaction.account_id) {
        const account = await storage.getAccount(transaction.account_id, companyId);
        if (account) {
          transaction.account = {
            id: account.id,
            account_code: account.account_code,
            account_name: account.account_name,
            account_type: account.account_type
          };
        }
      }

      return res.json(transaction);
    } catch (error) {
      console.error('Failed to fetch transaction:', error);
      return res.status(500).json({ message: 'Failed to fetch transaction' });
    }
  });

  // Create a new transaction
  app.post('/api/companies/:companyId/transactions', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Prepare data for validation - convert string dates to Date objects
      const requestData = { ...req.body, company_id: companyId };

      // Parse transaction_date if it exists and is a string
      if (requestData.transaction_date && typeof requestData.transaction_date === 'string') {
        requestData.transaction_date = new Date(requestData.transaction_date);
      }

      // Validate the request body
      const result = insertTransactionSchema.safeParse(requestData);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid transaction data',
          errors: formatZodError(result.error)
        });
      }

      // Create the transaction
      const transaction = await storage.createTransaction(result.data);

      return res.status(201).json(transaction);
    } catch (error) {
      console.error('Failed to create transaction:', error);
      return res.status(500).json({ message: 'Failed to create transaction' });
    }
  });

  // Update a transaction
  app.patch('/api/companies/:companyId/transactions/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const transactionId = parseInt(req.params.id);

      // First, check if the transaction exists
      const existingTransaction = await storage.getTransaction(transactionId, companyId);

      if (!existingTransaction) {
        return res.status(404).json({ message: 'Transaction not found' });
      }

      // Prepare request data - convert transaction_date from string to Date if needed
      const requestData = { ...req.body };

      // Parse transaction_date if it exists and is a string
      if (requestData.transaction_date && typeof requestData.transaction_date === 'string') {
        requestData.transaction_date = new Date(requestData.transaction_date);
      }

      // Validate the update data - modifying schema to expect Date for transaction_date
      const updateSchema = z.object({
        transaction_date: z.date().optional(),
        description: z.string().min(1).optional(),
        account_id: z.number().positive().optional(),
        amount: z.number().positive().optional(),
        transaction_type: z.enum(['debit', 'credit']).optional(),
        reference_type: z.string().optional().nullable(),
        reference_id: z.number().optional().nullable()
      });

      const result = updateSchema.safeParse(requestData);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid update data',
          errors: formatZodError(result.error)
        });
      }

      // Update the transaction
      const [updatedTransaction] = await db
        .update(transactions)
        .set(result.data)
        .where(and(
          eq(transactions.id, transactionId),
          eq(transactions.company_id, companyId)
        ))
        .returning();

      if (!updatedTransaction) {
        return res.status(500).json({ message: 'Failed to update transaction' });
      }

      return res.json(updatedTransaction);
    } catch (error) {
      console.error('Failed to update transaction:', error);
      return res.status(500).json({ message: 'Failed to update transaction' });
    }
  });

  // Delete a transaction
  app.delete('/api/companies/:companyId/transactions/:id', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const transactionId = parseInt(req.params.id);

      // First, check if the transaction exists
      const existingTransaction = await storage.getTransaction(transactionId, companyId);

      if (!existingTransaction) {
        return res.status(404).json({ message: 'Transaction not found' });
      }

      // Delete the transaction
      const result = await db
        .delete(transactions)
        .where(and(
          eq(transactions.id, transactionId),
          eq(transactions.company_id, companyId)
        ));

      return res.json({ success: true, message: 'Transaction deleted successfully' });
    } catch (error) {
      console.error('Failed to delete transaction:', error);
      return res.status(500).json({ message: 'Failed to delete transaction' });
    }
  });

  // Update all transaction reference codes to company-specific format
  app.post('/api/companies/:companyId/transactions/update-reference-codes', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      if (isNaN(companyId)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Get all transactions for the company
      const result = await storage.getTransactionsByCompany(companyId, {});
      const companyTransactions = result.transactions;
      console.log(`Found ${companyTransactions.length} transactions for company ${companyId}`);

      if (companyTransactions.length === 0) {
        return res.status(200).json({
          message: 'No transactions found for this company',
          updated: 0
        });
      }

      // Get company prefix
      const companyPrefix = await getCompanyName(companyId);
      const transactionStorage = new TransactionStorage();

      // Update each transaction with a company-specific reference code
      let updatedCount = 0;
      const errors = [];

      for (const transaction of companyTransactions) {
        try {
          // Only update transactions that don't already have a reference code or have an empty reference code
          if (!transaction.transaction_reference_code || transaction.transaction_reference_code.trim() === '') {
            // Get the highest existing transaction reference code for this company
            const highestSerial = await transactionStorage.getHighestTransactionSerial(companyId, `${companyPrefix}-T-`);
            const nextSerial = highestSerial + 1;
            const serialString = nextSerial.toString().padStart(3, '0');
            const transactionReferenceCode = `${companyPrefix}-T-${serialString}`;

            await storage.updateTransaction(
              transaction.id,
              companyId,
              { transaction_reference_code: transactionReferenceCode }
            );

            updatedCount++;
            console.log(`Updated transaction ${transaction.id} with reference code: ${transactionReferenceCode}`);
          }
        } catch (error) {
          console.error(`Error updating transaction ID ${transaction.id}:`, error);
          errors.push({
            transactionId: transaction.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return res.status(200).json({
        message: `Updated ${updatedCount} transactions with company-specific reference codes`,
        totalTransactions: companyTransactions.length,
        updatedTransactions: updatedCount,
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error updating transaction reference codes:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Original endpoint with authentication
  app.post('/api/companies/:companyId/delete-test-data', authMiddleware, requireRole(['company_admin', 'saas_admin']), requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      console.log(`Received request to delete all test data for company ${companyId}`);

      // Confirm user has required permissions
      const { userId } = ensureUserAuth(req);
      console.log(`User ${userId} is attempting to delete all test data for company ${companyId}`);

      // Call the function to delete all test data
      const result = await deleteAllTestData(companyId);

      if (!result.deleted) {
        return res.status(500).json({
          message: 'Failed to delete test data completely',
          partial_results: result.counts
        });
      }

      return res.json({
        message: 'All test data successfully deleted',
        deleted_counts: result.counts
      });
    } catch (error) {
      console.error('Error deleting test data:', error);
      return res.status(500).json({ message: 'Server error while deleting test data' });
    }
  });

  // Special testing endpoint that bypasses authentication
  app.post('/api/test/companies/:companyId/delete-test-data', async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      console.log(`Received TEST request to delete all test data for company ${companyId}`);

      // Call the function to delete all test data
      const result = await deleteAllTestData(companyId);

      if (!result.deleted) {
        return res.status(500).json({
          message: 'Failed to delete test data completely',
          partial_results: result.counts
        });
      }

      return res.json({
        message: 'All test data successfully deleted',
        deleted_counts: result.counts
      });
    } catch (error) {
      console.error('Error deleting test data:', error);
      return res.status(500).json({ message: 'Server error while deleting test data' });
    }
  });

  // Special testing endpoint for initializing system accounts
  app.post('/api/test/companies/:companyId/initialize-accounts', async (req: Request, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      console.log(`[TEST] Request to initialize system accounts for company ${companyId}`);

      if (!companyId || isNaN(companyId)) {
        return res.status(400).json({ error: "Valid company ID is required" });
      }

      // Check if company exists with raw SQL to avoid circular dependencies
      const companyExists = await db.execute(`SELECT id FROM companies WHERE id = ${companyId} LIMIT 1`);
      if (!companyExists || !companyExists.rows || companyExists.rows.length === 0) {
        return res.status(404).json({ error: "Company not found" });
      }

      // Call the initialize function
      await initializeSystemAccounts(companyId);

      // Get the created accounts to return in the response
      const createdAccounts = await db.execute(`SELECT * FROM accounts WHERE company_id = ${companyId}`);

      return res.status(200).json({
        success: true,
        message: `Initialized system accounts for company ${companyId}`,
        accounts: createdAccounts.rows
      });
    } catch (error) {
      console.error("[TEST] Error initializing system accounts:", error);
      return res.status(500).json({ error: "Failed to initialize system accounts", details: String(error) });
    }
  });

  // Collection Status Update Routes

  // Run status update job manually (admin only)
  app.post('/api/collections/update-statuses', authMiddleware, requireRole(['company_admin', 'saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      console.log('Manual collection status update requested');

      // Run the status update job
      const result = await collectionStatusService.updateCollectionStatuses();

      return res.json({
        message: 'Collection statuses updated successfully',
        updated: result.updated,
        errors: result.errors
      });
    } catch (error) {
      errorLogger.logError('Error running manual status update', 'status-update-route', error as Error);
      return res.status(500).json({
        message: 'Failed to update collection statuses',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });

  // Batch update collection statuses
  app.post('/api/collections/batch-update-status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { ids, status } = req.body;

      if (!ids || !Array.isArray(ids) || ids.length === 0) {
        return res.status(400).json({ message: 'Collection IDs are required' });
      }

      if (!status || !['pending', 'due', 'overdue', 'completed', 'cancelled'].includes(status)) {
        return res.status(400).json({ message: 'Valid status is required' });
      }

      const result = await collectionStatusService.batchUpdateCollectionStatus(ids, status);

      return res.json({
        message: 'Collection statuses updated',
        success: result.success,
        failed: result.failed
      });
    } catch (error) {
      errorLogger.logError('Error in batch status update', 'batch-status-update', error as Error);
      return res.status(500).json({ message: 'Failed to update collection statuses' });
    }
  });

  // Notification Routes

  // Send upcoming collection notifications
  app.post('/api/notifications/upcoming-collections', authMiddleware, requireRole(['company_admin', 'saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const daysAhead = req.body.daysAhead ? parseInt(req.body.daysAhead) : 3;

      if (isNaN(daysAhead) || daysAhead < 1 || daysAhead > 30) {
        return res.status(400).json({ message: 'Days ahead must be between 1 and 30' });
      }

      const result = await notificationService.sendUpcomingCollectionNotifications(daysAhead);

      return res.json({
        message: `Sent ${result.count} upcoming collection notifications`,
        success: result.success,
        count: result.count,
        errors: result.errors
      });
    } catch (error) {
      errorLogger.logError('Error sending upcoming collection notifications', 'notification-route', error as Error);
      return res.status(500).json({ message: 'Failed to send notifications' });
    }
  });

  // Send overdue collection notifications
  app.post('/api/notifications/overdue-collections', authMiddleware, requireRole(['company_admin', 'saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const result = await notificationService.sendOverdueCollectionNotifications();

      return res.json({
        message: `Sent ${result.count} overdue collection notifications`,
        success: result.success,
        count: result.count,
        errors: result.errors
      });
    } catch (error) {
      errorLogger.logError('Error sending overdue collection notifications', 'notification-route', error as Error);
      return res.status(500).json({ message: 'Failed to send notifications' });
    }
  });

  // Send payment confirmation notification
  app.post('/api/notifications/payment-confirmation/:collectionId', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const collectionId = parseInt(req.params.collectionId);

      if (isNaN(collectionId)) {
        return res.status(400).json({ message: 'Valid collection ID is required' });
      }

      const success = await notificationService.sendPaymentConfirmation(collectionId);

      if (success) {
        return res.json({
          message: 'Payment confirmation notification sent successfully'
        });
      } else {
        return res.status(400).json({
          message: 'Failed to send payment confirmation notification'
        });
      }
    } catch (error) {
      errorLogger.logError('Error sending payment confirmation', 'notification-route', error as Error);
      return res.status(500).json({ message: 'Failed to send payment confirmation' });
    }
  });

  // Update all customer reference codes to company-specific format
  app.post('/api/customers/update-reference-codes', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // Ensure user is authenticated
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      // Get company ID from request or user context
      const companyId = req.body.company_id || req.user.company_id;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      // Ensure user has access to this company
      if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc =>
          uc.company_id === companyId ||
          (uc.company && uc.company.id === companyId)
        );

        if (!hasAccess) {
          console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Get all customers for the company
      const companyCustomers = await storage.getCustomersByCompany(companyId);
      console.log(`Found ${companyCustomers.length} customers for company ${companyId}`);

      if (companyCustomers.length === 0) {
        return res.status(200).json({
          message: 'No customers found for this company',
          updated: 0
        });
      }

      // Get company prefix for customer reference codes
      const companyPrefix = await getCompanyName(companyId);
      console.log(`Generated company prefix for customer reference codes: ${companyPrefix}`);

      // Update each customer with a company-specific reference code
      let updatedCount = 0;
      const errors = [];
      let highestSerial = await getHighestCustomerSerial(companyId, companyPrefix);

      for (const customer of companyCustomers) {
        try {
          // Only update customers that don't already have a reference code or have an empty reference code
          if (!customer.customer_reference_code || customer.customer_reference_code.trim() === '') {
            // Generate the next sequential reference code
            highestSerial++;
            const serialString = highestSerial.toString().padStart(3, '0');
            const customerReferenceCode = `${companyPrefix}-${serialString}`;

            console.log(`Generating reference code ${customerReferenceCode} for customer ID ${customer.id}`);

            await storage.updateCustomer(
              customer.id,
              companyId,
              { customer_reference_code: customerReferenceCode }
            );

            updatedCount++;
          }
        } catch (error) {
          console.error(`Error updating customer ID ${customer.id}:`, error);
          errors.push({
            customerId: customer.id,
            error: error instanceof Error ? error.message : String(error)
          });
        }
      }

      return res.status(200).json({
        message: `Updated ${updatedCount} customers with company-specific reference codes`,
        totalCustomers: companyCustomers.length,
        updatedCustomers: updatedCount,
        companyPrefix: companyPrefix,
        errors: errors.length > 0 ? errors : undefined
      });
    } catch (error) {
      console.error('Error updating customer reference codes:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  });

  // Add basic permissions endpoint for user management
  app.get('/api/permissions', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      // For now, return a basic set of permissions
      // This can be expanded later with proper permission management
      const basicPermissions = [
        { id: 1, code: 'user_view', name: 'View Users', description: 'Can view user information' },
        { id: 2, code: 'user_create', name: 'Create Users', description: 'Can create new users' },
        { id: 3, code: 'user_edit', name: 'Edit Users', description: 'Can edit user information' },
        { id: 4, code: 'user_delete', name: 'Delete Users', description: 'Can delete users' },
        { id: 5, code: 'role_view', name: 'View Roles', description: 'Can view roles' },
        { id: 6, code: 'role_create', name: 'Create Roles', description: 'Can create new roles' },
        { id: 7, code: 'role_edit', name: 'Edit Roles', description: 'Can edit roles' },
        { id: 8, code: 'role_delete', name: 'Delete Roles', description: 'Can delete roles' }
      ];
      return res.json(basicPermissions);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Add basic roles endpoint for user management
  app.get('/api/roles', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : null;

      // For now, return basic system roles
      // This can be expanded later with proper role management
      const basicRoles = [
        { id: 1, name: 'Company Admin', description: 'Full access to company features', company_id: companyId },
        { id: 2, name: 'Employee', description: 'Standard employee access', company_id: companyId },
        { id: 3, name: 'Agent', description: 'Agent access for collections', company_id: companyId },
        { id: 4, name: 'Customer', description: 'Customer portal access', company_id: companyId }
      ];

      return res.json(basicRoles);
    } catch (error) {
      console.error('Error fetching roles:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // ==================== FIELD SECURITY ROUTES ====================

  // Import field security service
  const { fieldSecurityService } = await import('./services/fieldSecurityService');
  const { sensitiveFieldDefinitions, fieldSecurityRules, customRoles } = await import('../shared/schema');

  // GET /api/field-security/sensitive-fields - Get sensitive field definitions
  app.get('/api/field-security/sensitive-fields', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const tableName = req.query.table_name as string;

      let query = db.select().from(sensitiveFieldDefinitions);

      if (tableName) {
        query = query.where(eq(sensitiveFieldDefinitions.table_name, tableName));
      }

      const fields = await query;
      return res.json(fields);
    } catch (error) {
      errorLogger.logError('Error fetching sensitive fields', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to fetch sensitive fields' });
    }
  });

  // GET /api/field-security/rules - Get field security rules
  app.get('/api/field-security/rules', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const roleId = req.query.role_id ? parseInt(req.query.role_id as string) : null;

      let query = db
        .select({
          id: fieldSecurityRules.id,
          role_id: fieldSecurityRules.role_id,
          sensitive_field_id: fieldSecurityRules.sensitive_field_id,
          access_type: fieldSecurityRules.access_type,
          condition_config: fieldSecurityRules.condition_config,
          override_masking_pattern: fieldSecurityRules.override_masking_pattern,
          priority: fieldSecurityRules.priority,
          description: fieldSecurityRules.description,
          field_name: sensitiveFieldDefinitions.field_name,
          table_name: sensitiveFieldDefinitions.table_name,
          sensitivity_level: sensitiveFieldDefinitions.sensitivity_level,
          role_name: customRoles.name
        })
        .from(fieldSecurityRules)
        .innerJoin(sensitiveFieldDefinitions, eq(fieldSecurityRules.sensitive_field_id, sensitiveFieldDefinitions.id))
        .innerJoin(customRoles, eq(fieldSecurityRules.role_id, customRoles.id))
        .where(
          and(
            eq(fieldSecurityRules.is_active, true),
            or(
              eq(customRoles.company_id, companyId),
              isNull(customRoles.company_id) // System roles
            )
          )
        );

      if (roleId) {
        query = query.where(eq(fieldSecurityRules.role_id, roleId));
      }

      const rules = await query;
      return res.json(rules);
    } catch (error) {
      errorLogger.logError('Error fetching field security rules', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to fetch field security rules' });
    }
  });

  // POST /api/field-security/rules - Create field security rule
  app.post('/api/field-security/rules', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const { role_id, sensitive_field_id, access_type, condition_config, override_masking_pattern, priority, description } = req.body;

      // Validate required fields
      if (!role_id || !sensitive_field_id || !access_type) {
        return res.status(400).json({ message: 'Role ID, sensitive field ID, and access type are required' });
      }

      // Check if rule already exists
      const existingRule = await db
        .select()
        .from(fieldSecurityRules)
        .where(
          and(
            eq(fieldSecurityRules.role_id, role_id),
            eq(fieldSecurityRules.sensitive_field_id, sensitive_field_id)
          )
        )
        .limit(1);

      if (existingRule.length > 0) {
        return res.status(409).json({ message: 'Field security rule already exists for this role and field' });
      }

      const newRule = await db
        .insert(fieldSecurityRules)
        .values({
          role_id,
          sensitive_field_id,
          access_type,
          condition_config,
          override_masking_pattern,
          priority: priority || 0,
          description,
          is_active: true
        })
        .returning();

      return res.status(201).json(newRule[0]);
    } catch (error) {
      errorLogger.logError('Error creating field security rule', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to create field security rule' });
    }
  });

  // PUT /api/field-security/rules/:id - Update field security rule
  app.put('/api/field-security/rules/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const ruleId = parseInt(req.params.id);
      const { access_type, condition_config, override_masking_pattern, priority, description, is_active } = req.body;

      const updatedRule = await db
        .update(fieldSecurityRules)
        .set({
          access_type,
          condition_config,
          override_masking_pattern,
          priority,
          description,
          is_active,
          updated_at: new Date()
        })
        .where(eq(fieldSecurityRules.id, ruleId))
        .returning();

      if (updatedRule.length === 0) {
        return res.status(404).json({ message: 'Field security rule not found' });
      }

      return res.json(updatedRule[0]);
    } catch (error) {
      errorLogger.logError('Error updating field security rule', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to update field security rule' });
    }
  });

  // DELETE /api/field-security/rules/:id - Delete field security rule
  app.delete('/api/field-security/rules/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const ruleId = parseInt(req.params.id);

      const deletedRule = await db
        .delete(fieldSecurityRules)
        .where(eq(fieldSecurityRules.id, ruleId))
        .returning();

      if (deletedRule.length === 0) {
        return res.status(404).json({ message: 'Field security rule not found' });
      }

      return res.json({ message: 'Field security rule deleted successfully' });
    } catch (error) {
      errorLogger.logError('Error deleting field security rule', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to delete field security rule' });
    }
  });

  // POST /api/field-security/check-access - Check field access for user
  app.post('/api/field-security/check-access', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const { table_name, field_name, operation = 'read' } = req.body;

      if (!table_name || !field_name) {
        return res.status(400).json({ message: 'Table name and field name are required' });
      }

      const context = {
        userId,
        companyId,
        tableName: table_name,
        operation: operation as 'read' | 'write' | 'create' | 'update',
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      };

      const fieldAccess = await fieldSecurityService.checkFieldAccess(context, field_name);
      return res.json(fieldAccess);
    } catch (error) {
      errorLogger.logError('Error checking field access', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to check field access' });
    }
  });

  // POST /api/field-security/filter-data - Filter data with field-level security
  app.post('/api/field-security/filter-data', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const { table_name, data, operation = 'read' } = req.body;

      if (!table_name || !data) {
        return res.status(400).json({ message: 'Table name and data are required' });
      }

      const context = {
        userId,
        companyId,
        tableName: table_name,
        operation: operation as 'read' | 'write' | 'create' | 'update',
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      };

      const result = await fieldSecurityService.filterFieldsByPermissions(context, data);
      return res.json(result);
    } catch (error) {
      errorLogger.logError('Error filtering data', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to filter data' });
    }
  });

  // GET /api/field-security/accessible-fields - Get accessible fields for user
  app.get('/api/field-security/accessible-fields', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const tableName = req.query.table_name as string;

      if (!tableName) {
        return res.status(400).json({ message: 'Table name is required' });
      }

      const context = {
        userId,
        companyId,
        tableName,
        operation: 'read' as const,
        timestamp: new Date(),
        ipAddress: req.ip,
        userAgent: req.get('User-Agent')
      };

      const accessibleFields = await fieldSecurityService.getAccessibleFields(context);
      return res.json(accessibleFields);
    } catch (error) {
      errorLogger.logError('Error getting accessible fields', error, 'field-security-api');
      return res.status(500).json({ message: 'Failed to get accessible fields' });
    }
  });

  const httpServer = createServer(app);
  return httpServer;
}
