import { Request, Response, NextFunction } from 'express';
import { EnhancedPermissionService, PermissionContext } from '../services/enhancedPermissionService';
import { AuthRequest } from './auth';

const permissionService = new EnhancedPermissionService();

/**
 * Middleware to check loan creation permissions based on amount
 * @param amountField Field name in request body containing the loan amount
 * @returns Middleware function
 */
export function requireLoanCreationPermission(amountField: string = 'amount') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const amount = req.body[amountField] || 0;
      const hasPermission = await permissionService.checkLoanCreationPermission(req.user.id, amount);

      if (!hasPermission) {
        return res.status(403).json({
          message: 'Insufficient permissions for loan creation',
          required_amount_limit: amount,
          action: 'create_loan',
          user_permissions: await permissionService.getUserPermissions(req.user.id)
        });
      }

      next();
    } catch (error) {
      console.error('Loan creation permission middleware error:', error);
      return res.status(500).json({ message: 'Permission check failed' });
    }
  };
}

/**
 * Middleware to check loan approval permissions based on amount
 * @param amountField Field name in request body containing the loan amount
 * @returns Middleware function
 */
export function requireLoanApprovalPermission(amountField: string = 'amount') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const amount = req.body[amountField] || req.params.amount || 0;
      const hasPermission = await permissionService.checkLoanApprovalPermission(req.user.id, amount);

      if (!hasPermission) {
        return res.status(403).json({
          message: 'Insufficient permissions for loan approval',
          required_amount_limit: amount,
          action: 'approve_loan'
        });
      }

      next();
    } catch (error) {
      console.error('Loan approval permission middleware error:', error);
      return res.status(500).json({ message: 'Permission check failed' });
    }
  };
}

/**
 * Middleware to check loan disbursement permissions based on amount
 * @param amountField Field name in request body containing the loan amount
 * @returns Middleware function
 */
export function requireLoanDisbursementPermission(amountField: string = 'amount') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const amount = req.body[amountField] || 0;
      const hasPermission = await permissionService.checkLoanDisbursementPermission(req.user.id, amount);

      if (!hasPermission) {
        return res.status(403).json({
          message: 'Insufficient permissions for loan disbursement',
          required_amount_limit: amount,
          action: 'disburse_loan'
        });
      }

      next();
    } catch (error) {
      console.error('Loan disbursement permission middleware error:', error);
      return res.status(500).json({ message: 'Permission check failed' });
    }
  };
}

/**
 * Middleware to check customer data access permissions
 * @param dataType Type of customer data ('basic', 'financial', 'sensitive', 'all')
 * @returns Middleware function
 */
export function requireCustomerDataAccess(dataType: 'basic' | 'financial' | 'sensitive' | 'all') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const hasAccess = await permissionService.checkCustomerDataAccess(req.user.id, dataType);

      if (!hasAccess) {
        return res.status(403).json({
          message: `Access denied to ${dataType} customer data`,
          required_permission: `customer_view_${dataType}`,
          data_type: dataType
        });
      }

      next();
    } catch (error) {
      console.error('Customer data access middleware error:', error);
      return res.status(500).json({ message: 'Access check failed' });
    }
  };
}

/**
 * Middleware to check customer export permissions
 * @param exportType Type of export ('basic', 'financial', 'sensitive', 'bulk')
 * @returns Middleware function
 */
export function requireCustomerExportPermission(exportType: 'basic' | 'financial' | 'sensitive' | 'bulk') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const hasPermission = await permissionService.checkCustomerExportPermission(req.user.id, exportType);

      if (!hasPermission) {
        return res.status(403).json({
          message: `Access denied to export ${exportType} customer data`,
          required_permission: `customer_export_${exportType}`,
          export_type: exportType
        });
      }

      next();
    } catch (error) {
      console.error('Customer export permission middleware error:', error);
      return res.status(500).json({ message: 'Export permission check failed' });
    }
  };
}

/**
 * Middleware to check customer communication permissions
 * @param communicationType Type of communication ('email', 'sms', 'call', 'automated')
 * @returns Middleware function
 */
export function requireCustomerCommunicationPermission(communicationType: 'email' | 'sms' | 'call' | 'automated') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const hasPermission = await permissionService.checkCustomerCommunicationPermission(req.user.id, communicationType);

      if (!hasPermission) {
        return res.status(403).json({
          message: `Access denied to ${communicationType} customer communication`,
          required_permission: `customer_contact_${communicationType}`,
          communication_type: communicationType
        });
      }

      next();
    } catch (error) {
      console.error('Customer communication permission middleware error:', error);
      return res.status(500).json({ message: 'Communication permission check failed' });
    }
  };
}

/**
 * Middleware to check payment operation permissions
 * @param operationType Type of operation ('manual', 'automated', 'refund', 'void', 'adjust')
 * @returns Middleware function
 */
export function requirePaymentOperationPermission(operationType: 'manual' | 'automated' | 'refund' | 'void' | 'adjust') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const hasPermission = await permissionService.checkPaymentOperationPermission(req.user.id, operationType);

      if (!hasPermission) {
        return res.status(403).json({
          message: `Access denied to ${operationType} payment operations`,
          required_permission: `payment_process_${operationType}`,
          operation_type: operationType
        });
      }

      next();
    } catch (error) {
      console.error('Payment operation permission middleware error:', error);
      return res.status(500).json({ message: 'Payment permission check failed' });
    }
  };
}

/**
 * Middleware to check report access permissions
 * @param reportType Type of report ('basic', 'detailed', 'executive', 'export', 'custom', 'schedule')
 * @returns Middleware function
 */
export function requireReportPermission(reportType: 'basic' | 'detailed' | 'executive' | 'export' | 'custom' | 'schedule') {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const hasPermission = await permissionService.checkReportPermission(req.user.id, reportType);

      if (!hasPermission) {
        return res.status(403).json({
          message: `Access denied to ${reportType} reports`,
          required_permission: `report_view_${reportType}`,
          report_type: reportType
        });
      }

      next();
    } catch (error) {
      console.error('Report permission middleware error:', error);
      return res.status(500).json({ message: 'Report permission check failed' });
    }
  };
}

/**
 * Advanced middleware to check permissions with full context
 * @param permissionType Type of permission to check
 * @param operationType Specific operation type
 * @param options Additional options for permission checking
 * @returns Middleware function
 */
export function requirePermissionWithContext(
  permissionType: 'loan_create' | 'loan_approve' | 'loan_disburse' | 'customer_data' | 'customer_export' | 'customer_communication' | 'payment_operation' | 'report',
  operationType?: string,
  options: {
    amountField?: string;
    requireCompanyAccess?: boolean;
    logAccess?: boolean;
  } = {}
) {
  return async (req: AuthRequest, res: Response, next: NextFunction) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const context: PermissionContext = {
        userId: req.user.id,
        amount: options.amountField ? req.body[options.amountField] : undefined,
        timestamp: new Date(),
        ip_address: req.ip,
        company_id: req.user.company_id
      };

      const hasPermission = await permissionService.checkPermissionWithContext(
        context,
        permissionType,
        operationType
      );

      if (!hasPermission) {
        // Log access attempt if requested
        if (options.logAccess) {
          console.warn('Access denied:', {
            userId: req.user.id,
            permissionType,
            operationType,
            ip: req.ip,
            timestamp: new Date().toISOString()
          });
        }

        // Log permission denial to audit system
        try {
          const { auditService } = await import('../services/auditService');
          await auditService.logPermissionUsage({
            userId: req.user.id,
            companyId: req.user.company_id,
            sessionId: req.session?.sessionId,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            method: req.method,
            permissionCode: permissionType,
            permissionName: `${permissionType} ${operationType || ''}`.trim(),
            resourceType: options.amountField ? 'financial' : 'general',
            resourceId: req.params.id,
            operationType: operationType || req.method.toLowerCase(),
            result: 'denied',
            isSensitiveOperation: ['loan_approve', 'loan_disburse', 'customer_export'].includes(permissionType),
            metadata: {
              amount: context.amount,
              company_id: context.company_id,
              statusCode: 403,
            },
          });
        } catch (auditError) {
          console.error('Failed to log permission denial:', auditError);
        }

        return res.status(403).json({
          message: `Access denied for ${permissionType} operation`,
          permission_type: permissionType,
          operation_type: operationType,
          context: {
            amount: context.amount,
            company_id: context.company_id
          }
        });
      }

      // Log successful access if requested
      if (options.logAccess) {
        console.info('Access granted:', {
          userId: req.user.id,
          permissionType,
          operationType,
          ip: req.ip,
          timestamp: new Date().toISOString()
        });

        // Log successful permission usage to audit system
        try {
          const { auditService } = await import('../services/auditService');
          await auditService.logPermissionUsage({
            userId: req.user.id,
            companyId: req.user.company_id,
            sessionId: req.session?.sessionId,
            ipAddress: req.ip,
            userAgent: req.get('User-Agent'),
            endpoint: req.path,
            method: req.method,
            permissionCode: permissionType,
            permissionName: `${permissionType} ${operationType || ''}`.trim(),
            resourceType: options.amountField ? 'financial' : 'general',
            resourceId: req.params.id,
            operationType: operationType || req.method.toLowerCase(),
            result: 'success',
            isSensitiveOperation: ['loan_approve', 'loan_disburse', 'customer_export'].includes(permissionType),
            metadata: {
              amount: context.amount,
              company_id: context.company_id,
            },
          });
        } catch (auditError) {
          console.error('Failed to log permission success:', auditError);
        }
      }

      next();
    } catch (error) {
      console.error('Permission context middleware error:', error);
      return res.status(500).json({ message: 'Permission check failed' });
    }
  };
}
