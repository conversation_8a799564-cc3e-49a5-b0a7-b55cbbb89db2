import { useAuth } from './auth';
import { useCompany } from './companies';
import { useState, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { apiRequest } from './queryClient';
import { useQueryClient } from '@tanstack/react-query';
import logger from './logger';
import { DEFAULT_COMPANY_ID, getSafeCompanyId, isValidCompanyId } from '@/config/companyConfig';

export interface Branch {
  id: number;
  name: string;
  company_id: number;
  address?: string;
  phone?: string;
  email?: string;
  is_active: boolean;
}

/**
 * Custom hook that centralizes access to company, branch, and user data
 * throughout the application. This hook aggregates all necessary context data
 * and provides a consistent interface for components.
 */
export const useContextData = () => {
  const auth = useAuth();
  const company = useCompany();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  const [lastCompanyId, setLastCompanyId] = useState<number | undefined>(undefined);

  // Get the current user from auth context
  const user = auth.user;

  // Get the current company from company context, with fallback to user.company_id
  const currentCompany = company.currentCompany || null;

  // Derive companyId using a consistent approach, with proper logging of the source
  // IMPORTANT: Always prioritize the JWT token's company ID (user?.company_id) over company context
  // This ensures we use the actual logged-in company, not a cached/selected company
  let derivedCompanyId = user?.company_id || currentCompany?.company_id;

  // Enhanced debugging for company ID issues
  console.log(`[useContextData] Company ID derivation:`, {
    currentCompanyId: currentCompany?.company_id,
    userCompanyId: user?.company_id,
    derivedCompanyId,
    currentCompanyObject: currentCompany,
    userObject: user
  });

  // Ensure we're using a valid company ID
  const validatedCompanyId = getSafeCompanyId(derivedCompanyId);

  const companyId = validatedCompanyId;
  const companyName = currentCompany?.company?.name || user?.company_name || "Your Company"; // Default name

  // Invalidate cache when company changes
  useEffect(() => {
    if (companyId && lastCompanyId && companyId !== lastCompanyId) {
      console.log(`[useContextData] Company changed from ${lastCompanyId} to ${companyId}, invalidating cache`);

      // Invalidate all company-related queries
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      queryClient.invalidateQueries({ queryKey: ['companies'] });

      // Clear specific company data to force refetch
      queryClient.removeQueries({ queryKey: ['/api/companies', lastCompanyId] });
    }

    if (companyId) {
      setLastCompanyId(companyId);
    }
  }, [companyId, lastCompanyId, queryClient]);

  // Log context data for debugging
  useEffect(() => {
    // Get the component stack trace to identify which component is using this hook
    const stackTrace = new Error().stack || '';
    const callerInfo = stackTrace.split('\n').slice(2, 4).join(' -> ');

    // Generate a unique instance ID for this component
    const instanceId = Math.random().toString(36).substring(2, 9);

    logger.debug('Context data initialized', {
      context: 'context-data',
      instanceId,
      data: {
        userId: user?.id,
        username: user?.username,
        userCompanyId: user?.company_id,
        contextCompanyId: companyId,
        companyName: companyName,
        companySource: currentCompany ? 'company-context' : (user?.company_id ? 'user-auth' : 'default-19'),
        hasCompanyContext: !!company.currentCompany,
        companyDataAvailable: !!companyId,
        callerComponent: callerInfo // This will help identify which component is calling useContextData
      }
    });

    // Log a warning if no company ID is available
    if (!companyId) {
      console.warn(`[ContextData ${instanceId}] No company ID available. User needs to select a company.`, {
        caller: callerInfo
      });
    }

    // Log the company ID being used
    if (companyId) {
      console.log(`[ContextData ${instanceId}] Using company ID ${companyId}`, {
        source: currentCompany ? 'company-context' : (user?.company_id ? 'user-auth' : 'unknown'),
        caller: callerInfo,
        userCompanyId: user?.company_id,
        currentCompanyId: currentCompany?.company_id
      });
    }

    return () => {
      // Log when this context instance is destroyed
      logger.debug(`[ContextData ${instanceId}] Context instance destroyed`, {
        companyId,
        caller: callerInfo
      });
    };
  }, [user, companyId, companyName, currentCompany]);

  // Fetch branches when companyId changes
  useEffect(() => {
    let isMounted = true;

    const fetchBranches = async () => {
      if (!companyId) {
        setBranches([]);
        setSelectedBranch(null);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);

        const url = `/api/companies/${companyId}/branches`;
        logger.info('Fetching branches', {
          context: 'branches-api',
          data: { url, companyId }
        });

        const branchesData = await apiRequest('GET', url);

        if (isMounted) {
          const branchArray = await branchesData.json();
        setBranches(branchArray);

          // If no branch is selected yet, select the first one
          if (!selectedBranch && branchArray.length > 0) {
            setSelectedBranch(branchArray[0]);
            logger.debug('Auto-selected first branch', {
              context: 'branch-selection',
              data: {
                branchId: branchArray[0].id,
                branchName: branchArray[0].name
              }
            });
          }
        }
      } catch (err) {
        if (isMounted) {
          setError(err instanceof Error ? err : new Error('Failed to fetch branches'));
          logger.error('Error fetching branches', err instanceof Error ? err : new Error('Failed to fetch branches'), {
            context: 'branches-api',
            data: { companyId }
          });
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    fetchBranches();

    return () => {
      isMounted = false;
    };
  }, [companyId]);

  // Switch branch with validation
  const switchBranch = (branchId: number) => {
    const branch = branches.find(b => b.id === branchId);
    if (branch) {
      setSelectedBranch(branch);
      logger.info('Switched branch', {
        context: 'branch-selection',
        data: {
          branchId: branch.id,
          branchName: branch.name,
          companyId: companyId
        }
      });
    } else {
      toast({
        title: 'Error',
        description: `Branch with ID ${branchId} not found.`,
        variant: 'destructive',
      });
    }
  };

  return {
    // User data
    user,
    userId: user?.id,
    userRole: user?.role,

    // Company data
    companyId,
    companyName,
    currentCompany,
    userCompanies: company.userCompanies,
    switchCompany: company.switchCompany,

    // Branch data
    branches,
    selectedBranch,
    branchId: selectedBranch?.id,
    branchName: selectedBranch?.name,
    switchBranch,

    // Status flags
    isLoading: isLoading || company.isLoading || auth.loading,
    error: error || company.error || auth.error,
    isAuthenticated: !!user,
    hasCompanyContext: !!companyId,
    hasBranchContext: !!selectedBranch
  };
};