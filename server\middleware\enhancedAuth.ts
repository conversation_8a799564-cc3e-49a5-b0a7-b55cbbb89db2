import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils/jwt';
import { storage } from '../storage';
import { enhancedSessionService } from '../services/enhancedSessionService';
import crypto from 'crypto';

// Enhanced auth request interface with session data
export interface EnhancedAuthRequest extends Request {
  user?: {
    id: number;
    role: string;
    company_id?: number;
    permissions?: string[];
  };
  session?: {
    sessionId: string;
    deviceFingerprint?: string;
    deviceType: string;
    isTrustedDevice: boolean;
    mfaVerified: boolean;
    freshAuth: boolean;
    lastActivity: Date;
    expiresAt?: Date;
  };
  deviceInfo?: {
    fingerprint: string;
    type: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
    userAgent: string;
    ipAddress: string;
    location?: {
      country?: string;
      region?: string;
      city?: string;
    };
  };
  cookies?: {
    [key: string]: string;
  };
}

/**
 * Enhanced authentication middleware with session management
 */
export async function enhancedAuthMiddleware(req: EnhancedAuthRequest, res: Response, next: NextFunction) {
  try {
    // Extract device information
    const deviceInfo = extractDeviceInfo(req);
    req.deviceInfo = deviceInfo;

    // Check authorization header first (Bearer token)
    const authHeader = req.headers.authorization;
    let token = null;
    let decoded = null;

    // First try with authorization header
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.split(' ')[1];
      try {
        decoded = await verifyToken(token);
      } catch (tokenError) {
        console.error('Invalid token in Authorization header:', tokenError);
      }
    }

    // If no valid token from authorization header, try cookie
    if (!decoded && req.cookies && req.cookies.auth_token) {
      try {
        token = req.cookies.auth_token;
        decoded = await verifyToken(token);
      } catch (cookieTokenError) {
        console.error('Invalid token in cookie:', cookieTokenError);
      }
    }

    // If no valid token found, return unauthorized
    if (!decoded || typeof decoded !== 'object' || !decoded.userId) {
      console.error('No valid token found in request:', {
        hasAuthHeader: !!authHeader,
        hasCookies: !!req.headers.cookie
      });
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get user information
    const user = await storage.getUser(decoded.userId);
    if (!user) {
      return res.status(401).json({ message: 'User not found' });
    }

    // Check if we have a session ID in the token or create one
    let sessionId = decoded.sessionId;
    if (!sessionId) {
      // For backward compatibility, create a session for existing JWT tokens
      const sessionResult = await enhancedSessionService.createSession({
        userId: user.id,
        companyId: user.company_id,
        deviceFingerprint: deviceInfo.fingerprint,
        deviceType: deviceInfo.type,
        userAgent: deviceInfo.userAgent,
        ipAddress: deviceInfo.ipAddress,
        location: deviceInfo.location,
        mfaVerified: false, // Assume not verified for legacy tokens
        freshAuth: false
      });
      sessionId = sessionResult.session.session_id;
    }

    // Validate the session
    const sessionValidation = await enhancedSessionService.validateSession(sessionId, deviceInfo.ipAddress);
    
    if (!sessionValidation.isValid) {
      // Handle different session validation failures
      if (sessionValidation.requiresAction === 'mfa') {
        return res.status(403).json({ 
          message: 'Multi-factor authentication required',
          requiresAction: 'mfa',
          sessionId: sessionId
        });
      } else if (sessionValidation.requiresAction === 'fresh_auth') {
        return res.status(403).json({ 
          message: 'Fresh authentication required',
          requiresAction: 'fresh_auth'
        });
      } else {
        // Session expired or invalid - require logout
        return res.status(401).json({ 
          message: sessionValidation.reason || 'Session invalid',
          requiresAction: 'logout'
        });
      }
    }

    // Set user and session information in request
    req.user = {
      id: user.id,
      role: user.role,
      company_id: user.company_id
    };

    const sessionData = sessionValidation.session!;
    req.session = {
      sessionId: sessionData.session_id,
      deviceFingerprint: sessionData.device_fingerprint,
      deviceType: sessionData.device_type,
      isTrustedDevice: sessionData.is_trusted_device,
      mfaVerified: sessionData.mfa_verified,
      freshAuth: sessionData.fresh_auth,
      lastActivity: sessionData.last_activity,
      expiresAt: sessionData.expires_at
    };

    next();
  } catch (error) {
    console.error('Enhanced auth middleware error:', error);
    return res.status(401).json({ message: 'Authentication failed' });
  }
}

/**
 * Middleware to require fresh authentication for sensitive operations
 */
export function requireFreshAuth(maxAge: number = 300) { // 5 minutes default
  return (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    if (!req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    const sessionAge = (Date.now() - req.session.lastActivity.getTime()) / 1000;
    if (sessionAge > maxAge || !req.session.freshAuth) {
      return res.status(403).json({ 
        message: 'Fresh authentication required for this operation',
        requiresAction: 'fresh_auth',
        maxAge: maxAge
      });
    }

    next();
  };
}

/**
 * Middleware to require MFA verification
 */
export function requireMFA() {
  return (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    if (!req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!req.session.mfaVerified) {
      return res.status(403).json({ 
        message: 'Multi-factor authentication required',
        requiresAction: 'mfa',
        sessionId: req.session.sessionId
      });
    }

    next();
  };
}

/**
 * Middleware to require trusted device
 */
export function requireTrustedDevice() {
  return (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    if (!req.session) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    if (!req.session.isTrustedDevice) {
      return res.status(403).json({ 
        message: 'Trusted device required for this operation',
        requiresAction: 'device_registration',
        deviceFingerprint: req.deviceInfo?.fingerprint
      });
    }

    next();
  };
}

/**
 * Middleware to restrict access by device type
 */
export function requireDeviceType(allowedTypes: string[]) {
  return (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    if (!req.deviceInfo) {
      return res.status(401).json({ message: 'Device information required' });
    }

    if (!allowedTypes.includes(req.deviceInfo.type)) {
      return res.status(403).json({ 
        message: `This operation is not allowed on ${req.deviceInfo.type} devices`,
        allowedDeviceTypes: allowedTypes,
        currentDeviceType: req.deviceInfo.type
      });
    }

    next();
  };
}

/**
 * Extract device information from request
 */
function extractDeviceInfo(req: Request): {
  fingerprint: string;
  type: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown';
  userAgent: string;
  ipAddress: string;
  location?: {
    country?: string;
    region?: string;
    city?: string;
  };
} {
  const userAgent = req.get('User-Agent') || '';
  const ipAddress = req.ip || req.connection.remoteAddress || '';
  
  // Determine device type from user agent
  let deviceType: 'desktop' | 'laptop' | 'mobile' | 'tablet' | 'unknown' = 'unknown';
  
  if (/Mobile|Android|iPhone/.test(userAgent)) {
    deviceType = 'mobile';
  } else if (/iPad|Tablet/.test(userAgent)) {
    deviceType = 'tablet';
  } else if (/Laptop/.test(userAgent)) {
    deviceType = 'laptop';
  } else if (/Windows|Mac|Linux/.test(userAgent)) {
    deviceType = 'desktop';
  }

  // Create device fingerprint
  const fingerprintData = {
    userAgent,
    acceptLanguage: req.get('Accept-Language') || '',
    acceptEncoding: req.get('Accept-Encoding') || '',
    connection: req.get('Connection') || '',
    // Add more headers for better fingerprinting
    dnt: req.get('DNT') || '',
    upgradeInsecureRequests: req.get('Upgrade-Insecure-Requests') || ''
  };
  
  const fingerprint = crypto
    .createHash('sha256')
    .update(JSON.stringify(fingerprintData))
    .digest('hex');

  // Extract location information from headers (if available)
  const location = {
    country: req.get('X-Country-Code') || req.get('CF-IPCountry'),
    region: req.get('X-Region-Code'),
    city: req.get('X-City-Name')
  };

  return {
    fingerprint,
    type: deviceType,
    userAgent,
    ipAddress,
    location: location.country ? location : undefined
  };
}

/**
 * Session activity logging middleware
 */
export function sessionActivityLogger() {
  return async (req: EnhancedAuthRequest, res: Response, next: NextFunction) => {
    const startTime = Date.now();
    
    // Store original end function
    const originalEnd = res.end;
    
    // Override end function to log activity
    res.end = function(chunk?: any, encoding?: any) {
      const responseTime = Date.now() - startTime;
      
      // Log session activity if we have session info
      if (req.session && req.user) {
        enhancedSessionService['logSessionActivity']({
          sessionId: req.session.sessionId,
          userId: req.user.id,
          companyId: req.user.company_id,
          activityType: 'activity',
          activityDescription: `${req.method} ${req.path}`,
          endpoint: req.path,
          method: req.method,
          statusCode: res.statusCode,
          ipAddress: req.deviceInfo?.ipAddress,
          userAgent: req.deviceInfo?.userAgent,
          referer: req.get('Referer'),
          responseTimeMs: responseTime,
          metadata: {
            query: req.query,
            deviceType: req.deviceInfo?.type,
            isTrustedDevice: req.session?.isTrustedDevice
          }
        }).catch(error => {
          console.error('Failed to log session activity:', error);
        });
      }
      
      // Call original end function
      originalEnd.call(this, chunk, encoding);
    };
    
    next();
  };
}
