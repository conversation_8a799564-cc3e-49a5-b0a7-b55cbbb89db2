import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';
import { insertAccountSchema } from '@shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerAccountRoutes(app: Express): void {
  // Get all accounts for a company
  app.get('/api/companies/:companyId/accounts', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      const accounts = await storage.getAccountsByCompany(companyId);
      return res.json(accounts);
    } catch (error) {
      console.error('Error fetching accounts:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get account by ID
  app.get('/api/accounts/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const accountId = parseInt(req.params.id);
      
      const account = await storage.getAccount(accountId);
      
      if (!account) {
        return res.status(404).json({ message: 'Account not found' });
      }
      
      // Check if user has access to this account's company
      if (req.user!.role !== 'saas_admin' && account.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === account.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this account' });
        }
      }
      
      return res.json(account);
    } catch (error) {
      console.error(`Error fetching account ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get account balance
  app.get('/api/accounts/:id/balance', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const accountId = parseInt(req.params.id);
      
      const account = await storage.getAccount(accountId);
      
      if (!account) {
        return res.status(404).json({ message: 'Account not found' });
      }
      
      // Check if user has access to this account's company
      if (req.user!.role !== 'saas_admin' && account.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === account.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this account' });
        }
      }
      
      const balance = await storage.getAccountBalance(accountId, account.company_id);
      return res.json({ balance });
    } catch (error) {
      console.error(`Error fetching balance for account ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create account
  app.post('/api/accounts', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      
      // Validate input
      const result = insertAccountSchema.safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ 
          message: 'Invalid input', 
          errors: formatZodError(result.error) 
        });
      }
      
      // Check if user has access to the company
      const accountCompanyId = result.data.company_id;
      if (req.user!.role !== 'saas_admin' && accountCompanyId !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === accountCompanyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }
      
      const account = await storage.createAccount(result.data);
      return res.status(201).json(account);
    } catch (error) {
      console.error('Error creating account:', error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Update account
  app.put('/api/accounts/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const accountId = parseInt(req.params.id);
      
      // Get the account to check company access
      const account = await storage.getAccount(accountId);
      
      if (!account) {
        return res.status(404).json({ message: 'Account not found' });
      }
      
      // Check if user has access to this account's company
      if (req.user!.role !== 'saas_admin' && account.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === account.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this account' });
        }
      }
      
      // Validate input
      const result = insertAccountSchema.partial().safeParse(req.body);
      
      if (!result.success) {
        return res.status(400).json({ 
          message: 'Invalid input', 
          errors: formatZodError(result.error) 
        });
      }
      
      // Don't allow changing company_id
      delete result.data.company_id;
      
      // Don't allow direct modification of current_balance
      delete result.data.current_balance;
      
      const updatedAccount = await storage.updateAccount(accountId, account.company_id, result.data);
      return res.json(updatedAccount);
    } catch (error) {
      console.error(`Error updating account ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });

  // Delete account
  app.delete('/api/accounts/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const accountId = parseInt(req.params.id);
      
      // Get the account to check company access
      const account = await storage.getAccount(accountId);
      
      if (!account) {
        return res.status(404).json({ message: 'Account not found' });
      }
      
      // Check if user has access to this account's company
      if (req.user!.role !== 'saas_admin' && account.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === account.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this account' });
        }
      }
      
      const success = await storage.deleteAccount(accountId, account.company_id);
      
      if (!success) {
        return res.status(500).json({ message: 'Failed to delete account' });
      }
      
      return res.json({ message: 'Account deleted successfully' });
    } catch (error) {
      console.error(`Error deleting account ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error', error: (error as Error).message });
    }
  });
}
