// Helper script to login and capture the session cookie
const axios = require('axios');
const fs = require('fs');

// User credentials
const loginData = {
  username: '<EMAIL>',
  password: 'Password@123'
};

async function login() {
  try {
    console.log('Attempting to login...');
    const response = await axios.post('http://localhost:5000/api/login', loginData);
    
    // Extract and save the cookie
    if (response.headers['set-cookie']) {
      const cookies = response.headers['set-cookie'];
      
      // Write the raw cookie to file
      fs.writeFileSync('./cookie.txt', cookies.join('; '), 'utf8');
      
      console.log('Login successful! <PERSON>ie saved to cookie.txt');
      console.log('User data:', response.data);
      
      return response.data;
    } else {
      console.error('No cookie received from server');
    }
  } catch (error) {
    console.error('Lo<PERSON> failed:');
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
    } else {
      console.error(error.message);
    }
  }
}

// Execute login
login();