/**
 * Error logging service - Centralizes error handling across the application
 */

type LogLevel = 'error' | 'warn' | 'info' | 'debug';

interface LogEntry {
  level: LogLevel;
  message: string;
  source: string;
  timestamp: Date;
  details?: any;
  stack?: string;
}

class ErrorLogger {
  private logs: LogEntry[] = [];
  private maxLogs: number = 100;

  /**
   * Log an error with details
   * @param message Error message
   * @param source Component or page where error occurred
   * @param error Original error object
   */
  error(message: string, source: string, error?: any): void {
    const entry: LogEntry = {
      level: 'error',
      message,
      source,
      timestamp: new Date(),
      details: error,
      stack: error?.stack
    };
    
    this.addLog(entry);
    
    // Log to console in development
    console.error(`[${source}] ${message}`, error);
    
    // Here we could also send to a backend API or telemetry service
    this.maybeSendToServer(entry);
  }

  /**
   * Log a warning with details
   * @param message Warning message
   * @param source Component or page where warning occurred
   * @param details Additional details
   */
  warn(message: string, source: string, details?: any): void {
    const entry: LogEntry = {
      level: 'warn',
      message,
      source,
      timestamp: new Date(),
      details
    };
    
    this.addLog(entry);
    console.warn(`[${source}] ${message}`, details);
  }

  /**
   * Log an informational message
   * @param message Info message
   * @param source Component or page where info originated
   * @param details Additional details
   */
  info(message: string, source: string, details?: any): void {
    const entry: LogEntry = {
      level: 'info',
      message,
      source,
      timestamp: new Date(),
      details
    };
    
    this.addLog(entry);
    console.info(`[${source}] ${message}`, details);
  }

  /**
   * Log a debug message (only shown in development)
   * @param message Debug message
   * @param source Component or page where debug originated
   * @param details Additional details
   */
  debug(message: string, source: string, details?: any): void {
    const entry: LogEntry = {
      level: 'debug',
      message,
      source,
      timestamp: new Date(),
      details
    };
    
    this.addLog(entry);
    
    // Only show debug logs in development
    if (process.env.NODE_ENV === 'development') {
      console.debug(`[${source}] ${message}`, details);
    }
  }

  /**
   * Get all logs
   */
  getLogs(): LogEntry[] {
    return [...this.logs];
  }

  /**
   * Get logs filtered by level
   * @param level Log level to filter by
   */
  getLogsByLevel(level: LogLevel): LogEntry[] {
    return this.logs.filter(log => log.level === level);
  }

  /**
   * Clear all logs
   */
  clearLogs(): void {
    this.logs = [];
  }

  /**
   * Add log to the internal logs array
   * @param entry Log entry to add
   */
  private addLog(entry: LogEntry): void {
    this.logs.unshift(entry);
    
    // Keep logs under the maximum limit
    if (this.logs.length > this.maxLogs) {
      this.logs.pop();
    }
  }

  /**
   * Send log to server if available
   * This could be extended to integrate with error tracking services
   * @param entry Log entry to send
   */
  private maybeSendToServer(entry: LogEntry): void {
    // Only send errors to server
    if (entry.level !== 'error') return;
    
    // In the future, we could implement a server-side logging endpoint
    // Example:
    // fetch('/api/logs', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify({
    //     level: entry.level,
    //     message: entry.message,
    //     source: entry.source,
    //     timestamp: entry.timestamp,
    //     details: entry.details,
    //     stack: entry.stack,
    //   })
    // }).catch(err => {
    //   console.error('Failed to send log to server:', err);
    // });
  }
}

// Create a singleton instance for use throughout the app
const errorLogger = new ErrorLogger();
export default errorLogger;