import React, { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useAuth } from '@/lib/auth';
import { useCompany } from '@/lib/companies';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectGroup, SelectItem, SelectLabel, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Loader2, Plus, Search, UserPlus, Users, Shield, Settings, Eye, UserCheck, Workflow } from 'lucide-react';
import { DataTable } from '@/components/ui/data-table';
import { ColumnDef } from '@tanstack/react-table';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { useLocation } from 'wouter';
import BulkOperationsManager from '@/components/user-management/BulkOperationsManager';

// Define types
interface User {
  id: number;
  email: string;
  full_name: string;
  role: string;
  company_id: number;
  active: boolean;
}

interface Role {
  id: number;
  name: string;
  description: string;
  is_system: boolean;
  company_id: number;
  permissions?: Permission[];
}

interface Permission {
  id: number;
  code: string;
  name: string;
  description: string;
  category: string;
}

interface Group {
  id: number;
  name: string;
  description: string;
  company_id: number;
  branch_id: number | null;
  members?: User[];
  roles?: Role[];
}

export default function UserManagement() {
  const { toast } = useToast();
  const { currentCompany } = useCompany();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const queryClient = useQueryClient();
  const [, navigate] = useLocation();
  const [activeTab, setActiveTab] = useState('users');
  const [searchQuery, setSearchQuery] = useState('');
  const [isCreateUserDialogOpen, setIsCreateUserDialogOpen] = useState(false);
  const [isCreateRoleDialogOpen, setIsCreateRoleDialogOpen] = useState(false);
  const [isCreateGroupDialogOpen, setIsCreateGroupDialogOpen] = useState(false);

  // Debug information
  console.log('Current user:', user);
  console.log('Current company:', currentCompany);

  // User form state
  const [userForm, setUserForm] = useState({
    email: '',
    full_name: '',
    password: '',
    confirmPassword: '',
    role: 'employee',
    company_id: currentCompany?.company_id || currentCompany?.id
  });

  // Role form state
  const [roleForm, setRoleForm] = useState({
    name: '',
    description: '',
    company_id: currentCompany?.company_id || currentCompany?.id,
    permissions: [] as number[]
  });

  // Group form state
  const [groupForm, setGroupForm] = useState({
    name: '',
    description: '',
    company_id: currentCompany?.company_id || currentCompany?.id,
    branch_id: null as number | null
  });

  // Fetch users
  const { data: users, isLoading: isLoadingUsers } = useQuery({
    queryKey: ['/api/companies', currentCompany?.company_id || currentCompany?.id, 'users'],
    queryFn: async () => {
      const companyId = currentCompany?.company_id || currentCompany?.id;
      if (!companyId) return [];
      try {
        console.log(`Fetching users for company ID: ${companyId}`);

        // Use the company-specific users endpoint
        const res = await apiRequest('GET', `/api/companies/${companyId}/users`);

        if (!res.ok) {
          throw new Error(`Failed to fetch users: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        console.log(`Fetched ${data.length} users for company ${currentCompany.id}:`, data);

        // Make sure we include the current user if they're not already in the list
        if (user && data.every((u: any) => u.id !== user.id)) {
          console.log('Adding current user to the list since they were not included');
          return [...data, {
            id: user.id,
            email: user.email,
            full_name: user.full_name,
            role: user.role,
            company_id: companyId
          }];
        }

        return data;
      } catch (error) {
        console.error('Error fetching users:', error);

        // Fallback: If we can't get the users, at least show the current user
        if (user) {
          console.log('Falling back to just showing the current user');
          return [{
            id: user.id,
            email: user.email,
            full_name: user.full_name,
            role: user.role,
            company_id: companyId
          }];
        }

        return [];
      }
    },
    enabled: !!(currentCompany?.company_id || currentCompany?.id)
  });

  // Log users data when it changes
  useEffect(() => {
    if (users) {
      console.log('Users data:', users);
    }
  }, [users]);

  // Fetch roles
  const { data: roles, isLoading: isLoadingRoles } = useQuery({
    queryKey: ['/api/roles', currentCompany?.company_id || currentCompany?.id],
    queryFn: async () => {
      const companyId = currentCompany?.company_id || currentCompany?.id;
      if (!companyId) return [];
      try {
        console.log('Fetching roles for company:', companyId);
        const res = await apiRequest('GET', `/api/roles?company_id=${companyId}`);
        const data = await res.json();
        console.log('Fetched roles:', data);
        return data;
      } catch (error) {
        console.error('Error fetching roles:', error);
        return [];
      }
    },
    enabled: !!(currentCompany?.company_id || currentCompany?.id)
  });

  // Fetch permissions
  const { data: permissions, isLoading: isLoadingPermissions } = useQuery({
    queryKey: ['/api/permissions'],
    queryFn: async () => {
      try {
        console.log('Fetching permissions');
        const res = await apiRequest('GET', '/api/permissions');

        if (!res.ok) {
          throw new Error(`Failed to fetch permissions: ${res.status} ${res.statusText}`);
        }

        const data = await res.json();
        console.log('Fetched permissions:', data);

        // Ensure we always return an array
        if (Array.isArray(data)) {
          return data;
        } else if (data && Array.isArray(data.permissions)) {
          return data.permissions;
        } else {
          console.warn('Permissions API returned non-array data:', data);
          return [];
        }
      } catch (error) {
        console.error('Error fetching permissions:', error);
        return [];
      }
    }
  });

  // Fetch groups
  const { data: groups, isLoading: isLoadingGroups } = useQuery({
    queryKey: ['/api/group-management/groups', currentCompany?.company_id || currentCompany?.id],
    queryFn: async () => {
      const companyId = currentCompany?.company_id || currentCompany?.id;
      if (!companyId) return [];
      try {
        // Make sure we're passing the company_id as a query parameter
        const res = await apiRequest('GET', `/api/group-management/groups?company_id=${companyId}`);
        const data = await res.json();
        console.log('Fetched groups:', data);
        return data;
      } catch (error) {
        console.error('Error fetching groups:', error);
        return [];
      }
    },
    enabled: !!(currentCompany?.company_id || currentCompany?.id)
  });

  // Create user mutation
  const createUserMutation = useMutation({
    mutationFn: async (data: typeof userForm) => {
      const res = await apiRequest('POST', '/api/auth/register', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies', currentCompany?.company_id || currentCompany?.id, 'users'] });
      toast({
        title: 'User created',
        description: 'The user has been created successfully.',
      });
      setIsCreateUserDialogOpen(false);
      resetUserForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating user',
        description: error.message || 'There was an error creating the user.',
        variant: 'destructive',
      });
    }
  });

  // Create role mutation
  const createRoleMutation = useMutation({
    mutationFn: async (data: typeof roleForm) => {
      const res = await apiRequest('POST', '/api/roles', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/roles', currentCompany?.company_id || currentCompany?.id] });
      toast({
        title: 'Role created',
        description: 'The role has been created successfully.',
      });
      setIsCreateRoleDialogOpen(false);
      resetRoleForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating role',
        description: error.message || 'There was an error creating the role.',
        variant: 'destructive',
      });
    }
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: async (data: typeof groupForm) => {
      const res = await apiRequest('POST', '/api/groups', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/group-management/groups', currentCompany?.company_id || currentCompany?.id] });
      toast({
        title: 'Group created',
        description: 'The group has been created successfully.',
      });
      setIsCreateGroupDialogOpen(false);
      resetGroupForm();
    },
    onError: (error: any) => {
      toast({
        title: 'Error creating group',
        description: error.message || 'There was an error creating the group.',
        variant: 'destructive',
      });
    }
  });

  // Handle form changes
  const handleUserFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setUserForm(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setRoleForm(prev => ({ ...prev, [name]: value }));
  };

  const handleGroupFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setGroupForm(prev => ({ ...prev, [name]: value }));
  };

  const handleRoleChange = (value: string) => {
    setUserForm(prev => ({ ...prev, role: value }));
  };

  const handleBranchChange = (value: string) => {
    setGroupForm(prev => ({ ...prev, branch_id: value ? parseInt(value) : null }));
  };

  const handlePermissionChange = (permissionId: number, checked: boolean) => {
    setRoleForm(prev => {
      if (checked) {
        return { ...prev, permissions: [...prev.permissions, permissionId] };
      } else {
        return { ...prev, permissions: prev.permissions.filter(id => id !== permissionId) };
      }
    });
  };

  // Form submission handlers
  const handleSubmitUser = (e: React.FormEvent) => {
    e.preventDefault();
    if (userForm.password !== userForm.confirmPassword) {
      toast({
        title: 'Passwords do not match',
        description: 'Please make sure your passwords match.',
        variant: 'destructive',
      });
      return;
    }
    createUserMutation.mutate(userForm);
  };

  const handleSubmitRole = (e: React.FormEvent) => {
    e.preventDefault();
    createRoleMutation.mutate(roleForm);
  };

  const handleSubmitGroup = (e: React.FormEvent) => {
    e.preventDefault();
    createGroupMutation.mutate(groupForm);
  };

  // Reset form states
  const resetUserForm = () => {
    setUserForm({
      email: '',
      full_name: '',
      password: '',
      confirmPassword: '',
      role: 'employee',
      company_id: currentCompany?.company_id || currentCompany?.id
    });
  };

  const resetRoleForm = () => {
    setRoleForm({
      name: '',
      description: '',
      company_id: currentCompany?.company_id || currentCompany?.id,
      permissions: []
    });
  };

  const resetGroupForm = () => {
    setGroupForm({
      name: '',
      description: '',
      company_id: currentCompany?.company_id || currentCompany?.id,
      branch_id: null
    });
  };

  // Define table columns
  const userColumns: ColumnDef<User>[] = [
    {
      accessorKey: 'full_name',
      header: 'Name',
    },
    {
      accessorKey: 'email',
      header: 'Email',
    },
    {
      accessorKey: 'role',
      header: 'Role',
      cell: ({ row }) => (
        <Badge variant="outline">{row.original.role}</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => console.log('Edit user', row.original.id)}>
            Edit
          </Button>
          <Button variant="ghost" size="sm" onClick={() => console.log('Manage roles', row.original.id)}>
            <UserCheck className="h-4 w-4 mr-1" />
            Roles
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate(`/user-management/permissions/user-permissions?userId=${row.original.id}`)}
          >
            <Eye className="h-4 w-4 mr-1" />
            Permissions
          </Button>
        </div>
      ),
    },
  ];

  const roleColumns: ColumnDef<Role>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      accessorKey: 'is_system',
      header: 'System Role',
      cell: ({ row }) => (
        row.original.is_system ? <Badge>System</Badge> : <Badge variant="outline">Custom</Badge>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => console.log('Edit role', row.original.id)}>
            Edit
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => navigate('/user-management/permissions')}
          >
            <Shield className="h-4 w-4 mr-1" />
            Permissions
          </Button>
        </div>
      ),
    },
  ];

  const groupColumns: ColumnDef<Group>[] = [
    {
      accessorKey: 'name',
      header: 'Name',
    },
    {
      accessorKey: 'description',
      header: 'Description',
    },
    {
      id: 'members',
      header: 'Members',
      cell: ({ row }) => (
        <span>{row.original.members?.length || 0} members</span>
      ),
    },
    {
      id: 'actions',
      cell: ({ row }) => (
        <div className="flex items-center gap-2">
          <Button variant="ghost" size="sm" onClick={() => console.log('Edit group', row.original.id)}>
            Edit
          </Button>
          <Button variant="ghost" size="sm" onClick={() => console.log('Manage members', row.original.id)}>
            Members
          </Button>
          <Button variant="ghost" size="sm" onClick={() => console.log('Manage roles', row.original.id)}>
            Roles
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold">User Management</h1>
          <p className="text-muted-foreground">Manage users, roles, groups, and permissions</p>
        </div>
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => navigate('/user-management/permissions')}
            className="flex items-center gap-2"
          >
            <Shield className="h-4 w-4" />
            Permission Matrix
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate('/user-management/role-hierarchy')}
            className="flex items-center gap-2"
          >
            <UserCheck className="h-4 w-4" />
            Role Hierarchy
          </Button>
          <Button
            variant="outline"
            onClick={() => navigate('/user-management/approval-workflows')}
            className="flex items-center gap-2"
          >
            <Workflow className="h-4 w-4" />
            Approval Workflows
          </Button>
          <div className="relative">
            <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search..."
              className="pl-8 w-[250px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
          {activeTab === 'users' && (
            <Button onClick={() => setIsCreateUserDialogOpen(true)}>
              <UserPlus className="mr-2 h-4 w-4" />
              Add User
            </Button>
          )}
          {activeTab === 'roles' && (
            <Button onClick={() => setIsCreateRoleDialogOpen(true)}>
              <Shield className="mr-2 h-4 w-4" />
              Add Role
            </Button>
          )}
          {activeTab === 'groups' && (
            <Button onClick={() => setIsCreateGroupDialogOpen(true)}>
              <Users className="mr-2 h-4 w-4" />
              Add Group
            </Button>
          )}
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Total Users</p>
                <p className="text-2xl font-bold">{users?.length || 0}</p>
              </div>
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Active Roles</p>
                <p className="text-2xl font-bold">{roles?.length || 0}</p>
              </div>
              <Shield className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">User Groups</p>
                <p className="text-2xl font-bold">{groups?.length || 0}</p>
              </div>
              <Settings className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-muted-foreground">Permissions</p>
                <p className="text-2xl font-bold">{permissions?.length || 0}</p>
              </div>
              <UserCheck className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-6">
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="roles">Roles</TabsTrigger>
          <TabsTrigger value="groups">Groups</TabsTrigger>
          <TabsTrigger value="bulk-operations">Bulk Operations</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>Users</CardTitle>
              <CardDescription>Manage users in your organization.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingUsers ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={userColumns} data={users || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="roles">
          <Card>
            <CardHeader>
              <CardTitle>Roles</CardTitle>
              <CardDescription>Manage roles and permissions.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingRoles ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={roleColumns} data={roles || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="groups">
          <Card>
            <CardHeader>
              <CardTitle>Groups</CardTitle>
              <CardDescription>Manage user groups.</CardDescription>
            </CardHeader>
            <CardContent>
              {isLoadingGroups ? (
                <div className="flex justify-center items-center h-40">
                  <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
                </div>
              ) : (
                <DataTable columns={groupColumns} data={groups || []} />
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="bulk-operations">
          <BulkOperationsManager />
        </TabsContent>
      </Tabs>

      {/* Create User Dialog */}
      <Dialog open={isCreateUserDialogOpen} onOpenChange={setIsCreateUserDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New User</DialogTitle>
            <DialogDescription>
              Create a new user for your organization.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitUser}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="full_name">Full Name</Label>
                <Input
                  id="full_name"
                  name="full_name"
                  value={userForm.full_name}
                  onChange={handleUserFormChange}
                  required
                />
              </div>

              <div className="grid gap-2">
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={userForm.email}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  value={userForm.password}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type="password"
                  value={userForm.confirmPassword}
                  onChange={handleUserFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="role">Role</Label>
                <Select value={userForm.role} onValueChange={handleRoleChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectGroup>
                      <SelectItem value="company_admin">Company Admin</SelectItem>
                      <SelectItem value="employee">Employee</SelectItem>
                      <SelectItem value="agent">Agent</SelectItem>
                    </SelectGroup>
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetUserForm();
                  setIsCreateUserDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createUserMutation.isPending}>
                {createUserMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create User"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Role Dialog */}
      <Dialog open={isCreateRoleDialogOpen} onOpenChange={setIsCreateRoleDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Add New Role</DialogTitle>
            <DialogDescription>
              Create a new role with specific permissions.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitRole}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Role Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={roleForm.name}
                  onChange={handleRoleFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={roleForm.description}
                  onChange={handleRoleFormChange}
                />
              </div>
              <div className="grid gap-2">
                <Label>Permissions</Label>
                <div className="border rounded-md p-4 h-[200px] overflow-y-auto">
                  {isLoadingPermissions ? (
                    <div className="flex justify-center items-center h-full">
                      <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
                    </div>
                  ) : (
                    <div className="grid grid-cols-2 gap-2">
                      {Array.isArray(permissions) && permissions.length > 0 ? (
                        permissions.map((permission: Permission) => (
                          <div key={permission.id} className="flex items-center space-x-2">
                            <Checkbox
                              id={`permission-${permission.id}`}
                              checked={roleForm.permissions.includes(permission.id)}
                              onCheckedChange={(checked) => handlePermissionChange(permission.id, !!checked)}
                            />
                            <label
                              htmlFor={`permission-${permission.id}`}
                              className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                            >
                              {permission.name}
                            </label>
                          </div>
                        ))
                      ) : (
                        <div className="text-center text-muted-foreground py-4">
                          No permissions available
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetRoleForm();
                  setIsCreateRoleDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createRoleMutation.isPending}>
                {createRoleMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Role"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Create Group Dialog */}
      <Dialog open={isCreateGroupDialogOpen} onOpenChange={setIsCreateGroupDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Add New Group</DialogTitle>
            <DialogDescription>
              Create a new user group.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleSubmitGroup}>
            <div className="grid gap-4 py-4">
              <div className="grid gap-2">
                <Label htmlFor="name">Group Name</Label>
                <Input
                  id="name"
                  name="name"
                  value={groupForm.name}
                  onChange={handleGroupFormChange}
                  required
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="description">Description</Label>
                <Input
                  id="description"
                  name="description"
                  value={groupForm.description}
                  onChange={handleGroupFormChange}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="branch_id">Branch (Optional)</Label>
                <Select value={groupForm.branch_id?.toString() || ''} onValueChange={handleBranchChange}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a branch" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">No Branch</SelectItem>
                    {/* Branch options would be populated here */}
                  </SelectContent>
                </Select>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  resetGroupForm();
                  setIsCreateGroupDialogOpen(false);
                }}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={createGroupMutation.isPending}>
                {createGroupMutation.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating...
                  </>
                ) : (
                  "Create Group"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
