import { Express, Response } from 'express';
import { storage } from '../storage/index';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { requirePrefixSettings } from '../middleware/prefix-settings';
import {
  requireLoanCreationPermission,
  requireLoanApprovalPermission,
  requirePermissionWithContext
} from '../middleware/enhancedPermission';
import { insertLoanSchema } from '../../shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerLoanRoutes(app: Express): void {
  // Get all loans for a company
  app.get('/api/companies/:companyId/loans', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Check if branch_id filter is provided
      const branchId = req.query.branch_id ? parseInt(req.query.branch_id as string) : undefined;

      let loans;
      if (branchId) {
        loans = await storage.getLoansByBranch(branchId);
        // Filter by company_id as well for security
        loans = loans.filter(loan => loan.company_id === companyId);
      } else {
        loans = await storage.getLoansByCompany(companyId);
      }

      return res.json(loans);
    } catch (error) {
      console.error('Error fetching loans:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get loans for a customer
  app.get('/api/customers/:customerId/loans', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const customerId = parseInt(req.params.customerId);

      // Get the customer to check company access
      const customer = await storage.getCustomer(customerId);

      if (!customer) {
        return res.status(404).json({ message: 'Customer not found' });
      }

      // Check if user has access to this customer's company
      if (req.user!.role !== 'saas_admin' && customer.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === customer.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this customer' });
        }
      }

      const loans = await storage.getLoansByCustomer(customerId);
      return res.json(loans);
    } catch (error) {
      console.error(`Error fetching loans for customer ${req.params.customerId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get loan by ID
  app.get('/api/loans/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      return res.json(loan);
    } catch (error) {
      console.error(`Error fetching loan ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create loan
  app.post('/api/loans', authMiddleware, requirePrefixSettings, requireLoanCreationPermission('amount'), async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);

      // Validate input
      const result = insertLoanSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Check if user has access to the company
      const loanCompanyId = result.data.company_id;
      if (req.user!.role !== 'saas_admin' && loanCompanyId !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loanCompanyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // Check if customer exists and belongs to the company
      const customer = await storage.getCustomer(result.data.customer_id);
      if (!customer) {
        return res.status(400).json({ message: 'Customer not found' });
      }

      if (customer.company_id !== loanCompanyId) {
        return res.status(400).json({ message: 'Customer does not belong to the specified company' });
      }

      const loan = await storage.createLoan(result.data);

      // Return the loan immediately
      res.status(201).json({
        loan,
        message: 'Loan created successfully. Collections are being generated.',
        scheduleStatus: 'processing'
      });

      // Generate collections directly instead of payment schedules
      setTimeout(async () => {
        try {
          console.log(`[AUTO-COLLECTIONS] Asynchronously generating collections for loan ${loan.id} (company ${loan.company_id})`);

          // Get loan details for collection generation
          const loanDetails = await storage.getLoan(loan.id);
          if (!loanDetails) {
            throw new Error(`Loan with id=${loan.id} not found`);
          }

          // Calculate payment amount and dates
          const amount = parseFloat(loanDetails.amount);
          const interestRate = parseFloat(loanDetails.interest_rate);
          const term = loanDetails.term;
          const startDate = new Date(loanDetails.start_date);
          const paymentFrequency = loanDetails.payment_frequency || 'monthly';

          // Calculate payment amount based on loan type and interest type
          const interestType = loanDetails.interest_type || 'flat';
          const isUpfrontInterest = loanDetails.is_upfront_interest === true;

          // Calculate total interest
          let totalInterest = amount * (interestRate / 100);

          // For reducing balance, we need a more complex calculation
          if (interestType === 'reducing') {
            // This is a simplified calculation - in a real app, you'd use a more accurate formula
            totalInterest = (amount * (interestRate / 100) * (term + 1)) / (2 * 12);
          }

          // Calculate payment amount - use only principal for collections to match payment schedule
          const paymentAmount = amount / term;

          // Generate collections for each payment period
          const collections = [];

          for (let i = 0; i < term; i++) {
            // Calculate due date based on payment frequency
            const dueDate = new Date(startDate);

            // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
            if (paymentFrequency === 'daily') {
              dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
            } else if (paymentFrequency === 'weekly') {
              dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
            } else if (paymentFrequency === 'biweekly') {
              dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
            } else if (paymentFrequency === 'monthly') {
              dueDate.setMonth(dueDate.getMonth() + (i+1));
            } else {
              // Default to monthly
              dueDate.setMonth(dueDate.getMonth() + (i+1));
            }

            // Create collection
            const collection = await storage.createCollection({
              company_id: loanDetails.company_id,
              loan_id: loanDetails.id,
              customer_id: loanDetails.customer_id,
              agent_id: null,
              amount: paymentAmount.toFixed(2),
              original_amount: paymentAmount.toFixed(2),
              scheduled_date: dueDate,
              collection_date: null,
              status: 'pending',
              payment_method: null,
              receipt_id: null,
              notes: `Auto-generated for payment #${i + 1}`,
              emi_number: i + 1,
              time_of_day: null,
              fine_amount: '0'
            });

            collections.push(collection);
          }

          console.log(`[AUTO-COLLECTIONS] Successfully generated ${collections.length} collections for loan ${loan.id}`);
        } catch (error) {
          console.error('[AUTO-COLLECTIONS] Error generating collections automatically:', error);
        }
      }, 0);
    } catch (error) {
      console.error('Error creating loan:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Approve loan
  app.post('/api/loans/:id/approve', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      // Get the loan to check company access and amount
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      // Check loan approval permission based on amount
      const { EnhancedPermissionService } = await import('../services/enhancedPermissionService');
      const permissionService = new EnhancedPermissionService();
      const loanAmount = parseFloat(loan.amount);

      const hasApprovalPermission = await permissionService.checkLoanApprovalPermission(userId, loanAmount);
      if (!hasApprovalPermission) {
        return res.status(403).json({
          message: 'Insufficient permissions for loan approval',
          required_amount_limit: loanAmount,
          action: 'approve_loan'
        });
      }

      // Check if loan is in a state that can be approved
      if (loan.status === 'approved' || loan.status === 'active') {
        return res.status(400).json({ message: 'Loan is already approved' });
      }

      if (loan.status === 'rejected' || loan.status === 'closed') {
        return res.status(400).json({ message: 'Cannot approve a rejected or closed loan' });
      }

      // Update loan status to approved
      const updateData = {
        status: 'approved' as const,
        approved_by: userId,
        approved_at: new Date()
      };

      const updatedLoan = await storage.updateLoan(loanId, loan.company_id, updateData);

      return res.json({
        loan: updatedLoan,
        message: 'Loan approved successfully'
      });
    } catch (error) {
      console.error(`Error approving loan ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Disburse loan
  app.post('/api/loans/:id/disburse', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      // Get the loan to check company access and amount
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      // Check loan disbursement permission based on amount
      const { EnhancedPermissionService } = await import('../services/enhancedPermissionService');
      const permissionService = new EnhancedPermissionService();
      const loanAmount = parseFloat(loan.amount);

      const hasDisbursementPermission = await permissionService.checkLoanDisbursementPermission(userId, loanAmount);
      if (!hasDisbursementPermission) {
        return res.status(403).json({
          message: 'Insufficient permissions for loan disbursement',
          required_amount_limit: loanAmount,
          action: 'disburse_loan'
        });
      }

      // Check if loan is in a state that can be disbursed
      if (loan.status !== 'approved') {
        return res.status(400).json({ message: 'Loan must be approved before disbursement' });
      }

      if (loan.status === 'active') {
        return res.status(400).json({ message: 'Loan is already disbursed' });
      }

      // Update loan status to active (disbursed)
      const updateData = {
        status: 'active' as const,
        disbursed_by: userId,
        disbursed_at: new Date(),
        disbursement_method: req.body.disbursement_method || 'bank_transfer',
        disbursement_reference: req.body.disbursement_reference || null
      };

      const updatedLoan = await storage.updateLoan(loanId, loan.company_id, updateData);

      return res.json({
        loan: updatedLoan,
        message: 'Loan disbursed successfully'
      });
    } catch (error) {
      console.error(`Error disbursing loan ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update loan
  app.put('/api/loans/:id', authMiddleware, requirePermissionWithContext('loan_create', undefined, { logAccess: true }), async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      // Get the loan to check company access
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      // Validate input
      const result = insertLoanSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: formatZodError(result.error)
        });
      }

      // Don't allow changing company_id or customer_id
      delete result.data.company_id;
      delete result.data.customer_id;

      const updateData = result.data;

      // Check if the loan status was changed to active or approved
      if (updateData.status && (updateData.status === 'active' || updateData.status === 'approved')) {
        console.log(`[AUTO-COLLECTIONS] Loan status changed to ${updateData.status}, checking if collections need to be generated`);

        // Check if collections already exist for this loan
        const existingCollections = await storage.getCollectionsByLoan(loanId, loan.company_id);

        if (!existingCollections || existingCollections.length === 0) {
          console.log(`[AUTO-COLLECTIONS] No collections found for loan ${loanId}, generating them automatically`);

          try {
            // Get loan details for collection generation
            const loanDetails = await storage.getLoan(loanId);
            if (!loanDetails) {
              throw new Error(`Loan with id=${loanId} not found`);
            }

            // Calculate payment amount and dates
            const amount = parseFloat(loanDetails.amount);
            const interestRate = parseFloat(loanDetails.interest_rate);
            const term = loanDetails.term;
            const startDate = new Date(loanDetails.start_date);
            const paymentFrequency = loanDetails.payment_frequency || 'monthly';

            // Calculate payment amount based on loan type and interest type
            const interestType = loanDetails.interest_type || 'flat';
            const isUpfrontInterest = loanDetails.is_upfront_interest === true;

            // Calculate total interest
            let totalInterest = amount * (interestRate / 100);

            // For reducing balance, we need a more complex calculation
            if (interestType === 'reducing') {
              // This is a simplified calculation - in a real app, you'd use a more accurate formula
              totalInterest = (amount * (interestRate / 100) * (term + 1)) / (2 * 12);
            }

            // Calculate payment amount - use only principal for collections to match payment schedule
            const paymentAmount = amount / term;

            // Generate collections for each payment period
            const collections = [];

            for (let i = 0; i < term; i++) {
              // Calculate due date based on payment frequency
              const dueDate = new Date(startDate);

              // For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
              if (paymentFrequency === 'daily') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
              } else if (paymentFrequency === 'weekly') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
              } else if (paymentFrequency === 'biweekly') {
                dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
              } else if (paymentFrequency === 'monthly') {
                dueDate.setMonth(dueDate.getMonth() + (i+1));
              } else {
                // Default to monthly
                dueDate.setMonth(dueDate.getMonth() + (i+1));
              }

              // Create collection
              const collection = await storage.createCollection({
                company_id: loanDetails.company_id,
                loan_id: loanDetails.id,
                customer_id: loanDetails.customer_id,
                agent_id: null,
                amount: paymentAmount.toFixed(2),
                original_amount: paymentAmount.toFixed(2),
                scheduled_date: dueDate,
                collection_date: null,
                status: 'pending',
                payment_method: null,
                receipt_id: null,
                notes: `Auto-generated for payment #${i + 1}`,
                emi_number: i + 1,
                time_of_day: null,
                fine_amount: '0'
              });

              collections.push(collection);
            }

            console.log(`[AUTO-COLLECTIONS] Successfully generated ${collections.length} collections for loan ${loanId}`);
          } catch (error) {
            console.error('[AUTO-COLLECTIONS] Error generating collections automatically:', error);
            // We don't fail the whole request if collection generation fails
          }
        } else {
          console.log(`[AUTO-COLLECTIONS] Collections already exist for loan ${loanId}, skipping automatic generation`);
        }
      }

      const updatedLoan = await storage.updateLoan(loanId, loan.company_id, updateData);
      return res.json(updatedLoan);
    } catch (error) {
      console.error(`Error updating loan ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Check collection generation status
  app.get('/api/loans/:id/schedule-status', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      // Get the loan to check company access
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }

      // Check if collections exist
      const collections = await storage.getCollectionsByLoan(loanId, loan.company_id);

      if (collections.length > 0) {
        return res.json({
          status: 'completed',
          count: collections.length
        });
      } else {
        return res.json({
          status: 'processing'
        });
      }
    } catch (error) {
      console.error('Error checking collection status:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete loan
  app.delete('/api/loans/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { userId, companyId } = ensureUserAuth(req);
      const loanId = parseInt(req.params.id);

      // Get the loan to check company access
      const loan = await storage.getLoan(loanId);

      if (!loan) {
        return res.status(404).json({ message: 'Loan not found' });
      }

      // Check if user has access to this loan's company
      if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(userId);
        const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this loan' });
        }
      }      // Check if force delete is requested
      const forceDelete = req.query.force === 'true' || req.query.forceDelete === 'true';

      if (forceDelete) {
        const result = await storage.deleteLoanWithCollections(loanId, loan.company_id);

        if (!result.success) {
          // If there are non-pending collections, we can't delete even with force
          if (result.error && result.error.includes('non-pending collections')) {
            return res.status(400).json({
              message: result.error,
              hint: 'You must change all collections to pending status or delete them individually first'
            });
          }

          return res.status(400).json({ message: result.error });
        }

        return res.json({
          message: 'Loan and all associated records deleted successfully',
          collectionsDeleted: result.collectionsDeleted,
          paymentSchedulesDeleted: result.paymentSchedulesDeleted,
          transactionsDeleted: result.transactionsDeleted
        });
      } else {
        const result = await storage.deleteLoan(loanId, loan.company_id);

        if (!result.success) {
          // Check for different types of dependencies
          const hasCollections = result.collectionsCount && result.collectionsCount > 0;
          const hasPaymentSchedules = result.paymentSchedulesCount && result.paymentSchedulesCount > 0;
          const hasTransactions = result.transactionsCount && result.transactionsCount > 0;

          if (hasCollections || hasPaymentSchedules || hasTransactions) {
            // Check if there are non-pending collections
            if (result.nonPendingCollections && result.nonPendingCollections > 0) {
              return res.status(400).json({
                message: 'Cannot delete loan with non-pending collections. All collections must have "pending" status.',
                collectionsCount: result.collectionsCount,
                nonPendingCollections: result.nonPendingCollections,
                hint: 'All collections must have "pending" status before deletion is allowed'
              });
            } else {
              // All collections are pending or there are other dependencies
              return res.status(400).json({
                message: result.error,
                collectionsCount: result.collectionsCount,
                paymentSchedulesCount: result.paymentSchedulesCount,
                transactionsCount: result.transactionsCount,
                allPending: hasCollections && !result.nonPendingCollections,
                hint: 'Use ?force=true to delete the loan and all associated records'
              });
            }
          }

          return res.status(400).json({ message: result.error });
        }

        return res.json({ message: 'Loan deleted successfully' });
      }
    } catch (error) {
      console.error(`Error deleting loan ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
