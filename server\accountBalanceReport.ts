import { db } from './db';
import { eq, and, between, sql } from 'drizzle-orm';
import { format } from 'date-fns';
import errorLogger from './utils/errorLogger';
import { accounts, transactions } from '@shared/schema';

/**
 * Generates an account balance report for a company within a specified date range
 *
 * @param companyId The ID of the company
 * @param startDate Start date in YYYY-MM-DD format
 * @param endDate End date in YYYY-MM-DD format
 * @returns Promise<AccountBalanceReport[]>
 */
export async function getAccountBalanceReport(
  companyId: number,
  startDate: string,
  endDate: string
) {
  try {
    console.log(`Generating account balance report for company ${companyId} from ${startDate} to ${endDate}`);

    // Validate dates
    if (!startDate || !endDate) {
      errorLogger.logError(`Missing required date parameters`, 'account-balance-report', new Error('Start date and end date are required'));
      return [];
    }

    // Parse dates
    let startDateObj, endDateObj;
    try {
      startDateObj = new Date(startDate);
      endDateObj = new Date(endDate);

      // Check if dates are valid
      if (isNaN(startDateObj.getTime()) || isNaN(endDateObj.getTime())) {
        errorLogger.logError(`Invalid date format`, 'account-balance-report', new Error('Invalid date format'));
        return [];
      }

      // Check if end date is after start date
      if (endDateObj < startDateObj) {
        errorLogger.logError(`End date is before start date`, 'account-balance-report', new Error('End date must be after start date'));
        return [];
      }

      // Check if dates are in the future
      const currentDate = new Date();
      if (startDateObj > currentDate || endDateObj > currentDate) {
        errorLogger.logWarning(`Future dates used in report`, 'account-balance-report', { startDate, endDate });
        // We'll continue processing but log a warning
      }
    } catch (dateError) {
      errorLogger.logError(`Error parsing dates`, 'account-balance-report', dateError as Error);
      return [];
    }

    // Get all accounts for the company
    let companyAccounts;
    try {
      companyAccounts = await db.select()
        .from(accounts)
        .where(eq(accounts.company_id, companyId));

      if (!companyAccounts || companyAccounts.length === 0) {
        errorLogger.logWarning(`No accounts found for company ${companyId}`, 'account-balance-report');
        return [];
      }
    } catch (accountsError) {
      errorLogger.logError(`Error fetching company accounts`, 'account-balance-report', accountsError as Error);
      return [];
    }

    // Get all transactions for the period
    let periodTransactions, openingBalanceTransactions;
    try {
      periodTransactions = await db.select()
        .from(transactions)
        .where(and(
          eq(transactions.company_id, companyId),
          between(transactions.transaction_date, startDateObj, endDateObj)
        ));

      // Calculate opening balances (transactions before start date)
      openingBalanceTransactions = await db.select()
        .from(transactions)
        .where(and(
          eq(transactions.company_id, companyId),
          sql`${transactions.transaction_date} < ${startDateObj}`
        ));
    } catch (transactionsError) {
      errorLogger.logError(`Error fetching transactions`, 'account-balance-report', transactionsError as Error);
      return [];
    }

    // Group transactions by account
    const accountTransactionsMap = new Map();

    // Initialize with all accounts
    companyAccounts.forEach(account => {
      accountTransactionsMap.set(account.id, {
        account,
        openingBalance: 0,
        periodDebits: 0,
        periodCredits: 0,
        closingBalance: 0,
        transactions: []
      });
    });

    // Calculate opening balances
    openingBalanceTransactions.forEach(transaction => {
      const accountData = accountTransactionsMap.get(transaction.account_id);
      if (!accountData) return;

      const amount = Number(transaction.amount);

      if (transaction.transaction_type === 'debit') {
        // For asset and expense accounts, debits increase the balance
        if (accountData.account.account_type === 'asset' || accountData.account.account_type === 'expense') {
          accountData.openingBalance += amount;
        } else {
          // For liability, equity, and income accounts, debits decrease the balance
          accountData.openingBalance -= amount;
        }
      } else if (transaction.transaction_type === 'credit') {
        // For liability, equity, and income accounts, credits increase the balance
        if (accountData.account.account_type === 'liability' ||
            accountData.account.account_type === 'equity' ||
            accountData.account.account_type === 'income') {
          accountData.openingBalance += amount;
        } else {
          // For asset and expense accounts, credits decrease the balance
          accountData.openingBalance -= amount;
        }
      }
    });

    // Process period transactions
    periodTransactions.forEach(transaction => {
      const accountData = accountTransactionsMap.get(transaction.account_id);
      if (!accountData) return;

      const amount = Number(transaction.amount);

      // Add transaction to the account's transaction list
      accountData.transactions.push({
        id: transaction.id,
        date: format(new Date(transaction.transaction_date), 'yyyy-MM-dd'),
        description: transaction.description || '',
        type: transaction.transaction_type,
        amount
      });

      // Update period totals
      if (transaction.transaction_type === 'debit') {
        accountData.periodDebits += amount;

        // For asset and expense accounts, debits increase the balance
        if (accountData.account.account_type === 'asset' || accountData.account.account_type === 'expense') {
          accountData.closingBalance += amount;
        } else {
          // For liability, equity, and income accounts, debits decrease the balance
          accountData.closingBalance -= amount;
        }
      } else if (transaction.transaction_type === 'credit') {
        accountData.periodCredits += amount;

        // For liability, equity, and income accounts, credits increase the balance
        if (accountData.account.account_type === 'liability' ||
            accountData.account.account_type === 'equity' ||
            accountData.account.account_type === 'income') {
          accountData.closingBalance += amount;
        } else {
          // For asset and expense accounts, credits decrease the balance
          accountData.closingBalance -= amount;
        }
      }
    });

    // Calculate final closing balances (opening + period activity)
    accountTransactionsMap.forEach(accountData => {
      accountData.closingBalance += accountData.openingBalance;
    });

    // Convert map to array and format for response
    const result = Array.from(accountTransactionsMap.values())
      // Only include accounts with activity or balances
      .filter(data =>
        data.openingBalance !== 0 ||
        data.periodDebits !== 0 ||
        data.periodCredits !== 0 ||
        data.closingBalance !== 0 ||
        data.transactions.length > 0
      )
      .map(data => ({
        id: data.account.id,
        account_code: data.account.account_code,
        account_name: data.account.account_name,
        account_type: data.account.account_type,
        category: data.account.category,
        openingBalance: data.openingBalance,
        periodDebits: data.periodDebits,
        periodCredits: data.periodCredits,
        closingBalance: data.closingBalance,
        netChange: data.closingBalance - data.openingBalance,
        transactions: data.transactions
      }))
      // Sort by account code
      .sort((a, b) => a.account_code.localeCompare(b.account_code));

    return result;
  } catch (error) {
    errorLogger.logError(`Failed to generate account balance report for company ${companyId}`, 'account-balance-report', error as Error);

    // Return empty report structure on error
    return [];
  }
}
