# Payment Schedule and Form Submission Issues

## Issue Summary
The payment schedule and form submission functionality in the TrackFina application are currently experiencing errors. These issues are preventing users from viewing payment schedules for loans and accessing form submissions related to loans.

## Error Analysis from Logs
1. **Payment Schedule Error**:
   ```
   [error] [network] HTTP Error 500: Internal Server Error - /api/payment-schedules?loanId=35&companyId=2
   [error] [apiRequest] API request failed: 500: {"message":"Server error"}
   [error] [react-query] Query error for key: ["/api/payment-schedules",35]
   ```

2. **Form Submission Error**:
   ```
   [error] [network] HTTP Error 500: Internal Server Error - /api/companies/2/loans/35/form-submissions
   [error] [queryFn] Query failed: 500: {"message":"Failed to fetch form submissions"}
   [error] [react-query] Query error for key: ["/api/companies/2/loans/35/form-submissions"]
   ```

## Current State
1. Payment Schedule System:
   - The API endpoint for fetching payment schedules is returning a 500 Internal Server Error
   - The error occurs when trying to fetch schedules for a specific loan ID and company ID
   - This is likely related to missing methods in the storage interface or database query issues

2. Form Submission System:
   - The API endpoint for fetching form submissions related to loans is returning a 500 Internal Server Error
   - The error occurs when trying to fetch form submissions for a specific loan ID and company ID
   - This is likely related to missing database tables or implementation issues

## Bug Investigation

### 1. Payment Schedule Error Analysis
The error occurs when calling the endpoint `/api/payment-schedules?loanId=35&companyId=2`. This suggests:
- The payment schedule endpoint might be missing implementation in `routes.ts`
- The storage method `getPaymentSchedulesByLoan` might be missing or improperly implemented
- There could be a database schema issue with the payment schedules table

### 2. Form Submission Error Analysis
The error occurs when calling the endpoint `/api/companies/2/loans/35/form-submissions`. This suggests:
- The form submissions endpoint might be missing implementation in `routes.ts`
- The related storage methods for form submissions might be missing
- There might be a missing database table for form submissions or a schema issue

## Development & Fix Plan

### Phase 1: Fix Payment Schedule API
1. Investigate the payment schedule endpoint in `routes.ts`
2. Check if the `getPaymentSchedulesByLoan` method is correctly implemented in `storage.ts`
3. Verify that the payment schedule table exists in the database and has the correct schema
4. Add proper error handling to the endpoint to provide more descriptive error messages
5. Test the endpoint by creating a test payment schedule

### Phase 2: Fix Form Submission API
1. Investigate the form submission endpoint in `routes.ts`
2. Check if the necessary storage methods for form submissions are implemented
3. Verify that the form submissions table exists in the database and has the correct schema
4. Add proper error handling to the endpoint
5. Test the endpoint by creating a test form submission

## Testing Plan
1. Create a test loan with payment schedules
2. Test the payment schedule API to ensure it returns the expected data
3. Create a test form submission for a loan
4. Test the form submission API to ensure it returns the expected data

## Issues to Fix (Prioritized)
1. Fix the payment schedule API endpoint
2. Fix the form submission API endpoint
3. Add comprehensive error handling and logging
4. Ensure all required storage methods are implemented
5. Add validation to prevent similar issues in the future