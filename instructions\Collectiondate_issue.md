## Task 2: Fix Collection Date Mismatch with Payment Schedule

**Issue Description:**
While generating loans, everything is working fine, but in the collection module, the dates are not exactly matching what we have in the Payment Schedule data in loan details.

**Required Behavior:**
- Ensure collection dates exactly match the dates in the Payment Schedule
- Fix the date synchronization between loan payment schedules and collection records
- Do not modify or disturb the existing loan model structure
- Maintain data consistency between loan and collection modules

**Issue Description:**
For daily loans, the collection start date is incorrectly set to the loan disbursement date (day 0) instead of the first payment date (day 1). This causes a mismatch between the payment schedule and collections.

**Required Behavior:**
- Collections should start from day 1 (the day after loan disbursement), not day 0
- The first collection date should match day 1 in the payment schedule
- Ensure all subsequent collection dates align with the payment schedule
- Fix this issue specifically for daily loans

**Solution:**
The issue was fixed by modifying the date calculation for ALL loan types in the collection generation code. We now use `i+1` for all payment frequencies to skip day 0 (the loan disbursement date) and start from day 1.

Changes were made to:
1. All instances of date calculation in `routes.ts`
2. All instances of date calculation in `loan.routes.ts`

For all loan types, the code now uses:
```typescript
// For all payment frequencies, add (i+1) to skip day 0 (loan disbursement date) and start from day 1
if (paymentFrequency === 'daily') {
  dueDate.setDate(dueDate.getDate() + ((i+1) * 1));
} else if (paymentFrequency === 'weekly') {
  dueDate.setDate(dueDate.getDate() + ((i+1) * 7));
} else if (paymentFrequency === 'biweekly') {
  dueDate.setDate(dueDate.getDate() + ((i+1) * 14));
} else if (paymentFrequency === 'monthly') {
  dueDate.setMonth(dueDate.getMonth() + (i+1));
} else {
  // Default to monthly if not specified
  dueDate.setMonth(dueDate.getMonth() + (i+1));
}
```

**Result:**
After this fix, the collection dates now correctly match the payment schedule for all loan types:

- Daily Loans: Collection #1 starts on day 1 (May 24, 2025), not day 0 (May 23, 2025)
- Weekly Loans: Collection #1 starts on day 7 (May 30, 2025), not day 0 (May 23, 2025)
- Monthly Loans: Collection #1 starts on month 1 (June 23, 2025), not month 0 (May 23, 2025)

This ensures consistency between the payment schedule and collections for all loan types.

**Status:** Completed