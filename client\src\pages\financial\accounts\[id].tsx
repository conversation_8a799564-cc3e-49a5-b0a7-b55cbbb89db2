import { useState } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useRoute, useLocation, Link } from 'wouter';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { useContextData } from '@/lib/useContextData';

// UI Components
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Spinner } from '@/components/ui/spinner';
import { Badge } from '@/components/ui/badge';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";

// Icons
import {
  ArrowLeft,
  Edit,
  Trash2,
  Calendar,
  FileText,
  DollarSign,
  RefreshCw,
  List
} from 'lucide-react';

// Types
interface Account {
  id: number;
  company_id: number;
  account_code: string;
  account_name: string;
  account_type: 'asset' | 'liability' | 'equity' | 'income' | 'expense';
  parent_account_id: number | null;
  is_active: boolean;
  is_system: boolean;
  created_at: string;
  updated_at: string;
  // Additional fields that may be returned with account details
  parent_account?: {
    id: number;
    account_code: string;
    account_name: string;
  };
  balance?: number;
  description?: string;
}

interface Transaction {
  id: number;
  account_id: number;
  transaction_date: string;
  transaction_type: 'debit' | 'credit';
  amount: number;
  description: string;
  reference_type: string;
  reference_id: number;
  created_at: string;
  // Reference codes from joined tables
  loan_reference?: string;
  collection_reference?: string;
}

const AccountDetailPage = () => {
  const { companyId } = useContextData();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [, params] = useRoute('/financial/accounts/:id');
  const accountId = params?.id ? parseInt(params.id) : 0;
  const [, navigate] = useLocation();
  const [activeTab, setActiveTab] = useState('overview');

  // Fetch account details
  const {
    data: account,
    isLoading: isLoadingAccount,
    isError: isAccountError,
    refetch: refetchAccount
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'accounts', accountId],
    queryFn: async () => {
      if (!companyId || !accountId) return null;
      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts/${accountId}`);
      return await response.json();
    },
    enabled: !!companyId && !!accountId
  });

  // Fetch account transactions
  const {
    data: transactions = [],
    isLoading: isLoadingTransactions
  } = useQuery({
    queryKey: ['/api/companies', companyId, 'accounts', accountId, 'transactions'],
    queryFn: async () => {
      if (!companyId || !accountId) return [];
      const response = await apiRequest('GET', `/api/companies/${companyId}/accounts/${accountId}/transactions`);
      return await response.json();
    },
    enabled: !!companyId && !!accountId && activeTab === 'transactions'
  });

  // Delete account mutation
  const deleteMutation = useMutation({
    mutationFn: async () => {
      await apiRequest('DELETE', `/api/companies/${companyId}/accounts/${accountId}`);
    },
    onSuccess: () => {
      toast({
        title: "Account deleted",
        description: "The account has been deleted successfully",
      });
      queryClient.invalidateQueries({ queryKey: ['/api/companies', companyId, 'accounts'] });
      navigate('/financial/accounts');
    },
    onError: () => {
      toast({
        title: "Error",
        description: "Failed to delete account. It may be in use by transactions.",
        variant: "destructive",
      });
    }
  });

  // Handle delete action
  const handleDelete = () => {
    deleteMutation.mutate();
  };

  // Navigate to edit page
  const handleEdit = () => {
    navigate(`/financial/accounts/${accountId}/edit`);
  };

  // Navigate back to accounts list
  const handleBack = () => {
    navigate('/financial/accounts');
  };

  // Format date string
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  // Get account type badge color
  const getAccountTypeColor = (type: string) => {
    const colors = {
      'asset': 'bg-blue-100 text-blue-800',
      'liability': 'bg-red-100 text-red-800',
      'equity': 'bg-green-100 text-green-800',
      'income': 'bg-purple-100 text-purple-800',
      'expense': 'bg-yellow-100 text-yellow-800'
    };
    return colors[type as keyof typeof colors] || 'bg-gray-100 text-gray-800';
  };

  // Loading state
  if (isLoadingAccount) {
    return (
      <div className="container mx-auto p-4 flex justify-center items-center min-h-[60vh]">
        <Spinner size="lg" />
      </div>
    );
  }

  // Error state
  if (isAccountError || !account) {
    return (
      <div className="container mx-auto p-4">
        <Card>
          <CardHeader>
            <div className="flex items-center mb-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={handleBack}
                className="mr-2"
              >
                <ArrowLeft className="h-4 w-4 mr-1" />
                Back
              </Button>
            </div>
            <CardTitle className="text-2xl font-bold">Error</CardTitle>
            <CardDescription>
              There was an error loading the account details.
            </CardDescription>
          </CardHeader>
          <CardContent className="text-center py-8">
            <p className="text-red-500 mb-4">Account not found or you don't have permission to view it.</p>
            <Button onClick={handleBack}>Return to Accounts List</Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Card>
        <CardHeader>
          <div className="flex items-center mb-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBack}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4 mr-1" />
              Back
            </Button>
          </div>
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-4">
            <div>
              <CardTitle className="text-2xl font-bold flex items-center">
                {account.account_code} - {account.account_name}
                {!account.is_active && (
                  <Badge variant="outline" className="ml-2 text-gray-500">
                    Inactive
                  </Badge>
                )}
              </CardTitle>
              <CardDescription className="mt-1 flex items-center">
                <Badge className={`${getAccountTypeColor(account.account_type)} mr-2`}>
                  {account.account_type.charAt(0).toUpperCase() + account.account_type.slice(1)}
                </Badge>
                {account.parent_account && (
                  <span className="text-sm">
                    Sub-account of: {account.parent_account.account_code} - {account.parent_account.account_name}
                  </span>
                )}
              </CardDescription>
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => refetchAccount()}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
              {!account.is_system && (
                <>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={handleEdit}
                  >
                    <Edit className="h-4 w-4 mr-2" />
                    Edit
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        size="sm"
                        variant="destructive"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Delete
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Account</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete this account?
                          This will permanently remove the account and cannot be undone.
                          If this account has transactions, the delete operation may fail.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={handleDelete}
                          className="bg-red-500 hover:bg-red-600"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </>
              )}
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="overview" onValueChange={setActiveTab}>
            <TabsList className="mb-4">
              <TabsTrigger value="overview">
                <FileText className="h-4 w-4 mr-2" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="transactions">
                <DollarSign className="h-4 w-4 mr-2" />
                Transactions
              </TabsTrigger>
            </TabsList>
            <TabsContent value="overview">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Account Details</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <dl className="divide-y">
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Account Code</dt>
                        <dd className="col-span-2">{account.account_code}</dd>
                      </div>
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Account Name</dt>
                        <dd className="col-span-2">{account.account_name}</dd>
                      </div>
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Account Type</dt>
                        <dd className="col-span-2">
                          <Badge className={getAccountTypeColor(account.account_type)}>
                            {account.account_type.charAt(0).toUpperCase() + account.account_type.slice(1)}
                          </Badge>
                        </dd>
                      </div>
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Status</dt>
                        <dd className="col-span-2">
                          {account.is_active ? 'Active' : 'Inactive'}
                        </dd>
                      </div>
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Parent Account</dt>
                        <dd className="col-span-2">
                          {account.parent_account ? (
                            <Link href={`/financial/accounts/${account.parent_account.id}`}>
                              {account.parent_account.account_code} - {account.parent_account.account_name}
                            </Link>
                          ) : 'None (Top-level account)'}
                        </dd>
                      </div>
                      {account.description && (
                        <div className="py-2 grid grid-cols-3">
                          <dt className="font-medium">Description</dt>
                          <dd className="col-span-2">{account.description}</dd>
                        </div>
                      )}
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Created</dt>
                        <dd className="col-span-2">{formatDate(account.created_at)}</dd>
                      </div>
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Last Updated</dt>
                        <dd className="col-span-2">{formatDate(account.updated_at)}</dd>
                      </div>
                    </dl>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Financial Summary</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <dl className="divide-y">
                      <div className="py-2 grid grid-cols-3">
                        <dt className="font-medium">Current Balance</dt>
                        <dd className="col-span-2 text-xl font-semibold">
                          {formatCurrency(account.balance || 0)}
                        </dd>
                      </div>
                      <div className="py-4">
                        <p className="text-sm text-gray-500">
                          More financial metrics will be displayed here as transactions are recorded.
                        </p>
                      </div>
                    </dl>
                  </CardContent>
                </Card>
              </div>
              <div className="flex justify-center my-4">
                <Button
                  onClick={() => setActiveTab('transactions')}
                  variant="outline"
                >
                  <List className="h-4 w-4 mr-2" />
                  View Transactions
                </Button>
              </div>
            </TabsContent>
            <TabsContent value="transactions">
              {isLoadingTransactions ? (
                <div className="flex justify-center items-center py-8">
                  <Spinner size="lg" />
                </div>
              ) : (
                <>
                  <div className="flex justify-between mb-4">
                    <h3 className="text-lg font-semibold">Transaction History</h3>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => navigate('/financial/transactions/create')}
                    >
                      <DollarSign className="h-4 w-4 mr-2" />
                      New Transaction
                    </Button>
                  </div>
                  {transactions.length === 0 ? (
                    <div className="text-center py-8 border rounded-lg bg-gray-50">
                      <Calendar className="mx-auto h-12 w-12 text-gray-400 mb-2" />
                      <h3 className="text-lg font-medium text-gray-900">No Transactions Found</h3>
                      <p className="mt-1 text-sm text-gray-500">
                        This account doesn't have any transactions yet.
                      </p>
                      <div className="mt-6">
                        <Button
                          onClick={() => navigate('/financial/transactions/create')}
                          size="sm"
                        >
                          <DollarSign className="h-4 w-4 mr-2" />
                          Create First Transaction
                        </Button>
                      </div>
                    </div>
                  ) : (
                    <Table>
                      <TableCaption>Transaction history for this account</TableCaption>
                      <TableHeader>
                        <TableRow>
                          <TableHead className="w-[100px]">Date</TableHead>
                          <TableHead>Description</TableHead>
                          <TableHead>Reference</TableHead>
                          <TableHead className="text-right">Debit</TableHead>
                          <TableHead className="text-right">Credit</TableHead>
                          <TableHead className="text-right">Running Balance</TableHead>
                        </TableRow>
                      </TableHeader>
                      <TableBody>
                        {transactions.map((transaction: Transaction) => (
                          <TableRow key={transaction.id}>
                            <TableCell>{formatDate(transaction.transaction_date)}</TableCell>
                            <TableCell>{transaction.description}</TableCell>
                            <TableCell>
                              {transaction.reference_type === 'loan' && transaction.loan_reference
                                ? transaction.loan_reference
                                : transaction.reference_type === 'collection' && transaction.collection_reference
                                ? transaction.collection_reference
                                : `${transaction.reference_type} #${transaction.reference_id}`
                              }
                            </TableCell>
                            <TableCell className="text-right">
                              {transaction.transaction_type === 'debit' ? formatCurrency(transaction.amount) : ''}
                            </TableCell>
                            <TableCell className="text-right">
                              {transaction.transaction_type === 'credit' ? formatCurrency(transaction.amount) : ''}
                            </TableCell>
                            <TableCell className="text-right">
                              {formatCurrency(0)} {/* Placeholder for running balance */}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  )}
                </>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
        <CardFooter className="border-t pt-6">
          <Button
            variant="outline"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Accounts
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
};

export default AccountDetailPage;