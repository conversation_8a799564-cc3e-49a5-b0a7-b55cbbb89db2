import React from 'react';
import { useAuth } from '@/lib/auth';
import { useBranches } from '@/lib/branches';
import BranchManager from '@/components/branch/BranchManager';
import { BranchSelector } from '@/components/branch/BranchSelector';
import { useTitle } from 'react-use';
import {
  Building,
  ArrowLeft,
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useLocation } from 'wouter';

export default function BranchesPage() {
  useTitle('Branch Management - TrackFina');
  const [location, navigate] = useLocation();
  const { getCurrentUser } = useAuth();
  const { branches, isLoading } = useBranches();
  const user = getCurrentUser();
  
  if (!user) {
    return null;
  }

  return (
    <div className="container mx-auto py-8">
      <div className="mb-8">
        <Button
          variant="ghost"
          className="mb-2"
          onClick={() => navigate('/dashboard')}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Dashboard
        </Button>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center">
              <Building className="mr-2 h-7 w-7" />
              Branch Management
            </h1>
            <p className="text-muted-foreground mt-1">
              Create and manage branches for your company
            </p>
          </div>
          <BranchSelector />
        </div>
      </div>

      <div className="space-y-6">
        <BranchManager />
      </div>
    </div>
  );
}