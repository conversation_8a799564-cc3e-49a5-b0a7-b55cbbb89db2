# Loan Module Bugs - Analysis and Development Plan

## Current Issues

### 1. Loan Edit Screen Missing Payment Frequency Fields
**Root Cause**: The payment frequency and terms frequency fields are properly implemented in various loan form components (SimpleStaticLoanForm, DirectLoanForm, QuickLoanForm), but they are not included in the Loan Edit page form.

**Detailed Analysis**:
- The edit.tsx page has a form that does not include payment_frequency and terms_frequency fields
- These fields are crucial for proper loan scheduling and calculations
- The form is only including basic fields like amount, interest_rate, term, etc.

### 2. Payment Schedules Error in Loan View Details Screen
**Root Cause**: The `generatePaymentSchedules` method is being called in the routes.ts file but is not implemented in the IStorage interface and subsequent DatabaseStorage class.

**Detailed Analysis**:
- Route handler at `/api/loans/:id/generate-schedules` calls `storage.generatePaymentSchedules`
- This method is not defined in the IStorage interface (unlike other methods such as getPaymentSchedulesByLoan)
- The method needs to be added to the interface and implemented in the DatabaseStorage class

### 3. Empty Customer Column in Loans List
**Root Cause**: The getLoansByCompany method in the storage.ts does not join the customers table to fetch customer information.

**Detailed Analysis**:
- The Loans interface includes an optional customer property with id and full_name
- In the Loans list table, the column renders `loan.customer?.full_name`
- The API endpoint for fetching loans only returns the basic loan information without the related customer data
- Need to modify the database query to include a JOIN with the customers table

## Development Plan

### 1. Fix Loan Edit Screen

1. **Update the Loan Edit form to include payment frequency fields**:
   - Add FormField components for payment_frequency and terms_frequency
   - Implement the same Select controls found in other loan form components
   - Ensure the form's defaultValues include these fields
   - Update the form submission to include these values

### 2. Implement Payment Schedules Generation

1. **Add generatePaymentSchedules method to IStorage interface**:
   ```typescript
   // In IStorage interface
   generatePaymentSchedules(loanId: number, companyId: number): Promise<PaymentSchedule[]>;
   ```

2. **Implement the method in DatabaseStorage class**:
   - Calculate payment schedules based on loan details (amount, interest_rate, term, payment_frequency)
   - Create schedule entries in the database
   - Return the created payment schedules

### 3. Fix Customer Column in Loans List

1. **Update getLoansByCompany method**:
   - Modify the query to include a JOIN with the customers table
   - Select necessary customer fields (id, full_name)
   - Return the joined data in the expected format with customer as a nested property

## Implementation Priority

1. **Customer Column Fix** - Highest priority as it affects visibility of existing data
2. **Payment Schedules Generation** - Critical for loan functionality
3. **Loan Edit Screen Fields** - Important for complete data entry

## Testing Plan

1. **Customer Column**:
   - Verify customer names appear in loans list after implementation
   - Ensure the customer names link to the correct customer profiles

2. **Payment Schedules**:
   - Test generate schedules functionality from loan details page
   - Verify schedules are created with correct dates and amounts
   - Test with different loan parameters (daily, weekly, monthly frequencies)

3. **Loan Edit Screen**:
   - Test form loading with existing values
   - Test saving changes to payment_frequency and terms_frequency
   - Verify these changes persist after save