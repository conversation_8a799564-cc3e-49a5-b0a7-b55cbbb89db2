import React from "react";
import { useLocation } from "wouter";
import { useCompany } from "@/lib/companies";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { DirectLoanForm } from "@/components/loan/DirectLoanForm";
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from "@/components/ui/card";

export default function CreateLoanPage() {
  const { currentCompany } = useCompany();
  const companyId = currentCompany?.company_id;
  const [, navigate] = useLocation();
  
  // Handle successful loan creation
  const handleLoanCreated = (loan: any) => {
    if (loan && loan.id) {
      // Navigate to the loans list page as requested
      navigate('/loans');
    }
  };
  
  return (
    <div className="container mx-auto py-6 max-w-4xl">
      <div className="flex justify-between items-center mb-6">
        <div className="flex items-center gap-4">
          <Button 
            variant="outline" 
            onClick={() => navigate("/loans")} 
            className="text-sm"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Loans
          </Button>
          <h1 className="text-2xl font-bold">
            Create New Loan
          </h1>
        </div>
      </div>
      
      {companyId ? (
        <DirectLoanForm 
          companyId={companyId}
          onSuccess={handleLoanCreated}
          onCancel={() => navigate("/loans")}
        />
      ) : (
        <Card>
          <CardHeader>
            <CardTitle>Company Required</CardTitle>
            <CardDescription>
              You need to select a company to create loans
            </CardDescription>
          </CardHeader>
          <CardContent>
            <p>Please select a company from the company selector in the top navigation.</p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}