import { IFinancialStorage } from './interfaces';
import { AccountStorage } from './account.storage';
import { TransactionStorage } from './transaction.storage';
import { ReportStorage } from './report.storage';

// Create a class that implements the IFinancialStorage interface by combining all financial storage implementations
class FinancialStorage implements IFinancialStorage {
  private accountStorage: AccountStorage;
  private transactionStorage: TransactionStorage;
  private reportStorage: ReportStorage;

  constructor() {
    this.accountStorage = new AccountStorage();
    this.transactionStorage = new TransactionStorage();
    this.reportStorage = new ReportStorage();
  }

  // Delegate methods to the appropriate storage implementation

  // Account methods
  getAccount = this.accountStorage.getAccount.bind(this.accountStorage);
  getAccountsByCompany = this.accountStorage.getAccountsByCompany.bind(this.accountStorage);
  createAccount = this.accountStorage.createAccount.bind(this.accountStorage);
  updateAccount = this.accountStorage.updateAccount.bind(this.accountStorage);
  deleteAccount = this.accountStorage.deleteAccount.bind(this.accountStorage);
  getAccountBalance = this.accountStorage.getAccountBalance.bind(this.accountStorage);
  updateAccountBalance = this.accountStorage.updateAccountBalance.bind(this.accountStorage);

  // Transaction methods
  getTransaction = this.transactionStorage.getTransaction.bind(this.transactionStorage);
  getTransactionsByCompany = this.transactionStorage.getTransactionsByCompany.bind(this.transactionStorage);
  getTransactionsByAccount = this.transactionStorage.getTransactionsByAccount.bind(this.transactionStorage);
  createTransaction = this.transactionStorage.createTransaction.bind(this.transactionStorage);
  updateTransaction = this.transactionStorage.updateTransaction.bind(this.transactionStorage);
  deleteTransaction = this.transactionStorage.deleteTransaction.bind(this.transactionStorage);
  getTransactionSummary = this.transactionStorage.getTransactionSummary.bind(this.transactionStorage);

  // Report methods
  getProfitLossReport = this.reportStorage.getProfitLossReport.bind(this.reportStorage);
  getCashFlowReport = this.reportStorage.getCashFlowReport.bind(this.reportStorage);
  getCollectionReport = this.reportStorage.getCollectionReport.bind(this.reportStorage);
}

// Create and export a singleton instance
export const financialStorage = new FinancialStorage();
