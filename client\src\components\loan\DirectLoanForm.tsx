import React, { useState, useEffect } from "react";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useAuth } from "@/lib/auth";

// UI Components
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CustomerCombobox } from "@/components/ui/customer-combobox";
import { Textarea } from "@/components/ui/textarea";
import { Spinner } from "@/components/ui/spinner";
import { LoadingOverlay } from "@/components/ui/loading-overlay";
import { format, addMonths } from "date-fns";

// Define type for loan form values
// LoanFormValues type will be inferred from the schema below

// Define schema for the loan form
const loanFormSchema = z.object({
  customer_id: z.number({
    required_error: "Customer is required",
  }),
  amount: z.number({
    required_error: "Loan amount is required",
  }).min(1, {
    message: "Amount must be greater than 0",
  }),
  interest_rate: z.number({
    required_error: "Interest rate is required",
  }).min(0, {
    message: "Interest rate must be non-negative",
  }),
  interest_type: z.enum(["flat", "reducing", "compound"], {
    required_error: "Interest type is required",
  }),
  loan_type: z.enum(["personal", "business", "education", "housing", "vehicle", "agriculture", "microfinance", "other"], {
    required_error: "Loan type is required",
  }),
  term: z.number({
    required_error: "Loan term is required",
  }).min(1, {
    message: "Term must be at least 1",
  }),
  terms_frequency: z.enum(["daily", "weekly", "biweekly", "monthly", "yearly"], {
    required_error: "Term frequency is required",
  }).default("monthly"),
  is_upfront_interest: z.boolean().default(true),
  start_date: z.string({
    required_error: "Start date is required",
  }),
  end_date: z.string({
    required_error: "End date is required",
  }),
  payment_frequency: z.enum(["daily", "weekly", "monthly"], {
    required_error: "Payment frequency is required",
  }),
  notes: z.string().optional().nullable(),
});

type LoanFormValues = z.infer<typeof loanFormSchema>;

type Customer = {
  id: number;
  full_name: string;
  company_id: number;
  customer_reference_code?: string;
  phone?: string;
  email?: string;
  // Other customer fields are optional for this component
};

interface DirectLoanFormProps {
  companyId: number | undefined;
  onSuccess?: (data: any) => void;
  onCancel?: () => void;
  initialData?: Partial<LoanFormValues>;
  isEdit?: boolean;
  loanId?: number;
}

export function DirectLoanForm({
  companyId,
  onSuccess,
  onCancel,
  initialData,
  isEdit = false,
  loanId,
}: DirectLoanFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();

  // Add loading state for better user feedback
  const [loadingState, setLoadingState] = useState({
    isLoading: false,
    message: '',
    progress: 0,
  });

  // Fetch customers for dropdown
  const { data: customers = [], isLoading: isLoadingCustomers } = useQuery<Customer[]>({
    queryKey: [`/api/companies/${companyId}/customers`],
    queryFn: async () => {
      if (!companyId) return [];

      console.log(`Fetching customers for company ${companyId}`);
      const response = await apiRequest('GET', `/api/companies/${companyId}/customers`);
      const data = await response.json();

      // Log the customers to verify they belong to the correct company
      console.log(`Received ${data.length} customers for company ${companyId}`);

      // Verify all customers belong to the correct company
      const filteredCustomers = data.filter((customer: Customer) => customer.company_id === companyId);

      if (filteredCustomers.length !== data.length) {
        console.warn(`Filtered out ${data.length - filteredCustomers.length} customers that don't belong to company ${companyId}`);
      }

      return filteredCustomers;
    },
    enabled: !!companyId,
  });

  // Calculate a default end date (one year from today)
  const defaultStartDate = new Date().toISOString().split('T')[0];
  const defaultEndDate = addMonths(new Date(), 12).toISOString().split('T')[0];

  // Process the initial data for backward compatibility
  const processedInitialData = React.useMemo(() => {
    if (!initialData) return {
      customer_id: undefined,
      amount: undefined,
      interest_rate: undefined,
      interest_type: "flat",
      loan_type: "personal",
      term: 12,
      terms_frequency: "monthly",
      is_upfront_interest: true,
      start_date: defaultStartDate,
      end_date: defaultEndDate,
      payment_frequency: "monthly",
      notes: ""
    };

    // Create a typed object with proper backward compatibility
    return {
      ...initialData,
      // Using term field consistently
      customer_id: initialData.customer_id,
      amount: initialData.amount,
      interest_rate: initialData.interest_rate,
      interest_type: initialData.interest_type || "flat",
      loan_type: initialData.loan_type || "personal",
      term: initialData.term || 12,
      terms_frequency: initialData.terms_frequency || "monthly",
      is_upfront_interest: initialData.is_upfront_interest !== undefined ?
                           initialData.is_upfront_interest : true,
      start_date: initialData.start_date || defaultStartDate,
      end_date: initialData.end_date || defaultEndDate,
      payment_frequency: initialData.payment_frequency || "monthly",
      notes: initialData.notes || ""
    };
  }, [initialData, defaultStartDate, defaultEndDate]);

  // Set up form with default values
  const form = useForm<LoanFormValues>({
    resolver: zodResolver(loanFormSchema),
    defaultValues: {
      customer_id: processedInitialData.customer_id || undefined,
      amount: processedInitialData.amount || undefined,
      interest_rate: processedInitialData.interest_rate || undefined,
      interest_type: processedInitialData.interest_type || "flat",
      loan_type: processedInitialData.loan_type || "personal",
      term: processedInitialData.term || 12,
      terms_frequency: processedInitialData.terms_frequency || "monthly",
      is_upfront_interest: processedInitialData.is_upfront_interest !== undefined ?
                           processedInitialData.is_upfront_interest : true,
      start_date: processedInitialData.start_date || defaultStartDate,
      end_date: processedInitialData.end_date || defaultEndDate,
      payment_frequency: processedInitialData.payment_frequency || "monthly",
      notes: processedInitialData.notes || "",
    },
  });

  // Create or Update mutation
  const { mutate, isPending } = useMutation({
    mutationFn: async (data: LoanFormValues) => {
      // Set loading state at the beginning
      setLoadingState({
        isLoading: true,
        message: isEdit ? 'Updating loan...' : 'Creating loan...',
        progress: 10,
      });

      // Convert amount and interest_rate to strings as required by the backend
      // Calculate the disbursed amount based on upfront interest
      const interestAmount = data.amount * (data.interest_rate / 100);
      const disbursedAmount = data.is_upfront_interest
        ? data.amount - interestAmount
        : data.amount;
      const totalRepayable = data.is_upfront_interest
        ? data.amount
        : data.amount + interestAmount;
      const installmentAmount = totalRepayable / data.term;

      if (!companyId) {
        throw new Error("Company ID is required to create a loan");
      }

      // Update loading state before API call
      setLoadingState(prev => ({
        ...prev,
        message: isEdit ? 'Saving loan changes...' : 'Saving loan details...',
        progress: 30,
      }));

      const formattedData = {
        ...data,
        company_id: companyId,
        amount: data.amount.toString(),
        interest_rate: data.interest_rate.toString(),
        disbursed_amount: disbursedAmount.toString(),
        total_repayable: totalRepayable.toString(),
        installment_amount: installmentAmount.toString(),
      };

      let response;
      if (isEdit && loanId) {
        response = await apiRequest('PATCH', `/api/loans/${loanId}`, formattedData);
      } else {
        response = await apiRequest('POST', '/api/loans', formattedData);
      }

      // Update loading state for payment schedules generation
      setLoadingState(prev => ({
        ...prev,
        message: 'Generating payment schedules...',
        progress: 70,
      }));

      return response;
    },
    onSuccess: (data) => {
      // Update loading before final steps
      setLoadingState(prev => ({
        ...prev,
        message: 'Finalizing...',
        progress: 90,
      }));

      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/loans`] });

      // Clear loading state
      setTimeout(() => {
        setLoadingState({
          isLoading: false,
          message: '',
          progress: 0,
        });

        toast({
          title: isEdit ? "Loan updated" : "Loan created",
          description: isEdit
            ? "The loan has been updated successfully"
            : "New loan has been created successfully",
        });

        if (onSuccess) onSuccess(data);
      }, 500); // Small delay to ensure the user sees the "Finalizing" message
    },
    onError: (error: any) => {
      // Clear loading state on error
      setLoadingState({
        isLoading: false,
        message: '',
        progress: 0,
      });

      toast({
        title: "Error",
        description: error.message || "Something went wrong",
        variant: "destructive",
      });
    },
  });

  // Handle form submission
  const onSubmit = (values: LoanFormValues) => {
    // Ensure terms_frequency matches payment_frequency
    const formData = {
      ...values,
      terms_frequency: values.payment_frequency
    };
    mutate(formData);
  };

  // Calculate end date when term or start date changes
  const selectedStartDate = form.watch("start_date");
  const selectedTerm = form.watch("term");
  const selectedTermsFrequency = form.watch("terms_frequency");

  useEffect(() => {
    if (selectedStartDate && selectedTerm) {
      try {
        const startDate = new Date(selectedStartDate);
        let endDate;

        // Calculate end date based on terms frequency
        switch(selectedTermsFrequency) {
          case 'daily':
            // Add days
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + selectedTerm);
            break;
          case 'weekly':
            // Add weeks
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + (selectedTerm * 7));
            break;
          case 'biweekly':
            // Add biweekly periods
            endDate = new Date(startDate);
            endDate.setDate(startDate.getDate() + (selectedTerm * 14));
            break;
          case 'yearly':
            // Add years
            endDate = new Date(startDate);
            endDate.setFullYear(startDate.getFullYear() + selectedTerm);
            break;
          case 'monthly':
          default:
            // Add months
            endDate = addMonths(startDate, selectedTerm);
        }

        form.setValue("end_date", endDate.toISOString().split('T')[0]);
      } catch (error) {
        console.error("Error calculating end date:", error);
      }
    }
  }, [selectedStartDate, selectedTerm, selectedTermsFrequency, form]);

  if (isLoadingCustomers) {
    return (
      <div className="flex justify-center py-6">
        <Spinner size="lg" />
        <span className="ml-2">Loading customers...</span>
      </div>
    );
  }

  return (
    <>
      {/* Add the loading overlay */}
      <LoadingOverlay
        isLoading={loadingState.isLoading}
        message={loadingState.message}
        progress={loadingState.progress}
        showProgress={true}
      />

      <Card className="w-full">
        <CardHeader>
          <CardTitle>{isEdit ? "Edit Loan" : "Create New Loan"}</CardTitle>
          <CardDescription>
            {isEdit
              ? "Update the information for this loan"
              : "Enter the loan details to create a new loan"}
          </CardDescription>
        </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Customer Selection */}
              <FormField
                control={form.control}
                name="customer_id"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Customer</FormLabel>
                    <FormControl>
                      <CustomerCombobox
                        customers={customers}
                        value={field.value}
                        onValueChange={field.onChange}
                        placeholder="Select customer..."
                        searchPlaceholder="Search customers by name, Customer ID, phone, or email..."
                        emptyText="No customers found."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Loan Type */}
              <FormField
                control={form.control}
                name="loan_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Loan Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select loan type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="personal">Personal</SelectItem>
                        <SelectItem value="business">Business</SelectItem>
                        <SelectItem value="education">Education</SelectItem>
                        <SelectItem value="housing">Housing</SelectItem>
                        <SelectItem value="vehicle">Vehicle</SelectItem>
                        <SelectItem value="agriculture">Agriculture</SelectItem>
                        <SelectItem value="microfinance">Microfinance</SelectItem>
                        <SelectItem value="other">Other</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Loan Amount */}
              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Loan Amount</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter loan amount"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interest Rate */}
              <FormField
                control={form.control}
                name="interest_rate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Interest Rate (%)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter interest rate"
                        {...field}
                        onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Interest Type */}
              <FormField
                control={form.control}
                name="interest_type"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Interest Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select interest type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="flat">Flat</SelectItem>
                        <SelectItem value="reducing">Reducing</SelectItem>
                        <SelectItem value="compound">Compound</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Term */}
              <FormField
                control={form.control}
                name="term"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Term</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        placeholder="Enter term value"
                        {...field}
                        onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Term Frequency */}
              <FormField
                control={form.control}
                name="terms_frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Term Frequency</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select term frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Biweekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                        <SelectItem value="yearly">Yearly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Is Upfront Interest */}
              <FormField
                control={form.control}
                name="is_upfront_interest"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                    <FormControl>
                      <input
                        type="checkbox"
                        checked={field.value}
                        onChange={field.onChange}
                        className="h-4 w-4 rounded border-gray-300"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel className="font-medium">
                        Deduct Interest Upfront
                      </FormLabel>
                      <p className="text-xs text-muted-foreground">
                        If checked, interest will be deducted from the principal amount before disbursement
                      </p>
                    </div>
                  </FormItem>
                )}
              />

              {/* Payment Frequency */}
              <FormField
                control={form.control}
                name="payment_frequency"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Payment Frequency</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select payment frequency" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="daily">Daily</SelectItem>
                        <SelectItem value="weekly">Weekly</SelectItem>
                        <SelectItem value="biweekly">Biweekly</SelectItem>
                        <SelectItem value="monthly">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Start Date */}
              <FormField
                control={form.control}
                name="start_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">Start Date</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* End Date */}
              <FormField
                control={form.control}
                name="end_date"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="font-medium">End Date</FormLabel>
                    <FormControl>
                      <Input
                        type="date"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Notes */}
            <FormField
              control={form.control}
              name="notes"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium">Notes</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter any additional notes about this loan"
                      className="resize-none min-h-[100px]"
                      {...field}
                      value={field.value || ""}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-2">
              {onCancel && (
                <Button variant="outline" type="button" onClick={onCancel}>
                  Cancel
                </Button>
              )}
              <Button type="submit" disabled={isPending}>
                {isPending && <Spinner className="mr-2 h-4 w-4" />}
                {isEdit ? "Update Loan" : "Create Loan"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
    </>
  );
}