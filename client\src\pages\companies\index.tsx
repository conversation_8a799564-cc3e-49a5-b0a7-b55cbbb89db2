import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Plus, Pencil, Trash2, Eye, Search, Building2, Users, DollarSign } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation } from "@tanstack/react-query";
import { queryClient, apiRequest } from "@/lib/queryClient";
import { useAuth } from "@/lib/auth";
import { Link } from "wouter";
import * as z from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";

// Define country code constant
const COUNTRY_CODE = '+91';

// Company validation schema
const companySchema = z.object({
  name: z.string()
    .min(1, { message: "Company name is required" })
    .min(2, { message: "Company name must be at least 2 characters" }),
  email: z.string()
    .min(1, { message: "Email is required" })
    .refine(
      (value) => {
        // Email regex pattern for validation
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailPattern.test(value);
      },
      { message: "Please enter a valid email address" }
    ),
  phone: z.string()
    .min(1, { message: "Phone number is required" })
    .refine(
      (value) => {
        // Accept only format with country code followed by exactly 10 digits
        const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
        const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
        return pattern.test(value);
      },
      { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
    ),
  address: z.string()
    .min(1, { message: "Address is required" })
    .min(5, { message: "Address must be at least 5 characters" })
});

type CompanyFormData = z.infer<typeof companySchema>;

// Company type interface
interface Company {
  id: number;
  name: string;
  email?: string;
  contact_email?: string;
  phone?: string;
  contact_phone?: string;
  address?: string;
  website?: string;
  logo?: string;
  active: boolean;
  subscription_status?: string;
  created_at: string;
  updated_at: string;
}

export default function Companies() {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState("");
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentCompany, setCurrentCompany] = useState<Company | null>(null);

  // Get auth functions
  const { isAuthenticated } = useAuth();

  // Fetch companies from API
  const {
    data: companies = []
  } = useQuery<Company[]>({
    queryKey: ['/api/companies'],
    enabled: isAuthenticated(),
  });

  // Create form
  const createForm = useForm<CompanyFormData>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
    }
  });

  // Edit form
  const editForm = useForm<CompanyFormData>({
    resolver: zodResolver(companySchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
    }
  });

  // Handle search with safeguards for potentially undefined fields
  const filteredCompanies = companies.filter(company => {
    const query = searchQuery.toLowerCase();
    return (
      company.name.toLowerCase().includes(query) ||
      (company.email?.toLowerCase().includes(query) || false) ||
      (company.contact_email?.toLowerCase().includes(query) || false)
    );
  });

  // Open create dialog with empty form
  const handleOpenCreateDialog = () => {
    createForm.reset({
      name: "",
      email: "",
      phone: "",
      address: "",
    });
    setIsCreateDialogOpen(true);
  };

  // Open edit dialog with company data
  const handleOpenEditDialog = (company: any) => {
    setCurrentCompany(company);

    // Format phone number to ensure it has country code
    let phoneNumber = company.phone || "";
    if (phoneNumber && !phoneNumber.startsWith(COUNTRY_CODE)) {
      phoneNumber = `${COUNTRY_CODE}${phoneNumber}`;
    }

    editForm.reset({
      name: company.name || "",
      email: company.email || "",
      phone: phoneNumber,
      address: company.address || "",
    });
    setIsEditDialogOpen(true);
  };

  // Open delete confirmation dialog
  const handleOpenDeleteDialog = (company: any) => {
    setCurrentCompany(company);
    setIsDeleteDialogOpen(true);
  };

  // Create company mutation
  const createCompanyMutation = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest("POST", "/api/companies", data);
      return response.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsCreateDialogOpen(false);
      createForm.reset();
      toast({
        title: "Company created",
        description: `${variables.name} has been created successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error creating company",
        description: error.message || "Failed to create company",
        variant: "destructive",
      });
    }
  });

  // Create new company
  const handleCreateCompany = (data: CompanyFormData) => {
    // Manual validation for required fields
    let hasError = false;

    // Validate name field (required)
    if (!data.name || data.name.trim() === '') {
      createForm.setError("name", {
        type: "manual",
        message: "Company name is required"
      });
      hasError = true;
    }

    // Validate email field (required)
    if (!data.email || data.email.trim() === '') {
      createForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
      hasError = true;
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(data.email)) {
        createForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
        hasError = true;
      }
    }

    // Validate phone field (required)
    if (!data.phone || data.phone.trim() === '') {
      createForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
      hasError = true;
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(data.phone)) {
        createForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
        hasError = true;
      }
    }

    // Validate address field (required)
    if (!data.address || data.address.trim() === '') {
      createForm.setError("address", {
        type: "manual",
        message: "Address is required"
      });
      hasError = true;
    }

    // If there are validation errors, don't submit
    if (hasError) {
      return;
    }

    // Prepare the data for API
    const companyData = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      address: data.address,
      active: true
    };

    // Call mutation to create company
    createCompanyMutation.mutate(companyData);
  };

  // Update company mutation
  const updateCompanyMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number, data: any }) => {
      const response = await apiRequest("PATCH", `/api/companies/${id}`, data);
      return response.json();
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: "Company updated",
        description: `${variables.data.name} has been updated successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error updating company",
        description: error.message || "Failed to update company",
        variant: "destructive",
      });
    }
  });

  // Update existing company
  const handleUpdateCompany = (data: CompanyFormData) => {
    if (!currentCompany) return;

    // Manual validation for required fields
    let hasError = false;

    // Validate name field (required)
    if (!data.name || data.name.trim() === '') {
      editForm.setError("name", {
        type: "manual",
        message: "Company name is required"
      });
      hasError = true;
    }

    // Validate email field (required)
    if (!data.email || data.email.trim() === '') {
      editForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
      hasError = true;
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(data.email)) {
        editForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
        hasError = true;
      }
    }

    // Validate phone field (required)
    if (!data.phone || data.phone.trim() === '') {
      editForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
      hasError = true;
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(data.phone)) {
        editForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
        hasError = true;
      }
    }

    // Validate address field (required)
    if (!data.address || data.address.trim() === '') {
      editForm.setError("address", {
        type: "manual",
        message: "Address is required"
      });
      hasError = true;
    }

    // If there are validation errors, don't submit
    if (hasError) {
      return;
    }

    // Prepare the data for API
    const companyData = {
      name: data.name,
      email: data.email,
      phone: data.phone,
      address: data.address,
      active: true
    };

    // Call mutation to update company
    updateCompanyMutation.mutate({
      id: currentCompany.id,
      data: companyData
    });
  };

  // Delete company mutation
  const deleteCompanyMutation = useMutation({
    mutationFn: async (id: number) => {
      const response = await apiRequest("DELETE", `/api/companies/${id}`, null);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/companies'] });
      setIsDeleteDialogOpen(false);
      toast({
        title: "Company deleted",
        description: `${currentCompany?.name} has been deleted successfully.`,
      });
    },
    onError: (error: any) => {
      toast({
        title: "Error deleting company",
        description: error.message || "Failed to delete company",
        variant: "destructive",
      });
    }
  });

  // Delete company
  const handleDeleteCompany = () => {
    if (!currentCompany) return;

    // Call mutation to delete company
    deleteCompanyMutation.mutate(currentCompany.id);
  };

  // Format date for display
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Companies</h1>

        <Button onClick={handleOpenCreateDialog} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          Add Company
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="text-xl">Company Management</CardTitle>
          <CardDescription>
            View and manage all the companies in your SaaS platform.
          </CardDescription>

          <div className="relative">
            <Search className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-500" />
            <Input
              placeholder="Search companies by name or email..."
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </CardHeader>

        <CardContent>
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>ID</TableHead>
                  <TableHead>Company Name</TableHead>
                  <TableHead>Email</TableHead>
                  <TableHead>Phone</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>

              <TableBody>
                {filteredCompanies.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="text-center h-24 text-gray-500">
                      No companies found matching your search.
                    </TableCell>
                  </TableRow>
                ) : (
                  filteredCompanies.map((company) => (
                    <TableRow key={company.id}>
                      <TableCell className="font-medium">{company.id}</TableCell>
                      <TableCell>{company.name}</TableCell>
                      <TableCell>{company.email}</TableCell>
                      <TableCell>{company.phone}</TableCell>
                      <TableCell>
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          company.subscription_status === 'active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-yellow-100 text-yellow-800'
                        }`}>
                          {company.subscription_status}
                        </span>
                      </TableCell>
                      <TableCell>{formatDate(company.created_at)}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-2">
                          <Link to={`/companies/${company.id}`}>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-blue-600"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                          </Link>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-amber-600"
                            onClick={() => handleOpenEditDialog(company)}
                          >
                            <Pencil className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="icon"
                            className="h-8 w-8 text-red-600"
                            onClick={() => handleOpenDeleteDialog(company)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>

        <CardFooter className="flex justify-between border-t p-4">
          <div className="text-sm text-gray-500">
            Showing {filteredCompanies.length} of {companies.length} companies
          </div>
        </CardFooter>
      </Card>

      {/* Company Analytics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Companies</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Building2 className="h-6 w-6 mr-2 text-blue-600" />
              <span className="text-2xl font-bold">{companies.length}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Active Subscriptions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <DollarSign className="h-6 w-6 mr-2 text-green-600" />
              <span className="text-2xl font-bold">
                {companies.filter(c => c.subscription_status === 'active').length}
              </span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-gray-500">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center">
              <Users className="h-6 w-6 mr-2 text-purple-600" />
              <span className="text-2xl font-bold">0</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Create Company Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Company</DialogTitle>
            <DialogDescription>
              Add a new company to your SaaS platform.
            </DialogDescription>
          </DialogHeader>

          <Form {...createForm}>
            <form onSubmit={createForm.handleSubmit(handleCreateCompany)} className="space-y-4 py-4">
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter company name"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            createForm.setError("name", {
                              type: "manual",
                              message: "Company name is required"
                            });
                          } else if (value.length < 2) {
                            createForm.setError("name", {
                              type: "manual",
                              message: "Company name must be at least 2 characters"
                            });
                          } else {
                            createForm.clearErrors("name");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            createForm.setError("email", {
                              type: "manual",
                              message: "Email is required"
                            });
                          } else {
                            const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                            if (!emailPattern.test(value)) {
                              createForm.setError("email", {
                                type: "manual",
                                message: "Please enter a valid email address"
                              });
                            } else {
                              createForm.clearErrors("email");
                            }
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <div className="flex w-full">
                        <span className="flex items-center justify-center px-3 py-2 border border-r-0 border-input bg-muted text-sm rounded-l-md whitespace-nowrap">
                          {COUNTRY_CODE}
                        </span>
                        <Input
                          placeholder="Enter 10-digit phone number"
                          className="rounded-l-none border-l-0 flex-1"
                          value={field.value ? field.value.replace(COUNTRY_CODE, '') : ''}
                          onChange={(e) => {
                            // Remove non-digit characters
                            const digitsOnly = e.target.value.replace(/\D/g, '');

                            // Trim to 10 digits max
                            const trimmed = digitsOnly.substring(0, 10);

                            // Update form value with country code prefix
                            field.onChange(`${COUNTRY_CODE}${trimmed}`);

                            // Real-time validation
                            if (!trimmed || trimmed.length === 0) {
                              createForm.setError("phone", {
                                type: "manual",
                                message: "Phone number is required"
                              });
                            } else if (trimmed.length < 10) {
                              createForm.setError("phone", {
                                type: "manual",
                                message: `Phone number must be exactly 10 digits (currently ${trimmed.length})`
                              });
                            } else if (trimmed.length === 10) {
                              createForm.clearErrors("phone");
                            }
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={createForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="123 Business Street, City, State"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            createForm.setError("address", {
                              type: "manual",
                              message: "Address is required"
                            });
                          } else if (value.length < 5) {
                            createForm.setError("address", {
                              type: "manual",
                              message: "Address must be at least 5 characters"
                            });
                          } else {
                            createForm.clearErrors("address");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Create Company
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Company Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Company</DialogTitle>
            <DialogDescription>
              Update the company information.
            </DialogDescription>
          </DialogHeader>

          <Form {...editForm}>
            <form onSubmit={editForm.handleSubmit(handleUpdateCompany)} className="space-y-4 py-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter company name"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            editForm.setError("name", {
                              type: "manual",
                              message: "Company name is required"
                            });
                          } else if (value.length < 2) {
                            editForm.setError("name", {
                              type: "manual",
                              message: "Company name must be at least 2 characters"
                            });
                          } else {
                            editForm.clearErrors("name");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            editForm.setError("email", {
                              type: "manual",
                              message: "Email is required"
                            });
                          } else {
                            const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                            if (!emailPattern.test(value)) {
                              editForm.setError("email", {
                                type: "manual",
                                message: "Please enter a valid email address"
                              });
                            } else {
                              editForm.clearErrors("email");
                            }
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <div className="flex w-full">
                        <span className="flex items-center justify-center px-3 py-2 border border-r-0 border-input bg-muted text-sm rounded-l-md whitespace-nowrap">
                          {COUNTRY_CODE}
                        </span>
                        <Input
                          placeholder="Enter 10-digit phone number"
                          className="rounded-l-none border-l-0 flex-1"
                          value={field.value ? field.value.replace(COUNTRY_CODE, '') : ''}
                          onChange={(e) => {
                            // Remove non-digit characters
                            const digitsOnly = e.target.value.replace(/\D/g, '');

                            // Trim to 10 digits max
                            const trimmed = digitsOnly.substring(0, 10);

                            // Update form value with country code prefix
                            field.onChange(`${COUNTRY_CODE}${trimmed}`);

                            // Real-time validation
                            if (!trimmed || trimmed.length === 0) {
                              editForm.setError("phone", {
                                type: "manual",
                                message: "Phone number is required"
                              });
                            } else if (trimmed.length < 10) {
                              editForm.setError("phone", {
                                type: "manual",
                                message: `Phone number must be exactly 10 digits (currently ${trimmed.length})`
                              });
                            } else if (trimmed.length === 10) {
                              editForm.clearErrors("phone");
                            }
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={editForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="123 Business Street, City, State"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            editForm.setError("address", {
                              type: "manual",
                              message: "Address is required"
                            });
                          } else if (value.length < 5) {
                            editForm.setError("address", {
                              type: "manual",
                              message: "Address must be at least 5 characters"
                            });
                          } else {
                            editForm.clearErrors("address");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <DialogFooter>
                <Button type="button" variant="outline" onClick={() => setIsEditDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">
                  Update Company
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {currentCompany?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>

          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button type="button" variant="destructive" onClick={handleDeleteCompany}>
              Delete Company
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}