# TrackFina - Loan Management System

TrackFina is a comprehensive SaaS loan management platform designed for financial institutions. It offers a flexible, intuitive interface for loan workflows with advanced customer search and management capabilities.

## Project Overview

TrackFina enables financial institutions to efficiently create, view, manage, and process loans through an intuitive interface that works across all device types. The system uses a modular architecture with React on the frontend and Node.js with PostgreSQL on the backend.

## Core Features

### Multi-tenant Architecture
- Support for multiple companies and branches
- User role-based access control
- Company/branch switching capabilities

### Loan Management
- Create, view, edit, and delete loans
- Loan form templates with customizable fields
- Loan configurations for different types of loans
- Data integrity enforcement for related entities

### Collections Management
- Track and manage loan collections
- EMI payment scheduling and management
- Payment processing with receipts
- Amortization scheduling with visual indicators

### Customer Management
- Customer information storage and retrieval
- Advanced search and filtering capabilities
- Customer loan history tracking

### Branch Management
- Create and manage company branches
- Assign loans and customers to specific branches
- Branch-specific reporting

## User Stories

### Loan Template Management

**As a** company administrator,  
**I want** to create and manage loan templates,  
**So that** I can standardize the loan creation process for my organization.

**Acceptance Criteria:**
- Create loan templates with custom fields
- Edit existing loan templates
- Delete templates that are not in use
- View a list of all available templates
- Toggle template active status
- Prevent deletion of templates that are in use by existing loans

### Loan Configuration Management

**As a** finance manager,  
**I want** to configure which loan templates are available for loan creation,  
**So that** only approved loan types are offered to loan officers.

**Acceptance Criteria:**
- Create configurations that link templates to the company
- Toggle configuration active status to control availability
- Delete configurations when no longer needed
- Prevent deletion of configurations that are in use by existing loans
- View a list of all available configurations

### Loan Creation

**As a** loan officer,  
**I want** to create new loans using standardized templates,  
**So that** I can efficiently process loan applications with consistent data.

**Acceptance Criteria:**
- Select from available loan templates
- Enter loan details in a structured form
- Calculate loan terms based on entered values
- Preview payment schedule before submission
- Save loan data securely to the database

### Collections Management

**As a** collections manager,  
**I want** to track and manage loan payments and collections,  
**So that** I can ensure timely repayment and identify delinquent accounts.

**Acceptance Criteria:**
- View payment schedules for all loans
- Track payment status (due, paid, overdue)
- Process payments with receipts
- Visualize collection status with color-coded indicators
- Generate reports on collection performance

### Data Integrity Protection

**As a** system administrator,  
**I want** to ensure data integrity across the system,  
**So that** critical relationships between entities are maintained.

**Acceptance Criteria:**
- Prevent deletion of templates in use by existing loans
- Prevent deletion of configurations in use by existing loans
- Display meaningful error messages when deletion is prevented
- Maintain referential integrity across the database

## Technical Implementation

### Frontend
- React.js with TypeScript
- shadcn/ui component library
- React Hook Form for form handling
- TanStack Query for data fetching
- Responsive design with Tailwind CSS

### Backend
- Node.js with Express
- TypeScript for type safety
- PostgreSQL database
- Drizzle ORM for database operations
- JWT authentication

### Data Model
- Companies and branches
- Users with role-based permissions
- Customers with loan history
- Loan templates and configurations
- Loans with standardized fields
- Form submissions with dynamic fields
- Payment schedules and transactions

## Recent Implementations

### Data Integrity Enhancement
- Added checks to prevent deletion of templates and configurations in use
- Implemented `isFormTemplateInUse` and `isLoanConfigurationInUse` checks in storage layer
- Updated API endpoints to return specific error codes (template_in_use, configuration_in_use)
- Enhanced frontend error handling with user-friendly messages
- Added confirmation dialogs for destructive operations

### UI Improvements
- Added delete button for loan configurations
- Implemented confirmation dialogs with warning messages
- Enhanced status indicators with color coding
- Improved error message display
- Mobile-responsive layout for all screens

## Getting Started

### Prerequisites
- Node.js v20 or higher
- PostgreSQL database

### Installation
1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables
4. Run database migrations: `npm run db:push`
5. Start the development server: `npm run dev`

## License
All rights reserved. This software is proprietary.