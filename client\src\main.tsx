import { createRoot } from "react-dom/client";
import App from "./App";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { queryClient } from "@/lib/queryClient";
import { ThemeProvider } from "@/components/ui/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import ErrorBoundary from "@/components/ErrorBoundary";
import setupGlobalErrorHandlers from "@/lib/globalErrorHandler";
import errorLogger from "@/lib/errorLogger";

// Initialize global error handlers
setupGlobalErrorHandlers();

// Log application startup
errorLogger.info('Application starting', 'main');

// Expose queryClient globally for debugging
if (typeof window !== 'undefined') {
  (window as any).queryClient = queryClient;
}

createRoot(document.getElementById("root")!).render(
  <ThemeProvider defaultTheme="light" storageKey="trackfina-theme">
    <QueryClientProvider client={queryClient}>
      <ErrorBoundary source="app-root">
        <App />
      </ErrorBoundary>
      <Toaster />
    </QueryClientProvider>
  </ThemeProvider>
);
