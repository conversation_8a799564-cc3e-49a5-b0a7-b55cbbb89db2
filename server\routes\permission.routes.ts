import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, requirePermission, AuthRequest } from '../middleware/auth';
import { permissions, insertPermissionSchema } from '../../shared/schema';
import { eq } from 'drizzle-orm';
import { db } from '../db';

export function registerPermissionRoutes(app: Express): void {
  // Get all permissions - allow any user for demo purposes
  app.get('/api/permissions', async (req: AuthRequest, res: Response) => {
    try {
      const allPermissions = await db.select().from(permissions);
      return res.json(allPermissions);
    } catch (error) {
      console.error('Error fetching permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get permission by ID
  app.get('/api/permissions/:id', authMiddleware, requirePermission('permission_view'), async (req: AuthRequest, res: Response) => {
    try {
      const permissionId = parseInt(req.params.id);
      const [permission] = await db
        .select()
        .from(permissions)
        .where(eq(permissions.id, permissionId));

      if (!permission) {
        return res.status(404).json({ message: 'Permission not found' });
      }

      return res.json(permission);
    } catch (error) {
      console.error(`Error fetching permission ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create permission (admin only)
  app.post('/api/permissions', authMiddleware, requirePermission('permission_assign'), async (req: AuthRequest, res: Response) => {
    try {
      const result = insertPermissionSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if permission code already exists
      const [existingPermission] = await db
        .select()
        .from(permissions)
        .where(eq(permissions.code, result.data.code));

      if (existingPermission) {
        return res.status(400).json({ message: 'Permission code already exists' });
      }

      const [permission] = await db
        .insert(permissions)
        .values(result.data)
        .returning();

      return res.status(201).json(permission);
    } catch (error) {
      console.error('Error creating permission:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update permission (admin only)
  app.put('/api/permissions/:id', authMiddleware, requirePermission('permission_assign'), async (req: AuthRequest, res: Response) => {
    try {
      const permissionId = parseInt(req.params.id);
      const result = insertPermissionSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if permission exists
      const [existingPermission] = await db
        .select()
        .from(permissions)
        .where(eq(permissions.id, permissionId));

      if (!existingPermission) {
        return res.status(404).json({ message: 'Permission not found' });
      }

      // If code is being updated, check if it already exists
      if (result.data.code && result.data.code !== existingPermission.code) {
        const [duplicateCode] = await db
          .select()
          .from(permissions)
          .where(eq(permissions.code, result.data.code));

        if (duplicateCode) {
          return res.status(400).json({ message: 'Permission code already exists' });
        }
      }

      const [updatedPermission] = await db
        .update(permissions)
        .set(result.data)
        .where(eq(permissions.id, permissionId))
        .returning();

      return res.json(updatedPermission);
    } catch (error) {
      console.error(`Error updating permission ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete permission (admin only)
  app.delete('/api/permissions/:id', authMiddleware, requirePermission('permission_assign'), async (req: AuthRequest, res: Response) => {
    try {
      const permissionId = parseInt(req.params.id);

      // Check if permission exists
      const [existingPermission] = await db
        .select()
        .from(permissions)
        .where(eq(permissions.id, permissionId));

      if (!existingPermission) {
        return res.status(404).json({ message: 'Permission not found' });
      }

      await db
        .delete(permissions)
        .where(eq(permissions.id, permissionId));

      return res.json({ message: 'Permission deleted successfully' });
    } catch (error) {
      console.error(`Error deleting permission ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
