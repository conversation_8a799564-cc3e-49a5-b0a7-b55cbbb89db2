import { db } from '@/server/db';
import { companySettings, companySettingsSchema } from '@/shared/schema';
import { eq } from 'drizzle-orm';
import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { validateRequest } from '@/server/auth';

// GET /api/companies/:id/settings
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await validateRequest();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const companyId = parseInt(params.id);

    // Check if user has access to this company
    if (user.company_id !== companyId && user.role !== 'saas_admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Get company settings
    const settings = await db.query.companySettings.findFirst({
      where: eq(companySettings.company_id, companyId),
    });

    // If settings don't exist, return default values
    if (!settings) {
      return NextResponse.json({
        company_id: companyId,
        date_format: 'dd-MM-yyyy',
        currency_symbol: '₹',
      });
    }

    return NextResponse.json(settings);
  } catch (error) {
    console.error('Error fetching company settings:', error);
    return NextResponse.json(
      { error: 'Failed to fetch company settings' },
      { status: 500 }
    );
  }
}

// PUT /api/companies/:id/settings
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { user } = await validateRequest();
    if (!user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const companyId = parseInt(params.id);

    // Check if user has access to this company
    if (user.company_id !== companyId && user.role !== 'saas_admin') {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Validate request body
    const body = await request.json();
    const validatedData = companySettingsSchema.parse({
      ...body,
      company_id: companyId,
    });

    // Check if settings already exist
    const existingSettings = await db.query.companySettings.findFirst({
      where: eq(companySettings.company_id, companyId),
    });

    let result;

    if (existingSettings) {
      // Update existing settings
      result = await db
        .update(companySettings)
        .set({
          date_format: validatedData.date_format,
          currency_symbol: validatedData.currency_symbol,
          updated_at: new Date(),
        })
        .where(eq(companySettings.company_id, companyId))
        .returning();
    } else {
      // Create new settings
      result = await db
        .insert(companySettings)
        .values({
          company_id: companyId,
          date_format: validatedData.date_format,
          currency_symbol: validatedData.currency_symbol,
        })
        .returning();
    }

    return NextResponse.json(result[0]);
  } catch (error) {
    console.error('Error updating company settings:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Failed to update company settings' },
      { status: 500 }
    );
  }
}
