import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, requirePermission, requireAnyPermission, AuthRequest } from '../middleware/auth';
import {
  groups, insertGroupSchema,
  groupUsers, insertGroupUserSchema,
  groupRoles, insertGroupRoleSchema,
  users, customRoles
} from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';

export function registerGroupManagementRoutes(app: Express): void {
  // Get all groups with members and roles
  app.get('/api/group-management/groups', authMiddleware, requirePermission('group_view'), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : null;
      const branchId = req.query.branch_id ? parseInt(req.query.branch_id as string) : null;

      let query = db.select().from(groups);

      // Apply filters
      if (companyId) {
        query = query.where(eq(groups.company_id, companyId));
      }

      if (branchId) {
        query = query.where(eq(groups.branch_id, branchId));
      }

      const allGroups = await query;

      // For each group, get members and roles
      const groupsWithDetails = await Promise.all(allGroups.map(async (group) => {
        const members = await getGroupMembers(group.id);
        const roles = await getGroupRoles(group.id);

        return {
          ...group,
          members,
          roles
        };
      }));

      return res.json(groupsWithDetails);
    } catch (error) {
      console.error('Error fetching groups:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get group by ID with members and roles
  app.get('/api/group-management/groups/:id', authMiddleware, requirePermission('group_view'), async (req: AuthRequest, res: Response) => {
    try {
      const groupId = parseInt(req.params.id);
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      const members = await getGroupMembers(groupId);
      const roles = await getGroupRoles(groupId);

      const result = {
        ...group,
        members,
        roles
      };

      return res.json(result);
    } catch (error) {
      console.error(`Error fetching group ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Add user to group
  app.post('/api/group-management/groups/:id/users', authMiddleware, requirePermission('group_edit'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const groupId = parseInt(req.params.id);
      const userId = parseInt(req.body.user_id);

      if (!userId) {
        return res.status(400).json({ message: 'User ID is required' });
      }

      // Check if group exists
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      // Check if user has permission to add users to this group
      if (req.user.role !== 'saas_admin' && group.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only add users to groups in your own company' });
      }

      // Check if user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if user is already in the group
      const [existingMembership] = await db
        .select()
        .from(groupUsers)
        .where(
          and(
            eq(groupUsers.group_id, groupId),
            eq(groupUsers.user_id, userId)
          )
        );

      if (existingMembership) {
        return res.status(400).json({ message: 'User is already a member of this group' });
      }

      // Add user to group
      const [membership] = await db
        .insert(groupUsers)
        .values({
          group_id: groupId,
          user_id: userId
        })
        .returning();

      return res.status(201).json(membership);
    } catch (error) {
      console.error(`Error adding user to group ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Remove user from group
  app.delete('/api/group-management/groups/:groupId/users/:userId', authMiddleware, requirePermission('group_edit'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const groupId = parseInt(req.params.groupId);
      const userId = parseInt(req.params.userId);

      // Check if group exists
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      // Check if user has permission to remove users from this group
      if (req.user.role !== 'saas_admin' && group.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only remove users from groups in your own company' });
      }

      // Check if user is in the group
      const [membership] = await db
        .select()
        .from(groupUsers)
        .where(
          and(
            eq(groupUsers.group_id, groupId),
            eq(groupUsers.user_id, userId)
          )
        );

      if (!membership) {
        return res.status(404).json({ message: 'User is not a member of this group' });
      }

      // Remove user from group
      await db
        .delete(groupUsers)
        .where(
          and(
            eq(groupUsers.group_id, groupId),
            eq(groupUsers.user_id, userId)
          )
        );

      return res.json({ message: 'User removed from group successfully' });
    } catch (error) {
      console.error(`Error removing user from group:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Assign role to group
  app.post('/api/group-management/groups/:id/roles', authMiddleware, requireAnyPermission(['group_edit', 'role_assign']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const groupId = parseInt(req.params.id);
      const roleId = parseInt(req.body.role_id);

      if (!roleId) {
        return res.status(400).json({ message: 'Role ID is required' });
      }

      // Check if group exists
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      // Check if user has permission to assign roles to this group
      if (req.user.role !== 'saas_admin' && group.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only assign roles to groups in your own company' });
      }

      // Check if role exists
      const [role] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId));

      if (!role) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Check if role is already assigned to the group
      const [existingAssignment] = await db
        .select()
        .from(groupRoles)
        .where(
          and(
            eq(groupRoles.group_id, groupId),
            eq(groupRoles.role_id, roleId)
          )
        );

      if (existingAssignment) {
        return res.status(400).json({ message: 'Role is already assigned to this group' });
      }

      // Assign role to group
      const [assignment] = await db
        .insert(groupRoles)
        .values({
          group_id: groupId,
          role_id: roleId
        })
        .returning();

      return res.status(201).json(assignment);
    } catch (error) {
      console.error(`Error assigning role to group ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Remove role from group
  app.delete('/api/group-management/groups/:groupId/roles/:roleId', authMiddleware, requireAnyPermission(['group_edit', 'role_assign']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const groupId = parseInt(req.params.groupId);
      const roleId = parseInt(req.params.roleId);

      // Check if group exists
      const [group] = await db
        .select()
        .from(groups)
        .where(eq(groups.id, groupId));

      if (!group) {
        return res.status(404).json({ message: 'Group not found' });
      }

      // Check if user has permission to remove roles from this group
      if (req.user.role !== 'saas_admin' && group.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only remove roles from groups in your own company' });
      }

      // Check if role is assigned to the group
      const [assignment] = await db
        .select()
        .from(groupRoles)
        .where(
          and(
            eq(groupRoles.group_id, groupId),
            eq(groupRoles.role_id, roleId)
          )
        );

      if (!assignment) {
        return res.status(404).json({ message: 'Role is not assigned to this group' });
      }

      // Remove role from group
      await db
        .delete(groupRoles)
        .where(
          and(
            eq(groupRoles.group_id, groupId),
            eq(groupRoles.role_id, roleId)
          )
        );

      return res.json({ message: 'Role removed from group successfully' });
    } catch (error) {
      console.error(`Error removing role from group:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Helper function to get group members
  async function getGroupMembers(groupId: number) {
    const groupMembers = await db
      .select({
        membership: groupUsers,
        user: users
      })
      .from(groupUsers)
      .leftJoin(users, eq(groupUsers.user_id, users.id))
      .where(eq(groupUsers.group_id, groupId));

    return groupMembers.map(member => ({
      id: member.user.id,
      username: member.user.username,
      email: member.user.email,
      full_name: member.user.full_name,
      role: member.user.role,
      membership_id: member.membership.id,
      joined_at: member.membership.created_at
    }));
  }

  // Helper function to get group roles
  async function getGroupRoles(groupId: number) {
    const groupRolesData = await db
      .select({
        assignment: groupRoles,
        role: customRoles
      })
      .from(groupRoles)
      .leftJoin(customRoles, eq(groupRoles.role_id, customRoles.id))
      .where(eq(groupRoles.group_id, groupId));

    return groupRolesData.map(roleData => ({
      id: roleData.role.id,
      name: roleData.role.name,
      description: roleData.role.description,
      is_system: roleData.role.is_system,
      assignment_id: roleData.assignment.id,
      assigned_at: roleData.assignment.created_at
    }));
  }
}
