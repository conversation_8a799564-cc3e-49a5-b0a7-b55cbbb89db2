import { apiRequest } from "@/lib/queryClient";
import { queryClient } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { Company } from "@shared/schema";
import { DEFAULT_COMPANY_ID, getSafeCompanyId, isValidCompanyId } from '@/config/companyConfig';

export interface UserData {
  id: number;
  email: string;
  full_name: string;
  role: string;
  company_id?: number;
  company_name?: string;
}

export interface LoginResponse {
  token: string;
  user: UserData;
}

export interface RegisterData {
  email: string;
  password: string;
  confirmPassword: string;
  full_name: string;
  role: string;
  company_id?: number;
}

export interface CompanyWithAccess {
  id: number;
  user_id: number;
  company_id: number;
  company: Company;
  is_primary: boolean;
  created_at: Date | string;
  updated_at: Date | string;
  name?: string; // Convenience accessor for company.name
  _missing_association_id?: boolean; // Flag to indicate if this is using a fallback ID
}

export const useAuth = () => {
  const { toast } = useToast();

  const login = async (email: string, password: string): Promise<LoginResponse | null> => {
    try {
      const response = await apiRequest("POST", "/api/auth/login", { email, password });
      const data = await response.json();

      // Debug login response in detail
      console.log("Login response data:", data);
      console.log("User company_name:", data.user?.company_name);

      // Make sure we're storing the complete user data with company_name
      if (data.user) {
        // Store token in localStorage
        localStorage.setItem("auth_token", data.token);

        // Store user data with explicit company_name ensure it's definitely included
        const userData = {
          ...data.user,
          company_name: data.user.company_name || null
        };

        // Store enhanced user data in localStorage
        localStorage.setItem("user_data", JSON.stringify(userData));
        console.log("Stored enhanced user data:", userData);
      }

      return data;
    } catch (error) {
      console.error("Login error:", error);
      // Don't show toast here - let the calling component handle the error display
      // This prevents duplicate error messages
      throw error; // Re-throw the error so the calling component can handle it
    }
  };

  const register = async (registerData: RegisterData): Promise<UserData | null> => {
    try {
      const response = await apiRequest("POST", "/api/auth/register", registerData);
      const data = await response.json();
      return data.user;
    } catch (error) {
      console.error("Registration error:", error);
      toast({
        title: "Registration Failed",
        description: error instanceof Error ? error.message : "Could not create account",
        variant: "destructive",
      });
      return null;
    }
  };

  const logout = () => {
    // Remove token and user data from localStorage
    localStorage.removeItem("auth_token");
    localStorage.removeItem("user_data");

    // Clear all query cache
    queryClient.clear();
  };

  const getToken = (): string | null => {
    return localStorage.getItem("auth_token");
  };

  const getCurrentUser = (): UserData | null => {
    const userData = localStorage.getItem("user_data");
    let parsedUser = userData ? JSON.parse(userData) : null;

    // Only log user data in development mode and limit frequency
    if (parsedUser && process.env.NODE_ENV === 'development') {
      // Use a simple throttling mechanism to reduce log spam
      const now = Date.now();
      const lastLogTime = (window as any).__lastUserLogTime || 0;

      if (now - lastLogTime > 5000) { // Log at most once every 5 seconds
        console.log(`Current user data from localStorage:`, {
          userId: parsedUser.id,
          username: parsedUser.username,
          companyId: parsedUser.company_id,
          companyName: parsedUser.company_name
        });
        (window as any).__lastUserLogTime = now;
      }
    }

    return parsedUser;
  };

  const isAuthenticated = (): boolean => {
    return !!getToken();
  };

  const isAuthorized = (requiredRoles: string[] = []): boolean => {
    const user = getCurrentUser();

    if (!user) return false;

    if (requiredRoles.length === 0) return true;

    return requiredRoles.includes(user.role);
  };

  // Helper function to add retry capability to API calls
  const fetchWithRetry = async (url: string, options: RequestInit, maxRetries: number = 3): Promise<Response> => {
    let lastError: Error | null = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const response = await fetch(url, options);
        return response;
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        // Check if we're dealing with network errors
        const isNetworkError =
          error instanceof TypeError ||
          (error instanceof Error && (
            error.message.includes('Failed to fetch') ||
            error.message.includes('Network request failed') ||
            error.message.includes('NetworkError') ||
            error.message.includes('network error')
          ));

        // If it's not a network error, we don't want to retry
        if (!isNetworkError) {
          throw error;
        }

        // Wait longer between each retry attempt
        if (attempt < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, attempt - 1), 5000);
          await new Promise(resolve => setTimeout(resolve, delay));
        }
      }
    }

    // If all retries failed, throw the last error
    throw lastError || new Error('All fetch attempts failed');
  };

  const getUserCompanies = async (userId: number): Promise<CompanyWithAccess[]> => {
    try {
      // Make sure we have authentication
      if (!isAuthenticated()) {
        return [];
      }

      const token = getToken();
      if (!token) {
        return [];
      }

      // Use fetchWithRetry with credentials to ensure cookies are sent
      const response = await fetchWithRetry(`/api/users/${userId}/companies`, {
        method: "GET",
        headers: {
          "Authorization": `Bearer ${token}`,
          "Accept": "application/json",
          "Content-Type": "application/json"
        },
        credentials: "include"
      }, 3); // Try up to 3 times

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP error ${response.status}: ${errorText || 'No error details provided'}`);
      }

      let data;
      try {
        data = await response.json();
      } catch (parseError) {
        throw new Error("Invalid JSON response from server");
      }

      // Ensure the data is an array
      if (!Array.isArray(data)) {
        return [];
      }

      // Transform the data to match our interface if needed
      const mappedData = data.map(item => {
        // Case 1: If the data already includes a user-company association with the right structure
        if (item.id && item.user_id && item.company_id && item.company && typeof item.company === 'object') {
          return {
            ...item,
            // Ensure company_id is set
            company_id: item.company_id || item.company.id
          };
        }

        // Case 2: If the data includes a user_company_id as a separate property
        if (item.id && item.user_company_id) {
          return {
            id: item.user_company_id, // Use the explicit user-company association ID
            user_id: userId,
            company_id: item.id,
            company: {
              id: item.id,
              name: item.name,
              address: item.address,
              phone: item.phone,
              email: item.email,
              website: item.website,
              logo: item.logo,
              active: item.active,
              created_at: item.created_at,
              updated_at: item.updated_at
            },
            is_primary: item.is_primary || false,
            created_at: item.created_at || new Date(),
            updated_at: item.updated_at || new Date()
          };
        }

        // Case 3: If we're dealing with the real data structure from our backend
        // We need to find the user-company association ID from the database
        if (item.user_company) {
          return {
            id: item.user_company.id, // This is the correct association ID
            user_id: userId,
            company_id: item.id,
            company: {
              id: item.id,
              name: item.name,
              address: item.address,
              phone: item.phone,
              email: item.email,
              website: item.website,
              logo: item.logo,
              active: item.active,
              created_at: item.created_at,
              updated_at: item.updated_at
            },
            is_primary: item.is_primary || false,
            created_at: item.created_at || new Date(),
            updated_at: item.updated_at || new Date()
          };
        }

        // Case 4: Default case - the API returns just company objects,
        // need to query getUserCompanyAssociations separately (not ideal)
        return {
          // Use company ID as a fallback, but this is not correct for API calls
          id: item.id,
          user_id: userId,
          company_id: item.id,
          company: {
            id: item.id,
            name: item.name,
            address: item.address,
            phone: item.phone,
            email: item.email,
            website: item.website,
            logo: item.logo,
            active: item.active,
            created_at: item.created_at,
            updated_at: item.updated_at
          },
          is_primary: item.is_primary || false,
          created_at: item.created_at || new Date(),
          updated_at: item.updated_at || new Date(),
          // Add a flag to indicate this is not a real association ID
          _missing_association_id: true
        };
      });

      return mappedData;
    } catch (error) {
      // Create a friendly error message
      let errorMessage = "Could not fetch user companies. Please try again.";

      if (error instanceof Error && error.message) {
        errorMessage = `Error: ${error.message}`;
      } else if (error instanceof Response) {
        errorMessage = `HTTP Error: ${error.status} - ${error.statusText}`;
      }

      toast({
        title: "Error Loading Companies",
        description: errorMessage,
        variant: "destructive",
      });

      // Even with errors, return empty array to avoid breaking UI
      return [];
    }
  };

  const switchCompany = async (companyId: number, companyName: string, setAsPrimary: boolean = false): Promise<void> => {
    const userData = getCurrentUser();
    if (!userData) return;

    // Validate the company ID
    if (!isValidCompanyId(companyId)) {
      console.warn(`Attempted to switch to invalid company ID ${companyId}. Company ID must be a positive integer.`);
      toast({
        title: "Invalid Company",
        description: "Cannot switch to the selected company. Please try again or contact support.",
        variant: "destructive",
      });
      return;
    }

    console.log("Switching company to:", { companyId, companyName, setAsPrimary });

    // If we're already on this company and trying to set it as primary,
    // just make the primary request without switching company context
    const isPrimaryUpdate = setAsPrimary && userData.company_id === companyId;

    if (isPrimaryUpdate) {
      console.log("Already on this company, just updating primary status");

      try {
        if (!isAuthenticated()) {
          console.error("User is not authenticated!");
          toast({
            title: "Authentication Required",
            description: "Please log in to set your primary company",
            variant: "destructive",
          });
          return;
        }

        // First we need to get the user-companies to find the correct association ID
        const userCompanies = await getUserCompanies(userData.id);
        console.log("User companies for primary update:", userCompanies.length);

        if (!userCompanies || userCompanies.length === 0) {
          console.error("No user companies returned from API");
          toast({
            title: "Error",
            description: "Could not access your company information. Please try again.",
            variant: "destructive",
          });
          return;
        }

        // Find the user-company association for this company
        const userCompanyAssociation = userCompanies.find((uc: CompanyWithAccess) =>
          uc.company_id === companyId || (uc.company && uc.company.id === companyId)
        );

        console.log("Found association:", userCompanyAssociation?.id);

        if (!userCompanyAssociation || !userCompanyAssociation.id) {
          console.error(`Could not find valid user-company association for company ID ${companyId}`);
          toast({
            title: "Error",
            description: `Could not set ${companyName} as primary company. Please try again later.`,
            variant: "destructive",
          });
          return;
        }

        // Make the API call to set this company as primary
        const token = getToken();
        if (!token) {
          console.error("No auth token found!");
          toast({
            title: "Authentication Error",
            description: "Please log in again to update your settings.",
            variant: "destructive",
          });
          return;
        }

        console.log(`Setting company ${companyId} as primary using association ID ${userCompanyAssociation.id}`);

        try {
          const response = await fetchWithRetry(`/api/user-companies/${userCompanyAssociation.id}/primary`, {
            method: "PATCH",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Bearer ${token}`
            },
            credentials: "include",
            body: JSON.stringify({ is_primary: true })
          });

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP error ${response.status}: ${errorText}`);
          }

          toast({
            title: "Success",
            description: `${companyName} has been set as your primary company.`,
            variant: "default",
          });

          // Reload after success to refresh all components
          window.location.reload();

        } catch (fetchError) {
          console.error("Fetch error setting primary company:", fetchError);
          toast({
            title: "Error Setting Primary Company",
            description: `Technical error: ${fetchError instanceof Error ? fetchError.message : "Unknown error"}`,
            variant: "destructive",
          });
        }

        return;
      } catch (error) {
        console.error("Error in primary company update:", error);
        toast({
          title: "Error",
          description: "An unexpected error occurred. Please try again.",
          variant: "destructive",
        });
        return;
      }
    }

    // Otherwise, update user data with new company
    const updatedUserData = {
      ...userData,
      company_id: companyId,
      company_name: companyName || `Company ${companyId}`  // Fallback if name is missing
    };

    console.log("Updated user data:", updatedUserData);

    // Store updated user data
    localStorage.setItem("user_data", JSON.stringify(updatedUserData));

    // Get a new token with the updated company ID
    try {
      console.log("Requesting token refresh for new company context");
      const refreshResponse = await fetchWithRetry('/api/auth/refresh-token', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${getToken()}`
        },
        credentials: 'include',
        body: JSON.stringify({
          company_id: companyId
        })
      }, 3); // Try up to 3 times

      if (refreshResponse.ok) {
        const tokenData = await refreshResponse.json();
        if (tokenData.token) {
          localStorage.setItem('auth_token', tokenData.token);
          console.log("Token refreshed with new company context");
        } else {
          console.error("Token refresh response didn't contain a token");
        }
      } else {
        console.error("Failed to refresh token:", refreshResponse.status);
      }
    } catch (refreshError) {
      console.error("Error refreshing token:", refreshError);

      // Attempt to provide a more helpful error message to the user
      let errorMessage = "Problem updating authentication for the new company.";

      if (refreshError instanceof Error) {
        console.error("Error name:", refreshError.name);
        console.error("Error message:", refreshError.message);

        if (refreshError.message.includes('Failed to fetch') ||
            refreshError.message.includes('NetworkError') ||
            refreshError.message.includes('network error')) {
          errorMessage = "Network error while refreshing authentication. Please check your connection and try again.";
        }
      }

      // Show a toast with error info but don't prevent the switch
      toast({
        title: "Authentication Warning",
        description: `${errorMessage} Some features may be limited until you refresh the page.`,
        variant: "destructive",
      });
    }

    // If user just wants to switch companies, reload now
    if (!setAsPrimary) {
      // Force a reload to ensure all components update with the new company
      window.location.href = "/dashboard";
      return;
    }

    // Handle setting as primary company
    try {
      if (!isAuthenticated()) {
        console.error("User is not authenticated!");
        toast({
          title: "Authentication Required",
          description: "Please log in to set your primary company",
          variant: "destructive",
        });
        window.location.href = "/dashboard";
        return;
      }

      // First we need to get the user-companies to find the correct association ID
      const userCompanies = await getUserCompanies(userData.id);
      console.log("User companies for primary update:", userCompanies.length);

      if (!userCompanies || userCompanies.length === 0) {
        console.error("No user companies returned from API");
        toast({
          title: "Error",
          description: "Could not access your company information. Your company has been switched.",
          variant: "destructive",
        });
        window.location.href = "/dashboard";
        return;
      }

      // Find the user-company association for this company - simplified logic
      const userCompanyAssociation = userCompanies.find((uc: CompanyWithAccess) =>
        uc.company_id === companyId || (uc.company && uc.company.id === companyId)
      );

      console.log("Found association:", userCompanyAssociation?.id);

      if (!userCompanyAssociation || !userCompanyAssociation.id) {
        console.error(`Could not find valid user-company association for company ID ${companyId}`);
        toast({
          title: "Company Switched",
          description: `Switched to ${companyName}, but couldn't set it as primary due to missing association data.`,
          variant: "destructive",
        });
        window.location.href = "/dashboard";
        return;
      }

      console.log(`Setting company ${companyId} as primary using association ID ${userCompanyAssociation.id}`);

      // Make a direct fetch call with full control over the request
      const token = getToken();
      if (!token) {
        console.error("No auth token found!");
        toast({
          title: "Authentication Error",
          description: "Please log in again to update your settings.",
          variant: "destructive",
        });
        window.location.href = "/dashboard";
        return;
      }

      try {
        const response = await fetchWithRetry(`/api/user-companies/${userCompanyAssociation.id}/primary`, {
          method: "PATCH",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          },
          credentials: "include",
          body: JSON.stringify({ is_primary: true })
        });

        console.log("Set primary response status:", response.status);

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`HTTP error ${response.status}: ${errorText}`);
          toast({
            title: "Company Switched",
            description: `Switched to ${companyName}, but couldn't set it as primary. Try again later.`,
            variant: "destructive",
          });
        } else {
          console.log("Set primary success!");
          toast({
            title: "Success",
            description: `${companyName} has been set as your primary company.`,
            variant: "default",
          });
        }
      } catch (fetchError) {
        console.error("Fetch error setting primary company:", fetchError);
        toast({
          title: "Company Switched",
          description: `Switched to ${companyName}, but couldn't update primary status due to a technical error.`,
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error in primary company update:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating your primary company. Your company has been switched.",
        variant: "destructive",
      });
    }

    // Always reload at the end to update UI with the new company
    window.location.href = "/dashboard";
  };

  const user = getCurrentUser();

  return {
    login,
    register,
    logout,
    getToken,
    getCurrentUser,
    isAuthenticated,
    isAuthorized,
    getUserCompanies,
    switchCompany,
    user
  };
};
