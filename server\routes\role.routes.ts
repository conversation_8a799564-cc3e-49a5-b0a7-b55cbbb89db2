import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, requirePermission, requireAnyPermission, AuthRequest } from '../middleware/auth';
import { customRoles, insertCustomRoleSchema, rolePermissions, insertRolePermissionSchema, permissions } from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';

export function registerRoleRoutes(app: Express): void {
  console.log('Registering role routes...');
  // Get all roles
  app.get('/api/roles', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : null;

      let query = db.select().from(customRoles);

      // If company_id is provided, filter by it
      if (companyId) {
        query = query.where(eq(customRoles.company_id, companyId));
      }

      const roles = await query;
      return res.json(roles);
    } catch (error) {
      console.error('Error fetching roles:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get role by ID
  app.get('/api/roles/:id', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);
      const [role] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId));

      if (!role) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Get permissions for this role
      const rolePerms = await db
        .select({
          role_id: rolePermissions.role_id,
          permission_id: rolePermissions.permission_id,
          permission: permissions
        })
        .from(rolePermissions)
        .leftJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
        .where(eq(rolePermissions.role_id, roleId));

      const result = {
        ...role,
        permissions: rolePerms.map(rp => rp.permission)
      };

      return res.json(result);
    } catch (error) {
      console.error(`Error fetching role ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get permissions for a specific role
  app.get('/api/roles/:id/permissions', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);

      if (isNaN(roleId)) {
        return res.status(400).json({ message: 'Invalid role ID' });
      }

      // Get permissions for this role
      const rolePerms = await db
        .select({
          role_id: rolePermissions.role_id,
          permission_id: rolePermissions.permission_id,
          permission: permissions
        })
        .from(rolePermissions)
        .leftJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
        .where(eq(rolePermissions.role_id, roleId));

      const permissionsList = rolePerms.map(rp => rp.permission).filter(Boolean);
      return res.json(permissionsList);
    } catch (error) {
      console.error('Error fetching role permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get effective permissions for a role (including inherited permissions)
  app.get('/api/roles/:id/effective-permissions', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);

      if (isNaN(roleId)) {
        return res.status(400).json({ message: 'Invalid role ID' });
      }

      // For now, return the same as direct permissions
      // TODO: Implement hierarchy-based effective permissions
      const rolePerms = await db
        .select({
          role_id: rolePermissions.role_id,
          permission_id: rolePermissions.permission_id,
          permission: permissions
        })
        .from(rolePermissions)
        .leftJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
        .where(eq(rolePermissions.role_id, roleId));

      const effectivePermissions = rolePerms.map(rp => ({
        ...rp.permission,
        source: 'direct',
        inherited_from: null
      })).filter(Boolean);

      return res.json(effectivePermissions);
    } catch (error) {
      console.error('Error fetching effective permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get temporary permissions for a role
  app.get('/api/roles/:id/temporary-permissions', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);

      if (isNaN(roleId)) {
        return res.status(400).json({ message: 'Invalid role ID' });
      }

      // Return empty array for now - temporary permissions not implemented yet
      // TODO: Implement temporary permissions functionality
      return res.json([]);
    } catch (error) {
      console.error('Error fetching temporary permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Create role
  app.post('/api/roles', authMiddleware, requirePermission('role_create'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = insertCustomRoleSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if user has permission to create role for this company
      if (req.user.role !== 'saas_admin' && result.data.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only create roles for your own company' });
      }

      // Check if role name already exists for this company
      const [existingRole] = await db
        .select()
        .from(customRoles)
        .where(
          and(
            eq(customRoles.name, result.data.name),
            eq(customRoles.company_id, result.data.company_id)
          )
        );

      if (existingRole) {
        return res.status(400).json({ message: 'Role name already exists for this company' });
      }

      const [role] = await db
        .insert(customRoles)
        .values(result.data)
        .returning();

      // If permissions are provided, assign them to the role
      if (req.body.permissions && Array.isArray(req.body.permissions)) {
        const permissionIds = req.body.permissions;

        for (const permissionId of permissionIds) {
          await db
            .insert(rolePermissions)
            .values({
              role_id: role.id,
              permission_id: permissionId
            });
        }
      }

      // Get the role with its permissions
      const roleWithPermissions = await getRoleWithPermissions(role.id);

      return res.status(201).json(roleWithPermissions);
    } catch (error) {
      console.error('Error creating role:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update role
  app.put('/api/roles/:id', authMiddleware, requirePermission('role_edit'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const roleId = parseInt(req.params.id);
      const result = insertCustomRoleSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if role exists
      const [existingRole] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId));

      if (!existingRole) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Check if user has permission to update this role
      if (req.user.role !== 'saas_admin' && existingRole.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only update roles for your own company' });
      }

      // If name is being updated, check if it already exists for this company
      if (result.data.name && result.data.name !== existingRole.name) {
        const [duplicateName] = await db
          .select()
          .from(customRoles)
          .where(
            and(
              eq(customRoles.name, result.data.name),
              eq(customRoles.company_id, existingRole.company_id)
            )
          );

        if (duplicateName) {
          return res.status(400).json({ message: 'Role name already exists for this company' });
        }
      }

      const [updatedRole] = await db
        .update(customRoles)
        .set(result.data)
        .where(eq(customRoles.id, roleId))
        .returning();

      // If permissions are provided, update them
      if (req.body.permissions && Array.isArray(req.body.permissions)) {
        // Delete existing permissions
        await db
          .delete(rolePermissions)
          .where(eq(rolePermissions.role_id, roleId));

        // Add new permissions
        const permissionIds = req.body.permissions;

        for (const permissionId of permissionIds) {
          await db
            .insert(rolePermissions)
            .values({
              role_id: roleId,
              permission_id: permissionId
            });
        }
      }

      // Get the updated role with its permissions
      const roleWithPermissions = await getRoleWithPermissions(roleId);

      return res.json(roleWithPermissions);
    } catch (error) {
      console.error(`Error updating role ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete role
  app.delete('/api/roles/:id', authMiddleware, requirePermission('role_delete'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const roleId = parseInt(req.params.id);

      // Check if role exists
      const [existingRole] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId));

      if (!existingRole) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Check if user has permission to delete this role
      if (req.user.role !== 'saas_admin' && existingRole.company_id !== req.user.company_id) {
        return res.status(403).json({ message: 'You can only delete roles for your own company' });
      }

      // Don't allow deleting system roles
      if (existingRole.is_system) {
        return res.status(403).json({ message: 'System roles cannot be deleted' });
      }

      await db
        .delete(customRoles)
        .where(eq(customRoles.id, roleId));

      return res.json({ message: 'Role deleted successfully' });
    } catch (error) {
      console.error(`Error deleting role ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Helper function to get a role with its permissions
  async function getRoleWithPermissions(roleId: number) {
    const [role] = await db
      .select()
      .from(customRoles)
      .where(eq(customRoles.id, roleId));

    if (!role) {
      return null;
    }

    const rolePerms = await db
      .select({
        role_id: rolePermissions.role_id,
        permission_id: rolePermissions.permission_id,
        permission: permissions
      })
      .from(rolePermissions)
      .leftJoin(permissions, eq(rolePermissions.permission_id, permissions.id))
      .where(eq(rolePermissions.role_id, roleId));

    return {
      ...role,
      permissions: rolePerms.map(rp => rp.permission)
    };
  }
}
