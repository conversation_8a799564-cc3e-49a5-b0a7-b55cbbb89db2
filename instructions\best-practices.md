# TrackFina Application Best Practices

This document outlines the best practices for implementing new features and components in the TrackFina application. Following these guidelines will help prevent common issues and ensure consistent code quality.

## Component Implementation

### SelectItem Component Guidelines

- **Never use empty strings as values**: Always provide a meaningful string value for SelectItem components
  ```jsx
  // INCORRECT ❌
  <SelectItem value="">None</SelectItem>
  
  // CORRECT ✅
  <SelectItem value="none">None</SelectItem>
  ```
- When representing "null" or "empty" values, use a clear string like "none", "empty", or "default"
- Each SelectItem must have a unique value within its Select group
- Use consistent naming conventions for similar values across the application

### Form Field Handling

- Always initialize form values properly, especially for optional fields
- For fields that can be null/undefined:
  ```jsx
  // INCORRECT ❌
  value={field.value}
  
  // CORRECT ✅
  value={field.value || ''}
  ```
- Use controlled components consistently throughout the application
- Default values should be provided for all form fields
  ```jsx
  const form = useForm({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      name: '',
      description: '',
      amount: 0,
      // Initialize all fields, including optional ones
      reference: null,
      notes: ''
    }
  });
  ```

### Currency Formatting

- Always use the correct locale and currency code for formatting:
  ```js
  // INCORRECT ❌
  formatCurrency(amount, 'USD')
  
  // CORRECT ✅
  formatCurrency(amount, 'INR', 'en-IN')
  ```
- Don't use currency formatters for non-currency values (like counts)
- For consistency, use the utility function throughout the application

## Backend Implementation

### API Implementation Checklist

- Always implement all backend methods defined in the interface
- When writing a new frontend feature, verify that the corresponding backend methods exist
- For any new page that requires API access, check that:
  1. The storage method is implemented in `storage.ts`
  2. The API endpoint is properly defined in `routes.ts` 
  3. The types are correctly defined in `schema.ts`

### Database Queries

- Use SQL template strings for date comparisons and complex filters:
  ```typescript
  // INCORRECT ❌
  query = query.where(eq(expenses.expense_date, startDate));
  
  // CORRECT ✅
  query = query.where(
    sql`${expenses.expense_date} >= ${startDate.toISOString()}`
  );
  ```
- Always include proper error handling for database operations
- For complex queries, include comments explaining the logic
- Use descriptive variable names that clearly indicate what data is being queried

## Financial System Implementation

### Chart of Accounts Best Practices

- **Use standardized account coding**: Follow standard accounting practices with structured account codes
  ```
  1000-1999: Assets
  2000-2999: Liabilities
  3000-3999: Equity
  4000-4999: Income/Revenue
  5000-5999: Expenses
  ```

- **Include descriptive information**: All accounts should have clear descriptions to improve user understanding
  ```typescript
  // INCORRECT ❌
  { account_code: "1000", account_name: "Cash" }
  
  // CORRECT ✅
  { 
    account_code: "1000", 
    account_name: "Cash",
    description: "Physical currency held on premises"
  }
  ```

- **System accounts protection**: System accounts should be protected from deletion and unauthorized modification
  ```typescript
  // When updating accounts:
  if (account.is_system) {
    // Only allow certain fields to be updated
    // Don't allow deletion
  }
  ```

- **Initialize accounts properly**: Always ensure a company has the necessary system accounts before allowing financial operations
  ```typescript
  // Before performing financial operations:
  const requiredAccounts = ["Cash", "Bank", "Loan Receivable"];
  for (const accountName of requiredAccounts) {
    const account = await getAccountByName(companyId, accountName);
    if (!account) {
      // Initialize system accounts or notify user
    }
  }
  ```

### Double-Entry Accounting Practices

- **Always balance debits and credits**: Ensure every transaction has equal debits and credits
  ```typescript
  // Verify transaction balance:
  const totalDebits = transactions.filter(t => t.type === 'debit').reduce((sum, t) => sum + parseFloat(t.amount), 0);
  const totalCredits = transactions.filter(t => t.type === 'credit').reduce((sum, t) => sum + parseFloat(t.amount), 0);
  
  if (Math.abs(totalDebits - totalCredits) > 0.001) {
    throw new Error("Transaction is not balanced");
  }
  ```

- **Create corresponding journal entries**: Every financial action (loan, collection, expense) must generate proper journal entries
  ```typescript
  // When creating a loan:
  await createJournalEntry({
    company_id: companyId,
    reference_id: loanId,
    reference_type: 'loan',
    description: `Loan disbursement: ${loanNumber}`,
    transaction_date: new Date(),
    entries: {
      // Debit Loan Receivable (asset increases)
      [SYSTEM_ACCOUNT_CODES.LOAN_RECEIVABLE]: { account_id: loanReceivableAccount.id, amount: loanAmount, type: 'debit' },
      // Credit Cash/Bank (asset decreases)
      [SYSTEM_ACCOUNT_CODES.CASH]: { account_id: cashAccount.id, amount: loanAmount, type: 'credit' }
    }
  });
  ```

- **Use transaction references**: Always include clear reference IDs and types in journal entries
  ```typescript
  // Good practice for journal entry:
  {
    reference_id: entityId,
    reference_type: 'loan' | 'collection' | 'expense',
    description: "Clear description of transaction purpose"
  }
  ```

### Financial API Design

- **Consistent account hierarchy**: Design endpoints to return account data in a hierarchical structure for reports
  ```typescript
  // Well-structured account format for reports:
  {
    "assets": [
      { 
        "category": "Current Assets",
        "accounts": [ 
          { "code": "1000", "name": "Cash", "balance": 5000.00 },
          // ... other accounts
        ] 
      },
      // ... other categories
    ],
    "liabilities": [ /* similar structure */ ],
    "equity": [ /* similar structure */ ],
    // ... other sections
  }
  ```

- **Date range consistency**: All financial reports should use consistent date range parameters
  ```typescript
  // Consistent date parameters across all reports:
  app.get('/api/companies/:companyId/reports/:reportType', async (req, res) => {
    const { companyId, reportType } = req.params;
    const { startDate, endDate } = req.query;
    
    // Always validate dates the same way
    const validStartDate = startDate ? new Date(startDate) : new Date(/* default start */);
    const validEndDate = endDate ? new Date(endDate) : new Date();
    
    // Generate report based on type
    // ...
  });
  ```

## Component Routing and Navigation

### Dynamic Import Configuration

- Always update the DynamicImport component when adding new pages:
  ```tsx
  case '/financial/new-feature/index':
  case 'financial/new-feature/index':
    return lazy(() => import('@/pages/financial/new-feature/index'));
  ```
- Follow the established pattern for route naming
- Test navigation to pages directly (not just through links)

### Routing Configuration

- Register all new routes in App.tsx
- Maintain consistent route patterns:
  - List views: `/[module]/[entity]` or `/[module]/[entity]/index`
  - Detail views: `/[module]/[entity]/[id]`
  - Edit views: `/[module]/[entity]/[id]/edit`
  - Create views: `/[module]/[entity]/create`

## Testing and Quality Assurance

### Pre-Submission Checklist

Before submitting new code, verify:
- [ ] No empty string values in SelectItem components
- [ ] All form fields properly handle null/undefined values
- [ ] Required backend methods are implemented
- [ ] All database queries use proper SQL templates for filtering
- [ ] Currency formatting is consistent (using 'INR' with 'en-IN' locale)
- [ ] Components are properly imported in DynamicImport.tsx
- [ ] Financial transactions generate proper journal entries
- [ ] Chart of accounts is properly structured with correct account codes

### Financial Testing Checklist

- [ ] Test creation of a loan and verify journal entries are correctly created
- [ ] Test loan repayment/collection and verify accounting entries match
- [ ] Verify trial balance shows balanced debits and credits
- [ ] Check that all required system accounts exist for the company
- [ ] Verify financial reports show the correct account structure and balances
- [ ] Test edge cases like zero-amount transactions and negative balances

## Error Handling

### Frontend Error Handling

- Use try/catch blocks for API requests and data processing
- Provide descriptive error messages to users
- Implement graceful fallbacks for failed data fetching
- Use the toast component for notifying users of errors

### Backend Error Handling

- Log detailed error information
- Return appropriate HTTP status codes
- Provide meaningful error messages in API responses
- Always handle potential exceptions in database operations

## Performance Considerations

- Use pagination for large datasets
- Implement appropriate caching strategies
- Optimize database queries for performance
- Lazy load components and resources when appropriate
- Consider the mobile experience - test on different screen sizes

By following these guidelines, we can prevent common issues and maintain a high-quality, consistent codebase.