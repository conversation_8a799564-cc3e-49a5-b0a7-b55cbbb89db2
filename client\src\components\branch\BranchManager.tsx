import React, { useState, useEffect } from 'react';
import { use<PERSON>ran<PERSON>, Branch } from '@/lib/branches';
import { useAuth } from '@/lib/auth';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import {
  Building,
  MoreVertical,
  Plus,
  Edit,
  Trash2,
  CheckCircle,
  XCircle,
  Loader2,
  AlertTriangle
} from 'lucide-react';

// Form schema
const branchFormSchema = z.object({
  name: z.string().min(2, 'Branch name must be at least 2 characters').max(100),
  address: z.string().optional(),
  phone: z.string().regex(/^\+91\d{10}$/, { message: "Please enter a valid 10-digit phone number" }).optional().or(z.literal('')),
  email: z.string().email('Invalid email address').optional().or(z.literal('')),
  manager_name: z.string().optional(),
  notes: z.string().optional(),
});

type BranchFormValues = z.infer<typeof branchFormSchema>;

const BranchForm: React.FC<{
  defaultValues?: Partial<BranchFormValues>;
  onSubmit: (values: BranchFormValues) => void;
  onCancel: () => void;
  isSubmitting: boolean;
  mode: 'create' | 'edit';
}> = ({ defaultValues, onSubmit, onCancel, isSubmitting, mode }) => {
  const form = useForm<BranchFormValues>({
    resolver: zodResolver(branchFormSchema),
    defaultValues: {
      name: '',
      address: '',
      phone: defaultValues?.phone ?
        (defaultValues.phone.startsWith('+91') ? defaultValues.phone : `+91${defaultValues.phone}`) :
        '',
      email: '',
      manager_name: '',
      notes: '',
      ...defaultValues,
      // Override phone again to ensure it has the correct format
      phone: defaultValues?.phone ?
        (defaultValues.phone.startsWith('+91') ? defaultValues.phone : `+91${defaultValues.phone}`) :
        '',
    },
  });

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <FormField
          control={form.control}
          name="name"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Branch Name*</FormLabel>
              <FormControl>
                <Input placeholder="Enter branch name" {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="manager_name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Manager Name</FormLabel>
                <FormControl>
                  <Input placeholder="Enter manager name" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="phone"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Mobile Number</FormLabel>
                <FormControl>
                  <div className="flex" style={{ zIndex: 10 }}>
                    <span className="flex items-center px-3 border border-r-0 rounded-l-md bg-muted text-muted-foreground font-medium">
                      +91
                    </span>
                    <Input
                      className="rounded-l-none !pl-3"
                      placeholder="Enter 10 digit mobile number"
                      maxLength={10}
                      type="tel"
                      {...field}
                      value={field.value ? field.value.replace(/^\+91/, '') : ''}
                      onChange={(e) => {
                        // Only allow numbers and limit to 10 digits
                        const value = e.target.value.replace(/\D/g, '').substring(0, 10);
                        field.onChange(`+91${value}`);
                      }}
                    />
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormField
            control={form.control}
            name="email"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Email</FormLabel>
                <FormControl>
                  <Input placeholder="Enter email address" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="address"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Address</FormLabel>
                <FormControl>
                  <Input placeholder="Enter branch address" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="notes"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Notes</FormLabel>
              <FormControl>
                <Textarea
                  placeholder="Enter any additional notes"
                  {...field}
                  rows={3}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {mode === 'create' ? 'Create Branch' : 'Update Branch'}
          </Button>
        </DialogFooter>
      </form>
    </Form>
  );
};

export const BranchManager: React.FC = () => {
  const { getCurrentUser } = useAuth();
  const {
    branches,
    fetchBranches,
    createBranchWithFeedback,
    updateBranchWithFeedback,
    deleteBranchWithFeedback,
    currentBranchId,
    switchBranch,
    isLoading
  } = useBranches();

  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<Branch | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const user = getCurrentUser();
  const companyId = user?.company_id;

  // Fetch branches on component mount
  useEffect(() => {
    if (companyId) {
      fetchBranches(companyId);
    }
  }, [companyId, fetchBranches]);

  // Create branch handler
  const handleCreateBranch = async (values: BranchFormValues) => {
    if (!companyId) return;

    setIsSubmitting(true);
    try {
      const result = await createBranchWithFeedback({
        ...values,
        company_id: companyId,
        status: 'active'
      });

      if (result) {
        setIsCreateDialogOpen(false);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Edit branch handler
  const handleEditBranch = async (values: BranchFormValues) => {
    if (!selectedBranch) return;

    setIsSubmitting(true);
    try {
      const result = await updateBranchWithFeedback(selectedBranch.id, values);

      if (result) {
        setIsEditDialogOpen(false);
        setSelectedBranch(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Delete branch handler
  const handleDeleteBranch = async () => {
    if (!selectedBranch) return;

    setIsSubmitting(true);
    try {
      const result = await deleteBranchWithFeedback(
        selectedBranch.id,
        selectedBranch.name
      );

      if (result) {
        setIsDeleteDialogOpen(false);
        setSelectedBranch(null);
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // Open edit dialog
  const openEditDialog = (branch: Branch) => {
    setSelectedBranch(branch);
    setIsEditDialogOpen(true);
  };

  // Open delete dialog
  const openDeleteDialog = (branch: Branch) => {
    setSelectedBranch(branch);
    setIsDeleteDialogOpen(true);
  };

  // Set branch as current
  const setAsCurrentBranch = (branch: Branch) => {
    switchBranch(branch.id, branch.name);
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div>
            <CardTitle className="flex items-center">
              <Building className="mr-2 h-5 w-5" />
              Branch Management
            </CardTitle>
            <CardDescription>
              Create and manage branches for your company
            </CardDescription>
          </div>
          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button size="sm">
                <Plus className="mr-2 h-4 w-4" />
                Add Branch
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[500px]">
              <DialogHeader>
                <DialogTitle>Create New Branch</DialogTitle>
                <DialogDescription>
                  Add a new branch for your company. Fill in the details below.
                </DialogDescription>
              </DialogHeader>
              <BranchForm
                onSubmit={handleCreateBranch}
                onCancel={() => setIsCreateDialogOpen(false)}
                isSubmitting={isSubmitting}
                mode="create"
              />
            </DialogContent>
          </Dialog>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2">Loading branches...</span>
            </div>
          ) : branches.length === 0 ? (
            <div className="text-center py-8 space-y-4">
              <div className="mx-auto w-12 h-12 rounded-full bg-muted flex items-center justify-center">
                <Building className="h-6 w-6 text-muted-foreground" />
              </div>
              <div>
                <p className="text-muted-foreground">No branches found</p>
                <p className="text-sm text-muted-foreground mt-1">
                  Get started by creating your first branch
                </p>
              </div>
              <Button
                variant="outline"
                onClick={() => setIsCreateDialogOpen(true)}
                className="mt-2"
              >
                <Plus className="mr-2 h-4 w-4" />
                Add First Branch
              </Button>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Manager</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {branches.map((branch) => (
                    <TableRow
                      key={branch.id}
                      className={branch.id === currentBranchId ? 'bg-muted/50' : ''}
                    >
                      <TableCell className="font-medium">
                        <div className="flex items-center">
                          <Building className="mr-2 h-4 w-4 text-muted-foreground" />
                          <span>{branch.name}</span>
                          {branch.id === currentBranchId && (
                            <Badge variant="outline" className="ml-2">Current</Badge>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>{branch.manager_name || '-'}</TableCell>
                      <TableCell>
                        {branch.status === 'active' ? (
                          <div className="flex items-center">
                            <CheckCircle className="mr-2 h-4 w-4 text-green-500" />
                            <span>Active</span>
                          </div>
                        ) : (
                          <div className="flex items-center">
                            <XCircle className="mr-2 h-4 w-4 text-red-500" />
                            <span>Inactive</span>
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => setAsCurrentBranch(branch)}
                              disabled={branch.id === currentBranchId}
                            >
                              <CheckCircle className="mr-2 h-4 w-4" />
                              Set as Current
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => openEditDialog(branch)}>
                              <Edit className="mr-2 h-4 w-4" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={() => openDeleteDialog(branch)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Edit Branch Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Branch</DialogTitle>
            <DialogDescription>
              Update the branch details.
            </DialogDescription>
          </DialogHeader>
          {selectedBranch && (
            <BranchForm
              defaultValues={{
                name: selectedBranch.name,
                address: selectedBranch.address || '',
                phone: selectedBranch.phone ?
                  (selectedBranch.phone.startsWith('+91') ? selectedBranch.phone : `+91${selectedBranch.phone}`) :
                  '',
                email: selectedBranch.email || '',
                manager_name: selectedBranch.manager_name || '',
                notes: selectedBranch.notes || '',
              }}
              onSubmit={handleEditBranch}
              onCancel={() => setIsEditDialogOpen(false)}
              isSubmitting={isSubmitting}
              mode="edit"
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Delete Branch Confirmation */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 text-red-500 mr-2" />
              Delete Branch
            </AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the branch "{selectedBranch?.name}"?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isSubmitting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteBranch}
              disabled={isSubmitting}
              className="bg-red-500 hover:bg-red-600"
            >
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                <>
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </>
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

// Using the imported Badge component from UI

export default BranchManager;