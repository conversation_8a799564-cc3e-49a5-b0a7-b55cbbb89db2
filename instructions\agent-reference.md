# Agent Reference Code Implementation

This document provides step-by-step instructions for implementing company-specific agent reference codes in the FinancialTracker system.

## Overview

Agent reference codes are unique identifiers for agents within a company, following a similar pattern to collection IDs. The format is:

```
[COMPANY_PREFIX]-[SEQUENTIAL_NUMBER]
```

For example:
- "GS-001" for the first agent in "GOVINDARAJI S" company
- "CS-001" for the first agent in "Cloud Stier" company

The implementation consists of:
1. Adding a new column to the database
2. Creating a function to generate company prefixes
3. Creating a function to determine the next sequential number
4. Updating the agent creation process to generate and store reference codes
5. Updating the UI to display the reference codes

## Implementation Details

### 1. Database Schema Changes

#### SQL Migration
Create a migration file (e.g., `migrations/005_add_agent_reference_code.sql`) with the following SQL:

```sql
-- Add agent_reference_code column to agents table
ALTER TABLE agents ADD COLUMN IF NOT EXISTS agent_reference_code TEXT;

-- Create an index on agent_reference_code for faster lookups
CREATE INDEX IF NOT EXISTS idx_agents_reference_code ON agents(agent_reference_code);
```

#### Schema Definition Update
Update the agents table definition in `shared/schema.ts`:

```typescript
// Agents
export const agents = pgTable('agents', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  user_id: integer('user_id').references(() => users.id, { onDelete: 'cascade' }).notNull(),
  commission_rate: numeric('commission_rate', { precision: 5, scale: 2 }).notNull(),
  territory: text('territory'),
  notes: text('notes'),
  agent_reference_code: text('agent_reference_code'), // Add this line
  active: boolean('active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

#### Apply Migration
Run the migration using Drizzle's migration system:

```bash
npm run db:push
```

### 2. Company Prefix Generation

The company prefix is generated from the company name using the following rules:

- For a single word: First letter + Last letter (e.g., "Cloudstier" → "CR")
- For multiple words: First letter of first word + First letter of last word (e.g., "GOVINDARAJI S" → "GS")
- All letters are converted to uppercase for consistency

#### Implementation Steps:

1. Create or update the `getCompanyName` function in `server/routes.ts`:

```typescript
// Helper to get company name from company_id
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of last word
      // This handles cases like "GOVINDARAJI S" correctly
      prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    console.log(`Generated prefix: "${prefix}" for company name: "${fullName}"`);
    return prefix;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}`;
  }
}
```

2. Make sure the necessary imports are at the top of the file:

```typescript
import { db } from './db';
import { companies } from '@shared/schema';
import { eq } from 'drizzle-orm';

### 3. Sequential Number Generation

The sequential number is determined by finding the highest existing agent reference code for the company and incrementing it.

#### Implementation Steps:

1. Add the `getHighestAgentSerial` method to the `DatabaseStorage` class in `server/storage.ts`:

```typescript
async getHighestAgentSerial(companyId: number, companyPrefix: string): Promise<number> {
  try {
    // Get all agents for this company
    const agentsResult = await db
      .select()
      .from(agents)
      .where(eq(agents.company_id, companyId));

    if (!agentsResult || agentsResult.length === 0) {
      return 0; // No agents found, start with 1
    }

    // Extract the numeric part from agent_reference_code strings and find the highest
    let highestNumber = 0;
    for (const agent of agentsResult) {
      if (agent.agent_reference_code && agent.agent_reference_code.startsWith(companyPrefix)) {
        const parts = agent.agent_reference_code.split('-');
        if (parts.length === 2) {
          const numericPart = parseInt(parts[1], 10);
          if (!isNaN(numericPart) && numericPart > highestNumber) {
            highestNumber = numericPart;
          }
        }
      }
    }

    return highestNumber;
  } catch (error) {
    errorLogger.error('Error in getHighestAgentSerial', String(error));
    return 0;
  }
}
```

### 4. Agent Creation Process

When creating a new agent, the reference code is generated and stored in the agent record.

#### Implementation Steps:

1. Update the agent creation endpoint in `server/routes.ts` to generate and store the reference code:

```typescript
app.post('/api/agents', authMiddleware, async (req: AuthRequest, res: Response) => {
  try {
    // ... existing validation code ...

    // Now create the agent with the user ID
    let validatedAgentData;
    try {
      // Generate company-specific agent reference code
      const companyId = req.body.company_id;

      // Get company name for agent reference code prefix
      const companyName = await getCompanyName(companyId);
      console.log(`Generated company prefix for agent reference code: ${companyName}`);

      // Get the highest existing agent reference code for this company
      const highestSerial = await storage.getHighestAgentSerial(companyId, companyName);
      const nextSerial = highestSerial + 1;
      const serialString = nextSerial.toString().padStart(3, '0');
      const agentReferenceCode = `${companyName}-${serialString}`;

      console.log(`Generated agent reference code: ${agentReferenceCode} for company ${companyId}`);

      const agentData = {
        company_id: req.body.company_id,
        user_id: newUser.id,
        commission_rate: req.body.commission_rate,
        territory: req.body.territory,
        active: req.body.active !== false,
        notes: req.body.notes,
        agent_reference_code: agentReferenceCode
      };

      // ... continue with validation and agent creation ...
    }
    // ... rest of the function ...
  }
});
```

### 5. Frontend Display

The agent reference code needs to be displayed in both desktop and mobile views of the agents page.

#### Implementation Steps:

1. Update the desktop table view in `client/src/pages/agents.tsx`:

```tsx
<TableCell>
  <div className="font-medium">{agent.full_name}</div>
  <div className="text-xs text-muted-foreground">
    ID: {agent.id}
    {agent.agent_reference_code && (
      <span className="ml-2 text-xs font-medium text-primary">
        Ref: {agent.agent_reference_code}
      </span>
    )}
  </div>
</TableCell>
```

2. Update the mobile card view in the same file:

```tsx
<div className="flex items-start">
  <User className="h-4 w-4 mr-2 mt-0.5 text-muted-foreground" />
  <div className="space-y-1">
    <p className="text-sm font-medium">Agent ID</p>
    <p className="text-sm text-muted-foreground">
      {agent.id}
      {agent.agent_reference_code && (
        <span className="ml-2 text-xs font-medium text-primary">
          (Ref: {agent.agent_reference_code})
        </span>
      )}
    </p>
  </div>
</div>
```

3. Make sure the Agent type definition includes the agent_reference_code property:

```typescript
// In client/src/pages/agents.tsx or a shared types file
type Agent = {
  id: number;
  company_id: number;
  user_id: number;
  full_name: string;
  email: string;
  phone: string;
  commission_rate: number;
  territory?: string;
  active: boolean;
  notes?: string;
  agent_reference_code?: string; // Add this line
};
```

## Complete Implementation Process

Follow these steps to implement the agent reference code feature:

### 1. Database Migration

1. Create a migration file `migrations/005_add_agent_reference_code.sql`:
   ```sql
   -- Add agent_reference_code column to agents table
   ALTER TABLE agents ADD COLUMN IF NOT EXISTS agent_reference_code TEXT;

   -- Create an index on agent_reference_code for faster lookups
   CREATE INDEX IF NOT EXISTS idx_agents_reference_code ON agents(agent_reference_code);
   ```

2. Update the agents table schema in `shared/schema.ts` to include the new column:
   ```typescript
   agent_reference_code: text('agent_reference_code'),
   ```

3. Apply the migration using Drizzle's migration system:
   ```bash
   npm run db:push
   ```

### 2. Backend Implementation

1. Add the `getCompanyName` function to `server/routes.ts` to generate company prefixes
2. Add the `getHighestAgentSerial` method to the `DatabaseStorage` class in `server/storage.ts`
3. Update the agent creation endpoint in `server/routes.ts` to generate and store reference codes

### 3. Frontend Implementation

1. Update the agent type definition to include the `agent_reference_code` property
2. Modify the desktop and mobile views in `client/src/pages/agents.tsx` to display the reference codes

## Testing

Test the implementation by:

1. Creating new agents for different companies and verifying:
   - They receive sequential reference codes (001, 002, 003, etc.)
   - The prefix is correctly derived from the company name
   - The reference codes are displayed correctly in both desktop and mobile views

2. Test edge cases:
   - Companies with single-word names
   - Companies with multi-word names
   - Companies with special characters in names

## Troubleshooting

If you encounter issues:

1. **Database migration fails**:
   - Check database connection settings
   - Verify you have the correct permissions
   - Try running the SQL directly against the database

2. **Reference codes not generating correctly**:
   - Check the console logs for the generated prefix and reference code
   - Verify the company name is being retrieved correctly
   - Ensure the highest serial number is being calculated properly

3. **Reference codes not displaying in UI**:
   - Check the network response to ensure the reference codes are being returned from the API
   - Verify the conditional rendering in the UI components

## Future Improvements

1. Add search functionality to find agents by reference code
2. Add validation to ensure reference codes are unique within a company
3. Add the ability to manually set reference codes for special cases
4. Add bulk import functionality that automatically assigns sequential reference codes
