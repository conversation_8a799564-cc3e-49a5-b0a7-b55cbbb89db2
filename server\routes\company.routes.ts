import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, requireCompanyAccess, AuthRequest } from '../middleware/auth';
import { insertCompanySchema } from '../../shared/schema';
import { initializeSystemAccounts } from '../financialManagement';

export function registerCompanyRoutes(app: Express): void {
  // Create company (saas_admin only)
  app.post('/api/companies', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const result = insertCompanySchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const company = await storage.createCompany(result.data);

      // Initialize the Chart of Accounts system for this company
      try {
        await initializeSystemAccounts(company.id);
        console.log(`Initialized system accounts for new company ${company.id}`);
      } catch (accountsError) {
        console.error(`Failed to initialize system accounts for company ${company.id}:`, accountsError);
        // We don't fail the whole request if account initialization fails
      }

      return res.status(201).json(company);
    } catch (error) {
      console.error('Error creating company:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get all companies (saas_admin only)
  app.get('/api/companies', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companies = await storage.getCompanies();
      return res.json(companies);
    } catch (error) {
      console.error('Error fetching companies:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get company by ID
  app.get('/api/companies/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user.id);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      return res.json(company);
    } catch (error) {
      console.error(`Error fetching company ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Update company
  app.put('/api/companies/:id', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user.role !== 'saas_admin') {
        if (req.user.role !== 'company_admin' || req.user.company_id !== companyId) {
          return res.status(403).json({ message: 'Access denied' });
        }
      }

      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      // Validate input
      const result = insertCompanySchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const updatedCompany = await storage.updateCompany(companyId, result.data);

      return res.json(updatedCompany);
    } catch (error) {
      console.error(`Error updating company ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Delete company (saas_admin only)
  app.delete('/api/companies/:id', authMiddleware, requireRole(['saas_admin']), async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);

      const company = await storage.getCompany(companyId);

      if (!company) {
        return res.status(404).json({ message: 'Company not found' });
      }

      const result = await storage.deleteCompany(companyId);

      if (!result.success) {
        // Check if the company has customers
        if (result.customersCount && result.customersCount > 0) {
          return res.status(400).json({
            message: result.error,
            customersCount: result.customersCount,
            hint: 'Delete all associated customers before deleting this company'
          });
        }

        return res.status(400).json({ message: result.error || 'Failed to delete company' });
      }

      return res.json({ message: 'Company deleted successfully' });
    } catch (error) {
      console.error(`Error deleting company ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get company users
  app.get('/api/companies/:id/users', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);

      const users = await storage.getCompanyUsers(companyId);

      // Return users without sensitive information
      const safeUsers = users.map(user => ({
        id: user.id,
        username: user.username,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: user.company_id
      }));

      return res.json(safeUsers);
    } catch (error) {
      console.error(`Error fetching users for company ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get company settings
  app.get('/api/companies/:id/settings', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user?.id || 0);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // For now, return default settings - this can be expanded later
      const defaultSettings = {
        companyId,
        loanSettings: {
          defaultInterestRate: 12.0,
          maxLoanAmount: 100000,
          defaultLoanTerm: 12
        },
        collectionSettings: {
          gracePeriodDays: 7,
          lateFeePercentage: 2.0
        },
        notificationSettings: {
          emailNotifications: true,
          smsNotifications: false
        }
      };

      return res.json(defaultSettings);
    } catch (error) {
      console.error('Error fetching company settings:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get company branches
  app.get('/api/companies/:id/branches', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.id);

      // Check if user has access to this company
      if (req.user?.role !== 'saas_admin' && req.user?.company_id !== companyId) {
        const userCompanies = await storage.getUserCompanies(req.user?.id || 0);
        const hasAccess = userCompanies.some(uc => uc.company_id === companyId);

        if (!hasAccess) {
          return res.status(403).json({ message: 'Access denied to this company' });
        }
      }

      // For now, return empty array - this can be expanded when branches table is implemented
      return res.json([]);
    } catch (error) {
      console.error('Error fetching company branches:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
