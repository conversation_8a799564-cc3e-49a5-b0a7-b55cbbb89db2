import { db } from '../db';
import {
  approvalWorkflows,
  approvalWorkflowSteps,
  approvalWorkflowInstances,
  approvalWorkflowStepInstances,
  approvalActions,
  approvalEscalationRules,
  users,
  customRoles,
  type ApprovalWorkflow,
  type InsertApprovalWorkflow,
  type ApprovalWorkflowStep,
  type InsertApprovalWorkflowStep,
  type ApprovalWorkflowInstance,
  type InsertApprovalWorkflowInstance,
  type ApprovalWorkflowStepInstance,
  type InsertApprovalWorkflowStepInstance,
  type ApprovalAction,
  type InsertApprovalAction,
  type ApprovalEscalationRule,
  type InsertApprovalEscalationRule,
  type ApprovalWorkflowType,
  type ApprovalStepType,
  type ApprovalActionType,
  type WorkflowStatus,
  type StepStatus,
} from '../../shared/schema';
import { eq, and, or, desc, asc, inArray, isNull, lt, gte } from 'drizzle-orm';
import errorLogger from '../utils/errorLogger';

export interface WorkflowTriggerContext {
  requestType: string;
  requestId: string;
  requesterId: number;
  companyId: number;
  requestData: any;
  priority?: 'low' | 'medium' | 'high' | 'emergency';
  metadata?: Record<string, any>;
}

export interface WorkflowExecutionResult {
  workflowInstance: ApprovalWorkflowInstance;
  currentStep: ApprovalWorkflowStepInstance | null;
  nextApprovers: number[];
  requiresAction: boolean;
  message: string;
}

export interface ApprovalDecision {
  action: ApprovalActionType;
  comments?: string;
  delegatedTo?: number;
  actionData?: Record<string, any>;
}

export interface WorkflowStepConfig {
  stepName: string;
  stepType: ApprovalStepType;
  requiredApprovers?: number;
  approverRoles?: number[];
  approverUsers?: number[];
  escalationRoles?: number[];
  stepTimeoutHours?: number;
  isOptional?: boolean;
  conditions?: Record<string, any>;
}

export interface WorkflowConfig {
  name: string;
  description?: string;
  workflowType: ApprovalWorkflowType;
  triggerConditions?: Record<string, any>;
  autoEscalationHours?: number;
  maxEscalationLevels?: number;
  steps: WorkflowStepConfig[];
}

export class ApprovalWorkflowService {
  /**
   * Create a new approval workflow template
   * @param companyId Company ID
   * @param createdBy User creating the workflow
   * @param config Workflow configuration
   * @returns Promise<ApprovalWorkflow> Created workflow
   */
  async createWorkflow(
    companyId: number,
    createdBy: number,
    config: WorkflowConfig
  ): Promise<ApprovalWorkflow> {
    try {
      // Validate that company and creator exist
      await this.validateCompanyAndUser(companyId, createdBy);

      // Create the workflow
      const [workflow] = await db
        .insert(approvalWorkflows)
        .values({
          company_id: companyId,
          name: config.name,
          description: config.description,
          workflow_type: config.workflowType,
          trigger_conditions: config.triggerConditions,
          auto_escalation_hours: config.autoEscalationHours || 24,
          max_escalation_levels: config.maxEscalationLevels || 3,
          created_by: createdBy,
        })
        .returning();

      // Create workflow steps
      for (let i = 0; i < config.steps.length; i++) {
        const step = config.steps[i];
        await db.insert(approvalWorkflowSteps).values({
          workflow_id: workflow.id,
          step_order: i + 1,
          step_name: step.stepName,
          step_type: step.stepType,
          required_approvers: step.requiredApprovers || 1,
          approver_roles: step.approverRoles,
          approver_users: step.approverUsers,
          escalation_roles: step.escalationRoles,
          step_timeout_hours: step.stepTimeoutHours || 24,
          is_optional: step.isOptional || false,
          conditions: step.conditions,
        });
      }

      errorLogger.logInfo(
        `Created approval workflow: ${workflow.name} (ID: ${workflow.id}) for company ${companyId}`,
        'approval-workflow-service'
      );

      return workflow;
    } catch (error) {
      errorLogger.logError(
        `Failed to create approval workflow for company ${companyId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Start a workflow for a specific request
   * @param context Workflow trigger context
   * @returns Promise<WorkflowExecutionResult> Workflow execution result
   */
  async startWorkflow(context: WorkflowTriggerContext): Promise<WorkflowExecutionResult> {
    try {
      // Find appropriate workflow for this request
      const workflow = await this.findWorkflowForRequest(context);
      if (!workflow) {
        throw new Error(`No workflow found for request type: ${context.requestType}`);
      }

      // Create workflow instance
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + (workflow.auto_escalation_hours || 24));

      const [workflowInstance] = await db
        .insert(approvalWorkflowInstances)
        .values({
          workflow_id: workflow.id,
          company_id: context.companyId,
          request_id: context.requestId,
          request_type: context.requestType,
          requester_id: context.requesterId,
          request_data: context.requestData,
          priority: context.priority || 'medium',
          expires_at: expiresAt,
          status: 'in_progress',
        })
        .returning();

      // Start the first step
      const result = await this.advanceWorkflow(workflowInstance.id);

      errorLogger.logInfo(
        `Started workflow instance ${workflowInstance.id} for request ${context.requestId}`,
        'approval-workflow-service'
      );

      return result;
    } catch (error) {
      errorLogger.logError(
        `Failed to start workflow for request ${context.requestId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Process an approval decision
   * @param workflowInstanceId Workflow instance ID
   * @param stepInstanceId Step instance ID
   * @param approverId User making the decision
   * @param decision Approval decision
   * @returns Promise<WorkflowExecutionResult> Updated workflow state
   */
  async processApprovalDecision(
    workflowInstanceId: number,
    stepInstanceId: number,
    approverId: number,
    decision: ApprovalDecision
  ): Promise<WorkflowExecutionResult> {
    try {
      // Validate the approval is allowed
      await this.validateApprovalPermission(stepInstanceId, approverId);

      // Record the approval action
      await db.insert(approvalActions).values({
        workflow_instance_id: workflowInstanceId,
        step_instance_id: stepInstanceId,
        approver_id: approverId,
        action: decision.action,
        comments: decision.comments,
        delegated_to: decision.delegatedTo,
        action_data: decision.actionData,
      });

      // Update step instance based on decision
      await this.updateStepInstanceForDecision(stepInstanceId, decision);

      // Check if workflow should advance
      const result = await this.advanceWorkflow(workflowInstanceId);

      errorLogger.logInfo(
        `Processed approval decision: ${decision.action} by user ${approverId} for workflow ${workflowInstanceId}`,
        'approval-workflow-service'
      );

      return result;
    } catch (error) {
      errorLogger.logError(
        `Failed to process approval decision for workflow ${workflowInstanceId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Get workflow instance with current status
   * @param workflowInstanceId Workflow instance ID
   * @returns Promise<WorkflowExecutionResult> Current workflow state
   */
  async getWorkflowStatus(workflowInstanceId: number): Promise<WorkflowExecutionResult> {
    try {
      const [workflowInstance] = await db
        .select()
        .from(approvalWorkflowInstances)
        .where(eq(approvalWorkflowInstances.id, workflowInstanceId))
        .limit(1);

      if (!workflowInstance) {
        throw new Error(`Workflow instance ${workflowInstanceId} not found`);
      }

      const currentStep = await this.getCurrentStepInstance(workflowInstanceId);
      const nextApprovers = currentStep ? await this.getStepApprovers(currentStep.id) : [];

      return {
        workflowInstance,
        currentStep,
        nextApprovers,
        requiresAction: currentStep?.status === 'in_progress',
        message: this.getWorkflowStatusMessage(workflowInstance, currentStep),
      };
    } catch (error) {
      errorLogger.logError(
        `Failed to get workflow status for ${workflowInstanceId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  // ==================== PRIVATE HELPER METHODS ====================

  private async validateCompanyAndUser(companyId: number, userId: number): Promise<void> {
    const [user] = await db
      .select({ id: users.id })
      .from(users)
      .where(and(eq(users.id, userId), eq(users.company_id, companyId)))
      .limit(1);

    if (!user) {
      throw new Error(`User ${userId} not found in company ${companyId}`);
    }
  }

  private async findWorkflowForRequest(context: WorkflowTriggerContext): Promise<ApprovalWorkflow | null> {
    const workflows = await db
      .select()
      .from(approvalWorkflows)
      .where(
        and(
          eq(approvalWorkflows.company_id, context.companyId),
          eq(approvalWorkflows.workflow_type, context.requestType as ApprovalWorkflowType),
          eq(approvalWorkflows.is_active, true)
        )
      )
      .orderBy(desc(approvalWorkflows.created_at));

    // For now, return the first active workflow
    // TODO: Implement trigger condition matching
    return workflows[0] || null;
  }

  private getWorkflowStatusMessage(
    workflowInstance: ApprovalWorkflowInstance,
    currentStep: ApprovalWorkflowStepInstance | null
  ): string {
    if (workflowInstance.status === 'approved') {
      return 'Workflow completed - Request approved';
    }
    if (workflowInstance.status === 'denied') {
      return 'Workflow completed - Request denied';
    }
    if (workflowInstance.status === 'cancelled') {
      return 'Workflow cancelled';
    }
    if (workflowInstance.status === 'expired') {
      return 'Workflow expired';
    }
    if (currentStep) {
      return `Pending approval at step: ${currentStep.step_order}`;
    }
    return 'Workflow in progress';
  }

  private async getCurrentStepInstance(workflowInstanceId: number): Promise<ApprovalWorkflowStepInstance | null> {
    const [stepInstance] = await db
      .select()
      .from(approvalWorkflowStepInstances)
      .where(
        and(
          eq(approvalWorkflowStepInstances.workflow_instance_id, workflowInstanceId),
          or(
            eq(approvalWorkflowStepInstances.status, 'pending'),
            eq(approvalWorkflowStepInstances.status, 'in_progress')
          )
        )
      )
      .orderBy(asc(approvalWorkflowStepInstances.step_order))
      .limit(1);

    return stepInstance || null;
  }

  private async getStepApprovers(stepInstanceId: number): Promise<number[]> {
    const [stepInstance] = await db
      .select()
      .from(approvalWorkflowStepInstances)
      .where(eq(approvalWorkflowStepInstances.id, stepInstanceId))
      .limit(1);

    if (!stepInstance || !stepInstance.assigned_to) {
      return [];
    }

    return Array.isArray(stepInstance.assigned_to) ? stepInstance.assigned_to : [];
  }

  private async validateApprovalPermission(stepInstanceId: number, approverId: number): Promise<void> {
    const [stepInstance] = await db
      .select()
      .from(approvalWorkflowStepInstances)
      .where(eq(approvalWorkflowStepInstances.id, stepInstanceId))
      .limit(1);

    if (!stepInstance) {
      throw new Error(`Step instance ${stepInstanceId} not found`);
    }

    if (stepInstance.status !== 'in_progress') {
      throw new Error(`Step is not in progress, current status: ${stepInstance.status}`);
    }

    const assignedUsers = Array.isArray(stepInstance.assigned_to) ? stepInstance.assigned_to : [];
    if (!assignedUsers.includes(approverId)) {
      throw new Error(`User ${approverId} is not authorized to approve this step`);
    }
  }

  private async updateStepInstanceForDecision(
    stepInstanceId: number,
    decision: ApprovalDecision
  ): Promise<void> {
    const [stepInstance] = await db
      .select()
      .from(approvalWorkflowStepInstances)
      .where(eq(approvalWorkflowStepInstances.id, stepInstanceId))
      .limit(1);

    if (!stepInstance) {
      throw new Error(`Step instance ${stepInstanceId} not found`);
    }

    let updateData: Partial<ApprovalWorkflowStepInstance> = {};

    if (decision.action === 'approve') {
      const newApprovalsReceived = stepInstance.approvals_received + 1;
      updateData.approvals_received = newApprovalsReceived;

      // Check if step is complete
      if (newApprovalsReceived >= stepInstance.approvals_required) {
        updateData.status = 'completed';
        updateData.completed_at = new Date();
      }
    } else if (decision.action === 'deny') {
      updateData.status = 'completed';
      updateData.completed_at = new Date();
    } else if (decision.action === 'escalate') {
      updateData.status = 'escalated';
      updateData.escalated_at = new Date();
    }

    if (Object.keys(updateData).length > 0) {
      await db
        .update(approvalWorkflowStepInstances)
        .set(updateData)
        .where(eq(approvalWorkflowStepInstances.id, stepInstanceId));
    }
  }

  private async advanceWorkflow(workflowInstanceId: number): Promise<WorkflowExecutionResult> {
    const [workflowInstance] = await db
      .select()
      .from(approvalWorkflowInstances)
      .where(eq(approvalWorkflowInstances.id, workflowInstanceId))
      .limit(1);

    if (!workflowInstance) {
      throw new Error(`Workflow instance ${workflowInstanceId} not found`);
    }

    // Check current step status
    const currentStep = await this.getCurrentStepInstance(workflowInstanceId);

    if (!currentStep) {
      // No active step, check if we need to start the next step or complete workflow
      const nextStep = await this.getNextWorkflowStep(workflowInstanceId);

      if (nextStep) {
        // Start next step
        const stepInstance = await this.startWorkflowStep(workflowInstanceId, nextStep);
        const nextApprovers = await this.getStepApprovers(stepInstance.id);

        return {
          workflowInstance,
          currentStep: stepInstance,
          nextApprovers,
          requiresAction: true,
          message: `Step ${nextStep.step_order} started: ${nextStep.step_name}`,
        };
      } else {
        // Workflow complete
        await this.completeWorkflow(workflowInstanceId, 'approved');
        const updatedInstance = await this.getUpdatedWorkflowInstance(workflowInstanceId);

        return {
          workflowInstance: updatedInstance,
          currentStep: null,
          nextApprovers: [],
          requiresAction: false,
          message: 'Workflow completed successfully',
        };
      }
    }

    // Check if current step was denied
    if (currentStep.status === 'completed') {
      const hasApproval = currentStep.approvals_received >= currentStep.approvals_required;

      if (!hasApproval) {
        // Step was denied
        await this.completeWorkflow(workflowInstanceId, 'denied');
        const updatedInstance = await this.getUpdatedWorkflowInstance(workflowInstanceId);

        return {
          workflowInstance: updatedInstance,
          currentStep: null,
          nextApprovers: [],
          requiresAction: false,
          message: 'Workflow denied',
        };
      }
    }

    const nextApprovers = await this.getStepApprovers(currentStep.id);

    return {
      workflowInstance,
      currentStep,
      nextApprovers,
      requiresAction: currentStep.status === 'in_progress',
      message: this.getWorkflowStatusMessage(workflowInstance, currentStep),
    };
  }

  private async getNextWorkflowStep(workflowInstanceId: number): Promise<ApprovalWorkflowStep | null> {
    const [workflowInstance] = await db
      .select()
      .from(approvalWorkflowInstances)
      .where(eq(approvalWorkflowInstances.id, workflowInstanceId))
      .limit(1);

    if (!workflowInstance) {
      return null;
    }

    const [nextStep] = await db
      .select()
      .from(approvalWorkflowSteps)
      .where(
        and(
          eq(approvalWorkflowSteps.workflow_id, workflowInstance.workflow_id),
          eq(approvalWorkflowSteps.step_order, workflowInstance.current_step_order)
        )
      )
      .limit(1);

    return nextStep || null;
  }

  private async startWorkflowStep(
    workflowInstanceId: number,
    step: ApprovalWorkflowStep
  ): Promise<ApprovalWorkflowStepInstance> {
    // Get approvers for this step
    const approvers = await this.resolveStepApprovers(step);

    // Calculate timeout
    const timeoutAt = new Date();
    timeoutAt.setHours(timeoutAt.getHours() + step.step_timeout_hours);

    // Create step instance
    const [stepInstance] = await db
      .insert(approvalWorkflowStepInstances)
      .values({
        workflow_instance_id: workflowInstanceId,
        workflow_step_id: step.id,
        step_order: step.step_order,
        status: 'in_progress',
        assigned_to: approvers,
        started_at: new Date(),
        timeout_at: timeoutAt,
        approvals_required: step.required_approvers,
      })
      .returning();

    // Update workflow instance current step
    await db
      .update(approvalWorkflowInstances)
      .set({ current_step_order: step.step_order + 1 })
      .where(eq(approvalWorkflowInstances.id, workflowInstanceId));

    return stepInstance;
  }

  private async resolveStepApprovers(step: ApprovalWorkflowStep): Promise<number[]> {
    const approvers: number[] = [];

    // Add specific users
    if (step.approver_users && Array.isArray(step.approver_users)) {
      approvers.push(...step.approver_users);
    }

    // Add users from roles
    if (step.approver_roles && Array.isArray(step.approver_roles)) {
      const roleUsers = await db
        .select({ user_id: users.id })
        .from(users)
        .innerJoin(customRoles, eq(users.company_id, customRoles.company_id))
        .where(inArray(customRoles.id, step.approver_roles));

      approvers.push(...roleUsers.map(u => u.user_id));
    }

    // Remove duplicates
    return [...new Set(approvers)];
  }

  private async completeWorkflow(workflowInstanceId: number, finalDecision: 'approved' | 'denied'): Promise<void> {
    await db
      .update(approvalWorkflowInstances)
      .set({
        status: finalDecision,
        final_decision: finalDecision === 'approved' ? 'approve' : 'deny',
        completed_at: new Date(),
        final_decision_at: new Date(),
      })
      .where(eq(approvalWorkflowInstances.id, workflowInstanceId));
  }

  private async getUpdatedWorkflowInstance(workflowInstanceId: number): Promise<ApprovalWorkflowInstance> {
    const [instance] = await db
      .select()
      .from(approvalWorkflowInstances)
      .where(eq(approvalWorkflowInstances.id, workflowInstanceId))
      .limit(1);

    if (!instance) {
      throw new Error(`Workflow instance ${workflowInstanceId} not found`);
    }

    return instance;
  }

  // ==================== PUBLIC WORKFLOW MANAGEMENT METHODS ====================

  /**
   * Get all workflows for a company
   * @param companyId Company ID
   * @param workflowType Optional workflow type filter
   * @returns Promise<ApprovalWorkflow[]> List of workflows
   */
  async getWorkflows(companyId: number, workflowType?: ApprovalWorkflowType): Promise<ApprovalWorkflow[]> {
    try {
      const whereConditions = [eq(approvalWorkflows.company_id, companyId)];

      if (workflowType) {
        whereConditions.push(eq(approvalWorkflows.workflow_type, workflowType));
      }

      const workflows = await db
        .select()
        .from(approvalWorkflows)
        .where(and(...whereConditions))
        .orderBy(desc(approvalWorkflows.created_at));

      return workflows;
    } catch (error) {
      errorLogger.logError(
        `Failed to get workflows for company ${companyId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Get workflow with its steps
   * @param workflowId Workflow ID
   * @returns Promise<{workflow: ApprovalWorkflow, steps: ApprovalWorkflowStep[]}> Workflow with steps
   */
  async getWorkflowWithSteps(workflowId: number): Promise<{
    workflow: ApprovalWorkflow;
    steps: ApprovalWorkflowStep[];
  }> {
    try {
      const [workflow] = await db
        .select()
        .from(approvalWorkflows)
        .where(eq(approvalWorkflows.id, workflowId))
        .limit(1);

      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      const steps = await db
        .select()
        .from(approvalWorkflowSteps)
        .where(eq(approvalWorkflowSteps.workflow_id, workflowId))
        .orderBy(asc(approvalWorkflowSteps.step_order));

      return { workflow, steps };
    } catch (error) {
      errorLogger.logError(
        `Failed to get workflow ${workflowId} with steps`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Update workflow
   * @param workflowId Workflow ID
   * @param updates Workflow updates
   * @returns Promise<ApprovalWorkflow> Updated workflow
   */
  async updateWorkflow(
    workflowId: number,
    updates: Partial<InsertApprovalWorkflow>
  ): Promise<ApprovalWorkflow> {
    try {
      const [updated] = await db
        .update(approvalWorkflows)
        .set(updates)
        .where(eq(approvalWorkflows.id, workflowId))
        .returning();

      if (!updated) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      errorLogger.logInfo(
        `Updated workflow ${workflowId}`,
        'approval-workflow-service'
      );

      return updated;
    } catch (error) {
      errorLogger.logError(
        `Failed to update workflow ${workflowId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Delete workflow
   * @param workflowId Workflow ID
   * @returns Promise<boolean> Success status
   */
  async deleteWorkflow(workflowId: number): Promise<boolean> {
    try {
      // Check if workflow has active instances
      const [activeInstance] = await db
        .select({ id: approvalWorkflowInstances.id })
        .from(approvalWorkflowInstances)
        .where(
          and(
            eq(approvalWorkflowInstances.workflow_id, workflowId),
            or(
              eq(approvalWorkflowInstances.status, 'pending'),
              eq(approvalWorkflowInstances.status, 'in_progress')
            )
          )
        )
        .limit(1);

      if (activeInstance) {
        throw new Error('Cannot delete workflow with active instances');
      }

      await db
        .delete(approvalWorkflows)
        .where(eq(approvalWorkflows.id, workflowId));

      errorLogger.logInfo(
        `Deleted workflow ${workflowId}`,
        'approval-workflow-service'
      );

      return true;
    } catch (error) {
      errorLogger.logError(
        `Failed to delete workflow ${workflowId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Get pending approvals for a user
   * @param userId User ID
   * @param companyId Company ID
   * @returns Promise<ApprovalWorkflowStepInstance[]> Pending approvals
   */
  async getPendingApprovalsForUser(userId: number, companyId: number): Promise<ApprovalWorkflowStepInstance[]> {
    try {
      const pendingApprovals = await db
        .select()
        .from(approvalWorkflowStepInstances)
        .innerJoin(
          approvalWorkflowInstances,
          eq(approvalWorkflowStepInstances.workflow_instance_id, approvalWorkflowInstances.id)
        )
        .where(
          and(
            eq(approvalWorkflowInstances.company_id, companyId),
            eq(approvalWorkflowStepInstances.status, 'in_progress')
          )
        )
        .orderBy(desc(approvalWorkflowStepInstances.created_at));

      // Filter by user assignment
      return pendingApprovals
        .map(row => row.approval_workflow_step_instances)
        .filter(stepInstance => {
          const assignedUsers = Array.isArray(stepInstance.assigned_to) ? stepInstance.assigned_to : [];
          return assignedUsers.includes(userId);
        });
    } catch (error) {
      errorLogger.logError(
        `Failed to get pending approvals for user ${userId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Cancel workflow instance
   * @param workflowInstanceId Workflow instance ID
   * @param cancelledBy User cancelling the workflow
   * @param reason Cancellation reason
   * @returns Promise<boolean> Success status
   */
  async cancelWorkflow(
    workflowInstanceId: number,
    cancelledBy: number,
    reason?: string
  ): Promise<boolean> {
    try {
      await db
        .update(approvalWorkflowInstances)
        .set({
          status: 'cancelled',
          final_decision: 'deny',
          final_decision_by: cancelledBy,
          final_decision_at: new Date(),
          final_decision_notes: reason || 'Workflow cancelled',
          completed_at: new Date(),
        })
        .where(eq(approvalWorkflowInstances.id, workflowInstanceId));

      // Cancel all pending step instances
      await db
        .update(approvalWorkflowStepInstances)
        .set({ status: 'skipped' })
        .where(
          and(
            eq(approvalWorkflowStepInstances.workflow_instance_id, workflowInstanceId),
            or(
              eq(approvalWorkflowStepInstances.status, 'pending'),
              eq(approvalWorkflowStepInstances.status, 'in_progress')
            )
          )
        );

      errorLogger.logInfo(
        `Cancelled workflow instance ${workflowInstanceId} by user ${cancelledBy}`,
        'approval-workflow-service'
      );

      return true;
    } catch (error) {
      errorLogger.logError(
        `Failed to cancel workflow instance ${workflowInstanceId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  /**
   * Process escalation for timed-out steps
   * @param companyId Company ID
   * @returns Promise<number> Number of escalated steps
   */
  async processEscalations(companyId: number): Promise<number> {
    try {
      const now = new Date();

      // Find timed-out step instances
      const timedOutSteps = await db
        .select()
        .from(approvalWorkflowStepInstances)
        .innerJoin(
          approvalWorkflowInstances,
          eq(approvalWorkflowStepInstances.workflow_instance_id, approvalWorkflowInstances.id)
        )
        .where(
          and(
            eq(approvalWorkflowInstances.company_id, companyId),
            eq(approvalWorkflowStepInstances.status, 'in_progress'),
            lt(approvalWorkflowStepInstances.timeout_at, now)
          )
        );

      let escalatedCount = 0;

      for (const row of timedOutSteps) {
        const stepInstance = row.approval_workflow_step_instances;
        const workflowInstance = row.approval_workflow_instances;

        try {
          await this.escalateStep(stepInstance, workflowInstance);
          escalatedCount++;
        } catch (error) {
          errorLogger.logError(
            `Failed to escalate step ${stepInstance.id}`,
            error,
            'approval-workflow-service'
          );
        }
      }

      if (escalatedCount > 0) {
        errorLogger.logInfo(
          `Processed ${escalatedCount} escalations for company ${companyId}`,
          'approval-workflow-service'
        );
      }

      return escalatedCount;
    } catch (error) {
      errorLogger.logError(
        `Failed to process escalations for company ${companyId}`,
        error,
        'approval-workflow-service'
      );
      throw error;
    }
  }

  private async escalateStep(
    stepInstance: ApprovalWorkflowStepInstance,
    workflowInstance: ApprovalWorkflowInstance
  ): Promise<void> {
    // Get escalation rules for this workflow
    const escalationRules = await db
      .select()
      .from(approvalEscalationRules)
      .where(
        and(
          eq(approvalEscalationRules.company_id, workflowInstance.company_id),
          or(
            eq(approvalEscalationRules.workflow_id, workflowInstance.workflow_id),
            isNull(approvalEscalationRules.workflow_id) // Global rules
          ),
          eq(approvalEscalationRules.is_active, true)
        )
      );

    // For now, implement basic escalation - mark as escalated
    await db
      .update(approvalWorkflowStepInstances)
      .set({
        status: 'escalated',
        escalated_at: new Date(),
      })
      .where(eq(approvalWorkflowStepInstances.id, stepInstance.id));

    // Update workflow escalation level
    await db
      .update(approvalWorkflowInstances)
      .set({
        escalation_level: workflowInstance.escalation_level + 1,
        status: 'escalated',
      })
      .where(eq(approvalWorkflowInstances.id, workflowInstance.id));

    // TODO: Implement specific escalation actions based on rules
    // - Notify escalation targets
    // - Reassign to different approvers
    // - Auto-approve if configured
  }
}
