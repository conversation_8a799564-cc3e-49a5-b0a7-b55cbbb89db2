import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/api';
import { useToast } from '@/components/ui/use-toast';

export interface Role {
  id: number;
  name: string;
  description: string;
  company_id: number;
  is_system: boolean;
  created_at: string;
  updated_at: string;
}

export interface RoleHierarchyNode {
  role: Role;
  children: RoleHierarchyNode[];
  parents: RoleHierarchyNode[];
  inheritanceType?: 'inherit' | 'override' | 'deny';
  depth: number;
}

export interface RoleHierarchy {
  id: number;
  parent_role_id: number;
  child_role_id: number;
  inheritance_type: 'inherit' | 'override' | 'deny';
  created_at: string;
  updated_at: string;
}

export interface CreateHierarchyRequest {
  parent_role_id: number;
  child_role_id: number;
  inheritance_type: 'inherit' | 'override' | 'deny';
}

export interface UpdateHierarchyRequest {
  inheritance_type: 'inherit' | 'override' | 'deny';
}

export function useRoleHierarchy(companyId?: number) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Get role hierarchy tree
  const {
    data: hierarchyTree,
    isLoading: isLoadingTree,
    error: treeError,
  } = useQuery({
    queryKey: ['role-hierarchy', 'tree', companyId],
    queryFn: async (): Promise<RoleHierarchyNode[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest(`/api/role-hierarchy/tree${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch role hierarchy tree');
      }
      return response.json();
    },
    enabled: true,
  });

  // Get all roles for the company
  const {
    data: roles,
    isLoading: isLoadingRoles,
    error: rolesError,
  } = useQuery({
    queryKey: ['roles', companyId],
    queryFn: async (): Promise<Role[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest(`/api/roles${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch roles');
      }
      return response.json();
    },
    enabled: true,
  });

  // Get all hierarchy relationships
  const {
    data: hierarchyRelationships,
    isLoading: isLoadingRelationships,
    error: relationshipsError,
  } = useQuery({
    queryKey: ['role-hierarchy', 'relationships', companyId],
    queryFn: async (): Promise<RoleHierarchy[]> => {
      const params = companyId ? `?company_id=${companyId}` : '';
      const response = await apiRequest(`/api/role-hierarchy${params}`);
      if (!response.ok) {
        throw new Error('Failed to fetch role hierarchy relationships');
      }
      return response.json();
    },
    enabled: true,
  });

  // Create hierarchy relationship
  const createHierarchyMutation = useMutation({
    mutationFn: async (data: CreateHierarchyRequest): Promise<RoleHierarchy> => {
      const response = await apiRequest('/api/role-hierarchy', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to create hierarchy relationship');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-hierarchy'] });
      queryClient.invalidateQueries({ queryKey: ['roles'] });
      toast({
        title: 'Success',
        description: 'Role hierarchy relationship created successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Update hierarchy relationship
  const updateHierarchyMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: UpdateHierarchyRequest }): Promise<RoleHierarchy> => {
      const response = await apiRequest(`/api/role-hierarchy/${id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(data),
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to update hierarchy relationship');
      }
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-hierarchy'] });
      toast({
        title: 'Success',
        description: 'Role hierarchy relationship updated successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Delete hierarchy relationship
  const deleteHierarchyMutation = useMutation({
    mutationFn: async (id: number): Promise<void> => {
      const response = await apiRequest(`/api/role-hierarchy/${id}`, {
        method: 'DELETE',
      });
      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || 'Failed to delete hierarchy relationship');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['role-hierarchy'] });
      toast({
        title: 'Success',
        description: 'Role hierarchy relationship deleted successfully',
      });
    },
    onError: (error: Error) => {
      toast({
        title: 'Error',
        description: error.message,
        variant: 'destructive',
      });
    },
  });

  // Check for circular dependency
  const checkCircularDependency = async (parentId: number, childId: number): Promise<boolean> => {
    try {
      const response = await apiRequest(`/api/role-hierarchy/check-circular?parent_id=${parentId}&child_id=${childId}`);
      if (!response.ok) {
        return false;
      }
      const result = await response.json();
      return result.wouldCreateCircular;
    } catch (error) {
      console.error('Error checking circular dependency:', error);
      return false;
    }
  };

  return {
    // Data
    hierarchyTree,
    roles,
    hierarchyRelationships,
    
    // Loading states
    isLoading: isLoadingTree || isLoadingRoles || isLoadingRelationships,
    isLoadingTree,
    isLoadingRoles,
    isLoadingRelationships,
    
    // Errors
    error: treeError || rolesError || relationshipsError,
    treeError,
    rolesError,
    relationshipsError,
    
    // Mutations
    createHierarchy: createHierarchyMutation.mutate,
    updateHierarchy: updateHierarchyMutation.mutate,
    deleteHierarchy: deleteHierarchyMutation.mutate,
    
    // Mutation states
    isCreating: createHierarchyMutation.isPending,
    isUpdating: updateHierarchyMutation.isPending,
    isDeleting: deleteHierarchyMutation.isPending,
    
    // Utilities
    checkCircularDependency,
  };
}
