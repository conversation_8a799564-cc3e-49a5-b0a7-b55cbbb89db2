import { useEffect, useState } from "react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useAuth } from "@/lib/auth";
import { Loader2, UserIcon, Shield, Building, BellIcon, LockIcon, RefreshCcw, Building2, Plus, Pencil, Trash, Settings as SettingsIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";
import { CompanyManagement } from "@/components/company/CompanyManagement";
import { SystemSettings } from "@/components/settings/SystemSettings";
import NotificationSettings from "@/components/settings/NotificationSettings";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
  DialogFooter,
  DialogHeader,
  DialogTrigger,
  DialogClose
} from "@/components/ui/dialog";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow
} from "@/components/ui/table";

export default function Settings() {
  const { getCurrentUser, isAuthenticated } = useAuth();
  const user = getCurrentUser();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);
  const queryClient = useQueryClient();

  // Fetch the latest user data
  const {
    data: userData,
    isLoading: isUserDataLoading,
    refetch: refetchUserData
  } = useQuery({
    queryKey: [`/api/users/${user?.id}`],
    enabled: !!user?.id && isAuthenticated()
  });

  // Fetch company data
  const {
    data: companyData,
    isLoading: isCompanyLoading,
    refetch: refetchCompanyData
  } = useQuery({
    queryKey: [`/api/companies/${user?.company_id}`],
    enabled: !!user?.company_id && (user?.role === 'company_admin' || user?.role === 'saas_admin')
  });

  // Profile form state
  const [profileForm, setProfileForm] = useState({
    fullName: user?.full_name || "",
    email: user?.email || "",
    username: user?.username || "",
  });

  // Update form when user data is loaded
  useEffect(() => {
    if (userData) {
      setProfileForm({
        fullName: userData.full_name || "",
        email: userData.email || "",
        username: userData.username || "",
      });
    }
  }, [userData]);

  // Password form state
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  // Company form state
  const [companyForm, setCompanyForm] = useState({
    name: "",
    email: "",
    phone: "",
    address: "",
    website: "",
  });

  // Update company form when company data is loaded
  useEffect(() => {
    if (companyData) {
      setCompanyForm({
        name: companyData.name || "",
        email: companyData.email || "",
        phone: companyData.phone || "",
        address: companyData.address || "",
        website: companyData.website || "",
      });
    }
  }, [companyData]);

  const handleProfileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm((prev) => ({ ...prev, [name]: value }));
  };

  const handlePasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCompanyChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCompanyForm((prev) => ({ ...prev, [name]: value }));
  };

  const handleCompanySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      // Use apiRequest from queryClient for consistent authentication handling
      const response = await apiRequest(
        'PATCH',
        `/api/companies/${user?.company_id}`,
        companyForm
      );

      const updatedCompany = await response.json();

      // Update any cached user data if it contains company information
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${user?.company_id}`] });

      toast({
        title: "Company information updated",
        description: "Your company information has been updated successfully.",
      });

    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An error occurred while updating company information",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const saveProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    try {
      const response = await apiRequest(
        'PATCH',
        `/api/users/${user?.id}`,
        {
          full_name: profileForm.fullName,
          email: profileForm.email,
          username: profileForm.username
        }
      );

      queryClient.invalidateQueries({ queryKey: [`/api/users/${user?.id}`] });

      toast({
        title: "Profile updated",
        description: "Your profile information has been updated successfully.",
      });

    } catch (error) {
      toast({
        title: "Update failed",
        description: error instanceof Error ? error.message : "An error occurred while updating your profile",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const changePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Passwords don't match",
        description: "New password and confirmation do not match.",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const response = await apiRequest(
        'POST',
        `/api/users/${user?.id}/change-password`,
        {
          current_password: passwordForm.currentPassword,
          new_password: passwordForm.newPassword
        }
      );

      toast({
        title: "Password changed",
        description: "Your password has been changed successfully. You will be logged out for security reasons.",
      });

      setPasswordForm({
        currentPassword: "",
        newPassword: "",
        confirmPassword: "",
      });

      // Log out the user after 2 seconds to allow them to see the success message
      setTimeout(() => {
        // Clear auth token
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');

        // Redirect to login page
        window.location.href = '/login';
      }, 2000);

    } catch (error) {
      toast({
        title: "Password change failed",
        description: error instanceof Error ? error.message : "An error occurred while changing your password",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Account Settings</h1>

      <Tabs defaultValue="profile" className="w-full">
        <TabsList className="mb-6">
          <TabsTrigger value="profile" className="flex items-center gap-2">
            <UserIcon className="h-4 w-4" />
            <span>Profile</span>
          </TabsTrigger>
          <TabsTrigger value="security" className="flex items-center gap-2">
            <LockIcon className="h-4 w-4" />
            <span>Security</span>
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <BellIcon className="h-4 w-4" />
            <span>Notifications</span>
          </TabsTrigger>
          {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
            <TabsTrigger value="company" className="flex items-center gap-2">
              <Building className="h-4 w-4" />
              <span>Company</span>
            </TabsTrigger>
          )}
          {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
            <TabsTrigger value="branches" className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <span>Branches</span>
            </TabsTrigger>
          )}
          {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
            <TabsTrigger value="system" className="flex items-center gap-2">
              <SettingsIcon className="h-4 w-4" />
              <span>System</span>
            </TabsTrigger>
          )}
          {user?.role === 'saas_admin' && (
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              <span>User Roles</span>
            </TabsTrigger>
          )}
        </TabsList>

        <TabsContent value="profile">
          <Card>
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>
                Update your personal information and contact details
              </CardDescription>
            </CardHeader>
            <form onSubmit={saveProfile}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="fullName">Full Name</Label>
                  <Input
                    id="fullName"
                    name="fullName"
                    value={profileForm.fullName}
                    onChange={handleProfileChange}
                    placeholder="Your full name"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={profileForm.email}
                    onChange={handleProfileChange}
                    placeholder="Your email address"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    name="username"
                    value={profileForm.username}
                    onChange={handleProfileChange}
                    placeholder="Your username"
                  />
                </div>

                {user?.role && (
                  <div className="space-y-2">
                    <Label htmlFor="role">Role</Label>
                    <Input
                      id="role"
                      value={user.role.replace('_', ' ')}
                      readOnly
                      disabled
                      className="capitalize bg-gray-100"
                    />
                  </div>
                )}
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    "Save Changes"
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="security">
          <Card>
            <CardHeader>
              <CardTitle>Change Password</CardTitle>
              <CardDescription>
                Update your password to keep your account secure
              </CardDescription>
            </CardHeader>
            <form onSubmit={changePassword}>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="currentPassword">Current Password</Label>
                  <Input
                    id="currentPassword"
                    name="currentPassword"
                    type="password"
                    value={passwordForm.currentPassword}
                    onChange={handlePasswordChange}
                    placeholder="Your current password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="newPassword">New Password</Label>
                  <Input
                    id="newPassword"
                    name="newPassword"
                    type="password"
                    value={passwordForm.newPassword}
                    onChange={handlePasswordChange}
                    placeholder="Your new password"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="confirmPassword">Confirm New Password</Label>
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type="password"
                    value={passwordForm.confirmPassword}
                    onChange={handlePasswordChange}
                    placeholder="Confirm your new password"
                  />
                </div>
              </CardContent>
              <CardFooter>
                <Button type="submit" disabled={isLoading}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Updating...
                    </>
                  ) : (
                    "Change Password"
                  )}
                </Button>
              </CardFooter>
            </form>
          </Card>
        </TabsContent>

        <TabsContent value="notifications">
          <NotificationSettings />
        </TabsContent>

        {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
          <TabsContent value="company">
            <CompanyManagement />
          </TabsContent>
        )}

        {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
          <TabsContent value="branches">
            <BranchManagement />
          </TabsContent>
        )}

        {(user?.role === 'company_admin' || user?.role === 'saas_admin') && (
          <TabsContent value="system">
            <SystemSettings />
          </TabsContent>
        )}

        {user?.role === 'saas_admin' && (
          <TabsContent value="roles">
            <Card>
              <CardHeader>
                <CardTitle>User Roles Management</CardTitle>
                <CardDescription>
                  Manage permissions and access levels for different user roles
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-gray-500">
                  This feature is coming soon. You will be able to manage user roles and permissions here.
                </p>
              </CardContent>
            </Card>
          </TabsContent>
        )}
      </Tabs>
    </div>
  );
}

// Branch Management Component
function BranchManagement() {
  const { getCurrentUser } = useAuth();
  const user = getCurrentUser();
  const companyId = user?.company_id;
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [selectedBranch, setSelectedBranch] = useState<any>(null);
  const [branchForm, setBranchForm] = useState({
    name: '',
    address: '',
    phone: '',
    email: '',
    is_main_branch: false
  });

  // Fetch branches
  const { data: branches, isLoading } = useQuery({
    queryKey: [`/api/companies/${companyId}/branches`],
    enabled: !!companyId,
  });

  // Create branch mutation
  const createBranch = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('POST', '/api/branches', {
        ...data,
        company_id: companyId
      });
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/branches`] });
      toast({
        title: "Branch Created",
        description: "The branch has been created successfully."
      });
      setIsAddModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create branch.",
        variant: "destructive"
      });
    }
  });

  // Update branch mutation
  const updateBranch = useMutation({
    mutationFn: async (data: any) => {
      const response = await apiRequest('PATCH', `/api/branches/${selectedBranch.id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/branches`] });
      toast({
        title: "Branch Updated",
        description: "The branch has been updated successfully."
      });
      setIsEditModalOpen(false);
      resetForm();
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to update branch.",
        variant: "destructive"
      });
    }
  });

  // Delete branch mutation
  const deleteBranch = useMutation({
    mutationFn: async () => {
      const response = await apiRequest('DELETE', `/api/branches/${selectedBranch.id}`);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/branches`] });
      toast({
        title: "Branch Deleted",
        description: "The branch has been deleted successfully."
      });
      setIsDeleteModalOpen(false);
    },
    onError: (error: any) => {
      toast({
        title: "Error",
        description: error.message || "Failed to delete branch. It may have related data that needs to be moved first.",
        variant: "destructive"
      });
    }
  });

  const resetForm = () => {
    setBranchForm({
      name: '',
      address: '',
      phone: '',
      email: '',
      is_main_branch: false
    });
    setSelectedBranch(null);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;
    setBranchForm({
      ...branchForm,
      [name]: type === 'checkbox' ? (e.target as HTMLInputElement).checked : value
    });
  };

  const handleAddSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    createBranch.mutate(branchForm);
  };

  const handleEditSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    updateBranch.mutate(branchForm);
  };

  const handleDeleteConfirm = () => {
    deleteBranch.mutate();
  };

  const openEditModal = (branch: any) => {
    setSelectedBranch(branch);
    setBranchForm({
      name: branch.name,
      address: branch.address || '',
      phone: branch.phone || '',
      email: branch.email || '',
      is_main_branch: branch.is_main_branch || false
    });
    setIsEditModalOpen(true);
  };

  const openDeleteModal = (branch: any) => {
    setSelectedBranch(branch);
    setIsDeleteModalOpen(true);
  };

  return (
    <>
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <div>
            <CardTitle>Branch Management</CardTitle>
            <CardDescription>
              Manage your company's branches across different locations
            </CardDescription>
          </div>
          <Button
            onClick={() => {
              resetForm();
              setIsAddModalOpen(true);
            }}
            className="flex items-center gap-1"
          >
            <Plus size={16} />
            Add Branch
          </Button>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-6">
              <Loader2 className="h-6 w-6 animate-spin" />
            </div>
          ) : branches?.length > 0 ? (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Address</TableHead>
                  <TableHead>Contact</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {branches.map((branch: any) => (
                  <TableRow key={branch.id}>
                    <TableCell className="font-medium">{branch.name}</TableCell>
                    <TableCell>{branch.address || "—"}</TableCell>
                    <TableCell>
                      {branch.phone && (
                        <div>{branch.phone}</div>
                      )}
                      {branch.email && (
                        <div className="text-sm text-gray-500">{branch.email}</div>
                      )}
                    </TableCell>
                    <TableCell>
                      {branch.is_main_branch ? (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                          Main Branch
                        </span>
                      ) : (
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          Branch
                        </span>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={() => openEditModal(branch)}
                        >
                          <Pencil className="h-4 w-4" />
                          <span className="sr-only">Edit</span>
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          className="h-8 w-8 p-0 text-red-600 hover:bg-red-50"
                          onClick={() => openDeleteModal(branch)}
                          disabled={branch.is_main_branch}
                        >
                          <Trash className="h-4 w-4" />
                          <span className="sr-only">Delete</span>
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500 mb-4">No branches have been created yet.</p>
              <Button
                variant="outline"
                onClick={() => setIsAddModalOpen(true)}
              >
                <Plus className="mr-2 h-4 w-4" />
                Create your first branch
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add Branch Modal */}
      <Dialog open={isAddModalOpen} onOpenChange={setIsAddModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Branch</DialogTitle>
            <DialogDescription>
              Create a new branch for your company. Fill in the branch details below.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleAddSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="name">Branch Name</Label>
                <Input
                  id="name"
                  name="name"
                  placeholder="Main Branch, Downtown Office, etc."
                  value={branchForm.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="address">Address</Label>
                <Textarea
                  id="address"
                  name="address"
                  placeholder="Full address"
                  value={branchForm.address}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone">Phone</Label>
                  <Input
                    id="phone"
                    name="phone"
                    placeholder="Phone number"
                    value={branchForm.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Email</Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="Email address"
                    value={branchForm.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="is_main_branch"
                  name="is_main_branch"
                  checked={branchForm.is_main_branch}
                  onChange={handleInputChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="is_main_branch" className="text-sm font-normal">
                  This is the main branch
                </Label>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsAddModalOpen(false)}
              >
                Cancel
              </Button>
                <Button
                type="submit"
                disabled={createBranch.isPending}
                onClick={async (e) => {
                  e.preventDefault();
                  createBranch.mutate(branchForm, {
                  onSuccess: () => {
                    window.location.reload();
                  }
                  });
                }}
                >
                {createBranch.isPending ? (
                  <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating...
                  </>
                ) : (
                  "Create Branch"
                )}
                </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Edit Branch Modal */}
      <Dialog open={isEditModalOpen} onOpenChange={setIsEditModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Branch</DialogTitle>
            <DialogDescription>
              Update the branch information.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditSubmit}>
            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <Label htmlFor="edit-name">Branch Name</Label>
                <Input
                  id="edit-name"
                  name="name"
                  placeholder="Branch name"
                  value={branchForm.name}
                  onChange={handleInputChange}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="edit-address">Address</Label>
                <Textarea
                  id="edit-address"
                  name="address"
                  placeholder="Full address"
                  value={branchForm.address}
                  onChange={handleInputChange}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="edit-phone">Phone</Label>
                  <Input
                    id="edit-phone"
                    name="phone"
                    placeholder="Phone number"
                    value={branchForm.phone}
                    onChange={handleInputChange}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="edit-email">Email</Label>
                  <Input
                    id="edit-email"
                    name="email"
                    type="email"
                    placeholder="Email address"
                    value={branchForm.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  id="edit-is_main_branch"
                  name="is_main_branch"
                  checked={branchForm.is_main_branch}
                  onChange={handleInputChange}
                  className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
                />
                <Label htmlFor="edit-is_main_branch" className="text-sm font-normal">
                  This is the main branch
                </Label>
              </div>
            </div>
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => setIsEditModalOpen(false)}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={updateBranch.isPending}>
                {updateBranch.isPending ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating...
                  </>
                ) : (
                  "Update Branch"
                )}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Modal */}
      <Dialog open={isDeleteModalOpen} onOpenChange={setIsDeleteModalOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Branch</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this branch? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <p className="py-4">
            Branch: <strong>{selectedBranch?.name}</strong>
          </p>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteModalOpen(false)}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteConfirm}
              disabled={deleteBranch.isPending}
            >
              {deleteBranch.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete Branch"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}