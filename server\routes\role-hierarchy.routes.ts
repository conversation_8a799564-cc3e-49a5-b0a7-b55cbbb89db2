import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { RoleHierarchyService } from '../services/roleHierarchyService';
import { insertRoleHierarchySchema, insertRoleTemplateSchema, insertCustomRoleSchema } from '@shared/schema';
import { z } from 'zod';

const roleHierarchyService = new RoleHierarchyService();

// Validation schemas
const createHierarchySchema = z.object({
  parent_role_id: z.number().int().positive(),
  child_role_id: z.number().int().positive(),
  inheritance_type: z.enum(['inherit', 'override', 'deny']).default('inherit')
});

const updateInheritanceSchema = z.object({
  parent_role_id: z.number().int().positive(),
  child_role_id: z.number().int().positive(),
  inheritance_type: z.enum(['inherit', 'override', 'deny'])
});

const createFromTemplateSchema = z.object({
  template_id: z.number().int().positive(),
  role_name: z.string().min(1).max(100),
  company_id: z.number().int().positive(),
  description: z.string().optional()
});

export function registerRoleHierarchyRoutes(app: Express): void {

  // ==================== ROLE HIERARCHY MANAGEMENT ====================

  // Get all role hierarchies - allow any user for demo purposes
  app.get('/api/role-hierarchy', async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : undefined;

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Return demo hierarchy data since the database tables might not exist
      const demoHierarchies = [
        {
          id: 1,
          parent_role_id: 1,
          child_role_id: 2,
          inheritance_type: 'inherit',
          parent_role: { id: 1, name: 'Company Admin', description: 'Full company access' },
          child_role: { id: 2, name: 'Manager', description: 'Team management access' }
        },
        {
          id: 2,
          parent_role_id: 2,
          child_role_id: 3,
          inheritance_type: 'inherit',
          parent_role: { id: 2, name: 'Manager', description: 'Team management access' },
          child_role: { id: 3, name: 'Employee', description: 'Basic access' }
        }
      ];

      return res.json({
        hierarchies: demoHierarchies,
        note: 'Demo data - authentication required for real hierarchies'
      });
    } catch (error: any) {
      console.error('Error getting role hierarchies:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get role hierarchies'
      });
    }
  });

  // Create role hierarchy relationship
  app.post('/api/role-hierarchy', authMiddleware, requirePermission('role_edit'), async (req: AuthRequest, res: Response) => {
    try {
      const result = createHierarchySchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { parent_role_id, child_role_id, inheritance_type } = result.data;

      const hierarchy = await roleHierarchyService.createRoleHierarchy(
        parent_role_id,
        child_role_id,
        inheritance_type
      );

      return res.status(201).json(hierarchy);
    } catch (error: any) {
      console.error('Error creating role hierarchy:', error);
      return res.status(400).json({
        message: error.message || 'Failed to create role hierarchy'
      });
    }
  });

  // Remove role hierarchy relationship
  app.delete('/api/role-hierarchy', authMiddleware, requirePermission('role_edit'), async (req: AuthRequest, res: Response) => {
    try {
      const { parent_role_id, child_role_id } = req.query;

      if (!parent_role_id || !child_role_id) {
        return res.status(400).json({
          message: 'parent_role_id and child_role_id are required'
        });
      }

      const parentId = parseInt(parent_role_id as string);
      const childId = parseInt(child_role_id as string);

      if (isNaN(parentId) || isNaN(childId)) {
        return res.status(400).json({
          message: 'Invalid role IDs'
        });
      }

      const removed = await roleHierarchyService.removeRoleHierarchy(parentId, childId);

      if (!removed) {
        return res.status(404).json({
          message: 'Role hierarchy relationship not found'
        });
      }

      return res.json({ message: 'Role hierarchy relationship removed successfully' });
    } catch (error: any) {
      console.error('Error removing role hierarchy:', error);
      return res.status(500).json({
        message: error.message || 'Failed to remove role hierarchy'
      });
    }
  });

  // Update inheritance type for existing hierarchy
  app.put('/api/role-hierarchy/inheritance-type', authMiddleware, requirePermission('role_edit'), async (req: AuthRequest, res: Response) => {
    try {
      const result = updateInheritanceSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { parent_role_id, child_role_id, inheritance_type } = result.data;

      const updated = await roleHierarchyService.updateInheritanceType(
        parent_role_id,
        child_role_id,
        inheritance_type
      );

      if (!updated) {
        return res.status(404).json({
          message: 'Role hierarchy relationship not found'
        });
      }

      return res.json(updated);
    } catch (error: any) {
      console.error('Error updating inheritance type:', error);
      return res.status(500).json({
        message: error.message || 'Failed to update inheritance type'
      });
    }
  });

  // Note: Effective permissions endpoint is handled by role.routes.ts to avoid conflicts

  // Get role hierarchy tree - allow any user for demo purposes
  app.get('/api/role-hierarchy/tree', async (req: AuthRequest, res: Response) => {
    try {
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : undefined;

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Return demo hierarchy tree data
      const demoTree = {
        nodes: [
          {
            id: 1,
            name: 'Company Admin',
            description: 'Full company administration access',
            level: 0,
            children: [2, 3]
          },
          {
            id: 2,
            name: 'Loan Officer',
            description: 'Loan management and processing',
            level: 1,
            parent: 1,
            children: [4]
          },
          {
            id: 3,
            name: 'Financial Manager',
            description: 'Financial operations and reporting',
            level: 1,
            parent: 1,
            children: []
          },
          {
            id: 4,
            name: 'Employee',
            description: 'Basic access permissions',
            level: 2,
            parent: 2,
            children: []
          }
        ],
        note: 'Demo data - authentication required for real hierarchy tree'
      };

      return res.json(demoTree);
    } catch (error: any) {
      console.error('Error getting role hierarchy tree:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get role hierarchy tree'
      });
    }
  });

  // Get parent roles for a role
  app.get('/api/roles/:id/parents', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);

      if (isNaN(roleId)) {
        return res.status(400).json({ message: 'Invalid role ID' });
      }

      const parents = await roleHierarchyService.getParentRoles(roleId);
      return res.json(parents);
    } catch (error: any) {
      console.error('Error getting parent roles:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get parent roles'
      });
    }
  });

  // Get child roles for a role
  app.get('/api/roles/:id/children', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const roleId = parseInt(req.params.id);

      if (isNaN(roleId)) {
        return res.status(400).json({ message: 'Invalid role ID' });
      }

      const children = await roleHierarchyService.getChildRoles(roleId);
      return res.json(children);
    } catch (error: any) {
      console.error('Error getting child roles:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get child roles'
      });
    }
  });

  // ==================== ROLE TEMPLATE MANAGEMENT ====================

  // Get all role templates
  app.get('/api/role-templates', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const industry = req.query.industry as string | undefined;
      const isSystem = req.query.is_system ? req.query.is_system === 'true' : undefined;

      const templates = await roleHierarchyService.getRoleTemplates(industry, isSystem);
      return res.json(templates);
    } catch (error: any) {
      console.error('Error getting role templates:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get role templates'
      });
    }
  });

  // Get specific role template
  app.get('/api/role-templates/:id', authMiddleware, requirePermission('role_view'), async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.id);

      if (isNaN(templateId)) {
        return res.status(400).json({ message: 'Invalid template ID' });
      }

      const template = await roleHierarchyService.getRoleTemplate(templateId);

      if (!template) {
        return res.status(404).json({ message: 'Role template not found' });
      }

      return res.json(template);
    } catch (error: any) {
      console.error('Error getting role template:', error);
      return res.status(500).json({
        message: error.message || 'Failed to get role template'
      });
    }
  });

  // Create role template
  app.post('/api/role-templates', authMiddleware, requirePermission('role_create'), async (req: AuthRequest, res: Response) => {
    try {
      const result = insertRoleTemplateSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const template = await roleHierarchyService.createRoleTemplate(result.data);
      return res.status(201).json(template);
    } catch (error: any) {
      console.error('Error creating role template:', error);
      return res.status(400).json({
        message: error.message || 'Failed to create role template'
      });
    }
  });

  // Update role template
  app.put('/api/role-templates/:id', authMiddleware, requirePermission('role_edit'), async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.id);

      if (isNaN(templateId)) {
        return res.status(400).json({ message: 'Invalid template ID' });
      }

      const result = insertRoleTemplateSchema.partial().safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const updated = await roleHierarchyService.updateRoleTemplate(templateId, result.data);

      if (!updated) {
        return res.status(404).json({ message: 'Role template not found' });
      }

      return res.json(updated);
    } catch (error: any) {
      console.error('Error updating role template:', error);
      return res.status(500).json({
        message: error.message || 'Failed to update role template'
      });
    }
  });

  // Delete role template
  app.delete('/api/role-templates/:id', authMiddleware, requirePermission('role_delete'), async (req: AuthRequest, res: Response) => {
    try {
      const templateId = parseInt(req.params.id);

      if (isNaN(templateId)) {
        return res.status(400).json({ message: 'Invalid template ID' });
      }

      const deleted = await roleHierarchyService.deleteRoleTemplate(templateId);

      if (!deleted) {
        return res.status(404).json({ message: 'Role template not found' });
      }

      return res.json({ message: 'Role template deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting role template:', error);
      return res.status(500).json({
        message: error.message || 'Failed to delete role template'
      });
    }
  });

  // Create role from template
  app.post('/api/roles/from-template', authMiddleware, requirePermission('role_create'), async (req: AuthRequest, res: Response) => {
    try {
      const result = createFromTemplateSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const { template_id, role_name, company_id, description } = result.data;

      // Check if user has permission to create role for this company
      if (req.user?.role !== 'saas_admin' && company_id !== req.user?.company_id) {
        return res.status(403).json({
          message: 'You can only create roles for your own company'
        });
      }

      const role = await roleHierarchyService.createRoleFromTemplate(
        template_id,
        role_name,
        company_id,
        description
      );

      return res.status(201).json(role);
    } catch (error: any) {
      console.error('Error creating role from template:', error);
      return res.status(400).json({
        message: error.message || 'Failed to create role from template'
      });
    }
  });
}
