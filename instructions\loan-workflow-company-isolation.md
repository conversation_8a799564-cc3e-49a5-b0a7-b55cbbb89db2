# Loan Workflow and Company Data Isolation Documentation

## Overview

This document explains how the loan workflow ensures complete company data isolation in the FinancialTracker system. When a user logs in with Company ID 1, they can only access loans belonging to Company ID 1, ensuring multi-tenant security.

## Architecture Overview

```
User Authentication → Company Context → API Protection → Database Filtering → Response
```

## 1. Authentication & Company Context

### User Token Structure
When a user logs in, their JWT token contains:
```typescript
{
  userId: number,
  role: string,
  company_id: number  // Critical for company isolation
}
```

### Auth Middleware Implementation
**File:** `server/middleware/auth.ts`

```typescript
// Sets user context from JWT token
req.user = {
  id: user.id,
  role: user.role,
  company_id: user.company_id  // Company context from token
};
```

### Frontend Company Context
**File:** `client/src/pages/loans/index.tsx`

```typescript
const { companyId } = useContextData(); // Gets company ID from context
```

## 2. API Route Protection

### Company Access Middleware
**File:** `server/middleware/auth.ts`

The `requireCompanyAccess` middleware ensures users can only access their company's resources:

```typescript
export function requireCompanyAccess(req: AuthRequest, res: Response, next: NextFunction) {
  const companyId = parseInt(req.params.companyId || req.body.company_id, 10);

  // SaaS admin has access to all companies
  if (req.user.role === 'saas_admin') {
    return next();
  }

  // Check if user's token company_id matches requested company
  if (req.user.company_id === companyId) {
    return next();
  }

  // Additional check for multi-company users
  storage.getUserCompanies(req.user.id)
    .then(userCompanies => {
      const hasAccess = userCompanies.some(uc => uc.company_id === companyId);
      if (hasAccess) {
        return next();
      } else {
        return res.status(403).json({ message: 'Access denied to this company' });
      }
    });
}
```

### Loan Routes Protection
**File:** `server/routes/loan.routes.ts`

```typescript
// Get all loans for a company - Protected route
app.get('/api/companies/:companyId/loans',
  authMiddleware,           // Validates JWT token
  requireCompanyAccess,     // Validates company access
  async (req: AuthRequest, res: Response) => {
    const companyId = parseInt(req.params.companyId);
    const loans = await storage.getLoansByCompany(companyId);
    return res.json(loans);
  }
);
```

## 3. Database Layer Filtering

### Loan Storage Implementation
**File:** `server/storage/loan.storage.ts`

```typescript
async getLoansByCompany(companyId: number): Promise<Loan[]> {
  try {
    const result = await db
      .select({
        loan: loans,
        customer: {
          id: customers.id,
          full_name: customers.full_name
        }
      })
      .from(loans)
      .leftJoin(customers, eq(loans.customer_id, customers.id))
      .where(eq(loans.company_id, companyId));  // ✅ COMPANY FILTERING

    return result.map(row => ({
      ...row.loan,
      customer: row.customer.id ? row.customer : undefined
    }));
  } catch (error) {
    errorLogger.logError(`Error fetching loans for company id=${companyId}`, 'loan-fetch', error);
    return [];
  }
}
```

### Database Schema
**File:** `shared/schema.ts`

```typescript
export const loans = pgTable('loans', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id')
    .references(() => companies.id, { onDelete: 'cascade' })
    .notNull(),  // ✅ FOREIGN KEY ENSURES COMPANY ASSOCIATION
  customer_id: integer('customer_id')
    .references(() => customers.id, { onDelete: 'cascade' })
    .notNull(),
  // ... other fields
});
```

## 4. Frontend Implementation

### API Query Implementation
**File:** `client/src/pages/loans/index.tsx`

```typescript
// Fetch loans for specific company
const { data: loans = [], isLoading: isLoadingLoans } = useQuery<Loan[]>({
  queryKey: [`/api/companies/${companyId}/loans`],  // Company-specific endpoint
  enabled: !!companyId,
});
```

### Individual Loan Access
**File:** `client/src/pages/loans/[id].tsx`

```typescript
// Fetch individual loan with company context
const response = await apiRequest('GET', `/api/loans/${loanId}?companyId=${companyId}`);
```

## 5. Security Layers

### Layer 1: Authentication
- JWT token validation
- User existence verification
- Company context extraction

### Layer 2: Authorization
- Company access validation
- Role-based permissions
- Multi-company user support

### Layer 3: Database Filtering
- SQL-level company filtering
- Foreign key constraints
- Cascade delete protection

### Layer 4: Individual Resource Protection
**File:** `server/routes/loan.routes.ts`

```typescript
// Additional security for individual loan access
if (req.user!.role !== 'saas_admin' && loan.company_id !== companyId) {
  const userCompanies = await storage.getUserCompanies(userId);
  const hasAccess = userCompanies.some(uc => uc.company_id === loan.company_id);

  if (!hasAccess) {
    return res.status(403).json({ message: 'Access denied to this loan' });
  }
}
```

## 6. Complete Data Flow

### Loan List Workflow
```
1. User Login (Company ID 1)
   ↓
2. JWT Token: { userId: X, company_id: 1, role: 'user' }
   ↓
3. Frontend: GET /api/companies/1/loans
   ↓
4. authMiddleware: Validates token, sets req.user.company_id = 1
   ↓
5. requireCompanyAccess: Validates user can access company 1
   ↓
6. Database Query: SELECT * FROM loans WHERE company_id = 1
   ↓
7. Response: Only loans belonging to Company 1
```

### Individual Loan Access Workflow
```
1. Frontend: GET /api/loans/123?companyId=1
   ↓
2. authMiddleware: Validates token
   ↓
3. Database: SELECT * FROM loans WHERE id = 123
   ↓
4. Security Check: loan.company_id === user.company_id
   ↓
5. Response: Loan data (if authorized) or 403 Forbidden
```

## 7. Error Handling

### Company Access Denied
```json
{
  "message": "Access denied to this company",
  "status": 403
}
```

### Loan Access Denied
```json
{
  "message": "Access denied to this loan",
  "status": 403
}
```

### Invalid Company ID
```json
{
  "message": "Company ID is required",
  "status": 400
}
```

## 8. Testing Company Isolation

### Test Scenarios
1. **User A (Company 1)** should only see loans where `company_id = 1`
2. **User B (Company 2)** should only see loans where `company_id = 2`
3. **Cross-company access** should return 403 Forbidden
4. **SaaS Admin** should see all loans (with proper company context)

### Verification Points
- Database queries include `WHERE company_id = ?`
- API responses contain only company-specific data
- Frontend displays correct company context
- Error messages for unauthorized access

## 9. Related Components

### Similar Isolation Patterns
- **Customers:** `GET /api/companies/:companyId/customers`
- **Collections:** `GET /api/companies/:companyId/collections`
- **Transactions:** `GET /api/companies/:companyId/transactions`

### Middleware Dependencies
- `authMiddleware`: JWT validation
- `requireCompanyAccess`: Company authorization
- `requirePrefixSettings`: Company configuration validation

## 10. Best Practices

### Security Guidelines
1. Always use company-specific API endpoints
2. Validate company access at middleware level
3. Filter database queries by company_id
4. Double-check individual resource access
5. Use foreign key constraints for data integrity

### Performance Considerations
1. Index `company_id` columns for fast filtering
2. Use JOIN queries to reduce database calls
3. Implement proper caching strategies
4. Monitor query performance for large datasets

## Conclusion

The loan workflow implements comprehensive company data isolation through multiple security layers:
- **Authentication** ensures valid users
- **Authorization** validates company access
- **Database filtering** enforces data separation
- **Individual checks** provide additional security

This multi-layered approach ensures that Company ID 1 users can only access Company ID 1 loan data, maintaining strict tenant isolation in the multi-tenant system.

## Implementation Status for Other Entities

### Transactions Workflow
The transactions list has been updated to follow the same pattern as loans:
- ✅ Company-specific API endpoints: `/api/companies/:companyId/transactions`
- ✅ Same middleware protection: `authMiddleware` + `requireCompanyAccess`
- ✅ Database filtering by `company_id`
- ✅ Enhanced JOIN operations with accounts table
- ✅ Individual transaction access: `/api/companies/:companyId/transactions/:id`
- ✅ Consistent error handling and company isolation
- ✅ Server-side pagination with filtering
- ✅ Enhanced search and filtering capabilities
- ✅ Account information display (similar to customer info in loans)

**Documentation:** See `instructions/transaction-workflow-company-isolation.md` for detailed implementation.
