import { db } from '../db';
import { eq } from 'drizzle-orm';
import { users } from '@shared/schema';
import { User, InsertUser } from '@shared/schema';
import bcrypt from 'bcrypt';
import errorLogger from '../utils/errorLogger';
import { IUserStorage } from './interfaces';

export class UserStorage implements IUserStorage {
  async getUser(id: number): Promise<User | undefined> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.id, id));
      return user;
    } catch (error) {
      errorLogger.logError(`Error fetching user id=${id}`, 'user-fetch', error as Error);
      return undefined;
    }
  }



  async getUserByEmail(email: string): Promise<User | undefined> {
    try {
      const [user] = await db.select()
        .from(users)
        .where(eq(users.email, email));
      return user;
    } catch (error) {
      errorLogger.logError(`Error fetching user by email=${email}`, 'user-fetch', error as Error);
      return undefined;
    }
  }

  async createUser(userData: InsertUser): Promise<User> {
    try {
      // Hash the password
      const hashedPassword = await bcrypt.hash(userData.password, 10);

      // Create the user with the hashed password
      const [user] = await db.insert(users)
        .values({
          ...userData,
          password: hashedPassword
        })
        .returning();

      return user;
    } catch (error) {
      errorLogger.logError(`Error creating user`, 'user-create', error as Error);
      throw error;
    }
  }

  async updateUser(id: number, userData: Partial<InsertUser>): Promise<User> {
    try {
      // If password is being updated, hash it
      if (userData.password) {
        userData.password = await bcrypt.hash(userData.password, 10);
      }

      // Update the user
      const [updatedUser] = await db.update(users)
        .set(userData)
        .where(eq(users.id, id))
        .returning();

      return updatedUser;
    } catch (error) {
      errorLogger.logError(`Error updating user id=${id}`, 'user-update', error as Error);
      throw error;
    }
  }

  async getUsersByCompany(companyId: number): Promise<User[]> {
    try {
      const companyUsers = await db.select()
        .from(users)
        .where(eq(users.company_id, companyId));
      return companyUsers;
    } catch (error) {
      errorLogger.logError(`Error fetching users for company id=${companyId}`, 'user-fetch', error as Error);
      return [];
    }
  }

  async updateUserPassword(id: number, newPassword: string): Promise<User> {
    try {
      // Hash the new password
      const hashedPassword = await bcrypt.hash(newPassword, 10);

      // Update the user's password
      const [updatedUser] = await db.update(users)
        .set({ password: hashedPassword })
        .where(eq(users.id, id))
        .returning();

      return updatedUser;
    } catch (error) {
      errorLogger.logError(`Error updating password for user id=${id}`, 'user-password-update', error as Error);
      throw error;
    }
  }

  async getUsers(): Promise<User[]> {
    try {
      const allUsers = await db.select().from(users);
      return allUsers;
    } catch (error) {
      errorLogger.logError('Error fetching all users', 'users-fetch', error as Error);
      return [];
    }
  }
}
