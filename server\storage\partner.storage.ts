import { eq, and } from 'drizzle-orm';
import * as schema from '../../shared/schema';
import { Partner, InsertPartner } from '../../shared/schema';
import errorLogger from '../utils/errorLogger';
import { IPartnerStorage } from './interfaces';
import { pool } from '../db';

// Get the partners table from the schema
const { partners } = schema;

export class PartnerStorage implements IPartnerStorage {
  async getHighestPartnerSerial(companyId: number, companyPrefix: string): Promise<number> {
    try {
      console.log(`PartnerStorage.getHighestPartnerSerial called with companyId=${companyId}, prefix=${companyPrefix}`);

      // Get all partners for this company
      const query = `
        SELECT * FROM partners
        WHERE company_id = $1
      `;

      const result = await pool.query(query, [companyId]);

      if (!result.rows || result.rows.length === 0) {
        return 0; // No partners found, start with 1
      }

      // Extract the numeric part from partner_reference_code strings and find the highest
      let highestNumber = 0;
      for (const partner of result.rows) {
        if (partner.partner_reference_code && partner.partner_reference_code.startsWith(companyPrefix)) {
          const parts = partner.partner_reference_code.split('-');
          if (parts.length === 2) {
            const numericPart = parseInt(parts[1], 10);
            if (!isNaN(numericPart) && numericPart > highestNumber) {
              highestNumber = numericPart;
            }
          }
        }
      }

      console.log(`Highest partner serial for company ${companyId} with prefix ${companyPrefix}: ${highestNumber}`);
      return highestNumber;
    } catch (error) {
      console.error('Error in getHighestPartnerSerial', error);
      errorLogger.logError(`Error getting highest partner serial for company ${companyId}`, 'partner-serial', error as Error);
      return 0;
    }
  }

  async getPartner(id: number): Promise<Partner | undefined> {
    try {
      console.log(`PartnerStorage.getPartner called with id=${id}`);

      // Direct query to the database using the imported pool
      const query = `
        SELECT * FROM partners
        WHERE id = $1
      `;

      const result = await pool.query(query, [id]);
      if (result.rows.length > 0) {
        return result.rows[0];
      }
      return undefined;
    } catch (error) {
      console.error('Error in PartnerStorage.getPartner:', error);
      errorLogger.logError(`Error fetching partner id=${id}`, 'partner-fetch', error as Error);
      return undefined;
    }
  }

  async getPartnersByCompany(companyId: number): Promise<Partner[]> {
    try {
      console.log(`PartnerStorage.getPartnersByCompany called with companyId=${companyId}`);

      // Direct query to the database using the imported pool
      const query = `
        SELECT * FROM partners
        WHERE company_id = $1
      `;

      const result = await pool.query(query, [companyId]);
      console.log(`Direct SQL query returned ${result.rows.length} partners`);

      return result.rows;
    } catch (error) {
      console.error(`Error fetching partners for company id=${companyId}:`, error);
      // Log the full error details
      if (error instanceof Error) {
        console.error('Error name:', error.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);
      } else {
        console.error('Unknown error type:', error);
      }

      errorLogger.logError(`Error fetching partners for company id=${companyId}`, 'partner-fetch', error as Error);
      return [];
    }
  }

  async createPartner(partnerData: InsertPartner): Promise<Partner> {
    try {
      console.log(`PartnerStorage.createPartner called with data:`, partnerData);

      // Prepare the query
      const keys = Object.keys(partnerData);
      const values = Object.values(partnerData);
      const placeholders = values.map((_, i) => `$${i + 1}`).join(', ');
      const columns = keys.join(', ');

      // Direct query to the database using the imported pool
      const query = `
        INSERT INTO partners (${columns})
        VALUES (${placeholders})
        RETURNING *
      `;

      const result = await pool.query(query, values);
      console.log(`Partner created with ID: ${result.rows[0].id}`);

      return result.rows[0];
    } catch (error) {
      console.error('Error in PartnerStorage.createPartner:', error);
      errorLogger.logError(`Error creating partner`, 'partner-create', error as Error);
      throw error;
    }
  }

  async updatePartner(id: number, companyId: number, partnerData: Partial<InsertPartner>): Promise<Partner> {
    try {
      console.log(`PartnerStorage.updatePartner called with id=${id}, companyId=${companyId}`);

      // First check if the partner exists and belongs to the company
      const checkQuery = `
        SELECT * FROM partners
        WHERE id = $1 AND company_id = $2
      `;

      const checkResult = await pool.query(checkQuery, [id, companyId]);

      if (checkResult.rows.length === 0) {
        throw new Error(`Partner with id=${id} not found for company id=${companyId}`);
      }

      // Prepare the update query
      const keys = Object.keys(partnerData);
      const values = Object.values(partnerData);

      if (keys.length === 0) {
        return checkResult.rows[0]; // Nothing to update
      }

      const setClause = keys.map((key, i) => `${key} = $${i + 3}`).join(', ');

      // Direct query to the database using the imported pool
      const updateQuery = `
        UPDATE partners
        SET ${setClause}
        WHERE id = $1 AND company_id = $2
        RETURNING *
      `;

      const updateResult = await pool.query(updateQuery, [id, companyId, ...values]);
      console.log(`Partner updated with ID: ${updateResult.rows[0].id}`);

      return updateResult.rows[0];
    } catch (error) {
      console.error('Error in PartnerStorage.updatePartner:', error);
      errorLogger.logError(`Error updating partner id=${id}`, 'partner-update', error as Error);
      throw error;
    }
  }

  async deletePartner(id: number, companyId: number): Promise<{ success: boolean, error?: string }> {
    try {
      console.log(`PartnerStorage.deletePartner called with id=${id}, companyId=${companyId}`);

      // First check if the partner exists and belongs to the company
      const checkQuery = `
        SELECT * FROM partners
        WHERE id = $1 AND company_id = $2
      `;

      const checkResult = await pool.query(checkQuery, [id, companyId]);

      if (checkResult.rows.length === 0) {
        return {
          success: false,
          error: `Partner with id=${id} not found for company id=${companyId}`
        };
      }

      // Delete the partner
      const deleteQuery = `
        DELETE FROM partners
        WHERE id = $1 AND company_id = $2
      `;

      await pool.query(deleteQuery, [id, companyId]);
      console.log(`Partner deleted with ID: ${id}`);

      return { success: true };
    } catch (error) {
      console.error('Error in PartnerStorage.deletePartner:', error);
      errorLogger.logError(`Error deleting partner id=${id}`, 'partner-delete', error as Error);
      return {
        success: false,
        error: error instanceof Error ? error.message : "Unknown error"
      };
    }
  }
}
