import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/lib/auth';
import { useToast } from '@/hooks/use-toast';
import { Company } from '@shared/schema';

// Types
export interface CompanyWithAccess {
  id: number;
  user_id: number;
  company_id: number;
  company: Company;
  is_primary: boolean;
  created_at: Date | string;
  updated_at?: Date | string;
  name?: string;
}

interface CompanyContextType {
  currentCompany: CompanyWithAccess | null;
  userCompanies: CompanyWithAccess[];
  isLoading: boolean;
  error: Error | null;
  switchCompany: (companyId: number, companyName: string, setAsPrimary?: boolean) => Promise<void>;
}

// Create context with default values
const defaultContext: CompanyContextType = {
  currentCompany: null,
  userCompanies: [],
  isLoading: false,
  error: null,
  switchCompany: async () => {}
};

// Create context
const CompanyContext = createContext<CompanyContextType>(defaultContext);

// Provider component
export const CompanyProvider = ({ children }: { children: ReactNode }) => {
  const auth = useAuth();
  const { toast } = useToast();
  const [currentCompany, setCurrentCompany] = useState<CompanyWithAccess | null>(null);
  const [userCompanies, setUserCompanies] = useState<CompanyWithAccess[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  // Track if component is mounted and last fetch time
  const isMounted = React.useRef(true);
  const lastFetch = React.useRef(0);
  const fetchIntervalMs = 60000; // Only fetch new data every 1 minute

  // Load company data once when the component mounts or when auth changes
  useEffect(() => {
    async function loadCompanies() {
      try {
        if (!isMounted.current) return;

        // Don't fetch too frequently
        const now = Date.now();
        if (now - lastFetch.current < fetchIntervalMs && userCompanies.length > 0) {
          console.log('Skipping companies fetch - too soon since last fetch');
          setIsLoading(false);
          return;
        }

        setIsLoading(true);
        const user = auth.getCurrentUser();

        if (!user) {
          setIsLoading(false);
          return;
        }

        console.log('Loading companies for user:', {
          userId: user.id,
          userCompanyId: user.company_id,
          userCompanyName: user.company_name
        });

        // Make sure auth.getUserCompanies exists and is callable
        if (typeof auth.getUserCompanies !== 'function') {
          console.error('getUserCompanies is not a function in auth object:', auth);
          setError(new Error('API function not available'));
          setIsLoading(false);
          return;
        }

        const companies = await auth.getUserCompanies(user.id);
        lastFetch.current = Date.now();

        if (!isMounted.current) return;

        console.log('Loaded companies:', companies.map(c => ({
          id: c.id,
          company_id: c.company_id,
          name: c.company?.name,
          is_primary: c.is_primary
        })));

        setUserCompanies(companies);

        // First priority: Find the primary company
        const primaryCompany = companies.find((company: CompanyWithAccess) => company.is_primary);

        // Second priority: Find the company that matches user.company_id
        const currentUserCompany = companies.find(
          (company: CompanyWithAccess) => company.company_id === user.company_id
        );

        console.log('Company selection logic:', {
          hasPrimaryCompany: !!primaryCompany,
          primaryCompanyId: primaryCompany?.company_id,
          hasCurrentUserCompany: !!currentUserCompany,
          currentUserCompanyId: currentUserCompany?.company_id,
          userCompanyId: user.company_id,
          totalCompanies: companies.length
        });

        // Set the current company based on priority
        if (primaryCompany) {
          console.log('Using primary company:', primaryCompany.company_id);
          setCurrentCompany(primaryCompany);
        } else if (currentUserCompany) {
          console.log('Using current user company:', currentUserCompany.company_id);
          setCurrentCompany(currentUserCompany);
        } else if (companies.length > 0) {
          console.log('Using first available company:', companies[0].company_id);
          setCurrentCompany(companies[0]);
        }
      } catch (err) {
        if (!isMounted.current) return;

        setError(err instanceof Error ? err : new Error('Failed to load companies'));
        toast({
          title: 'Error',
          description: 'Failed to load company information',
          variant: 'destructive',
        });
      } finally {
        if (isMounted.current) {
          setIsLoading(false);
        }
      }
    }

    loadCompanies();

    // Cleanup function
    return () => {
      isMounted.current = false;
    };
  }, [auth]); // Only re-run when auth changes

  const handleSwitchCompany = async (companyId: number, companyName: string, setAsPrimary: boolean = false) => {
    try {
      console.log('Switching company:', { companyId, companyName, setAsPrimary });

      // Find the user-company association for this company
      const userCompanyAssociation = userCompanies.find(
        (company: CompanyWithAccess) => company.company_id === companyId
      );

      if (!userCompanyAssociation) {
        console.error(`Could not find user-company association for company ID ${companyId}`);
        toast({
          title: 'Error',
          description: 'Could not find company association',
          variant: 'destructive',
        });
        return;
      }

      // If setting as primary, update the local state immediately for better UX
      if (setAsPrimary) {
        // Create a new array with updated is_primary flags
        const updatedCompanies = userCompanies.map((company: CompanyWithAccess) => ({
          ...company,
          is_primary: company.company_id === companyId
        }));

        setUserCompanies(updatedCompanies);

        // Also update the current company
        const updatedCurrentCompany = {
          ...userCompanyAssociation,
          is_primary: true
        };

        setCurrentCompany(updatedCurrentCompany);

        console.log('Updated local state for primary company:', {
          companyId,
          companyName,
          associationId: userCompanyAssociation.id
        });
      } else {
        // Just update the current company
        setCurrentCompany(userCompanyAssociation);
      }

      // Call the auth function to update the server and handle page reload
      await auth.switchCompany(companyId, companyName, setAsPrimary);
    } catch (err) {
      console.error('Error switching company:', err);
      toast({
        title: 'Error',
        description: 'Failed to switch company',
        variant: 'destructive',
      });
    }
  };

  // Return the JSX for provider
  return React.createElement(
    CompanyContext.Provider,
    {
      value: {
        currentCompany,
        userCompanies,
        isLoading,
        error,
        switchCompany: handleSwitchCompany
      }
    },
    children
  );
};

// Hook
export function useCompany() {
  const context = useContext(CompanyContext);
  return context;
}