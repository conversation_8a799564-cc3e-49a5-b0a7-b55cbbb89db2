# Company Switching Implementation Guide

This document outlines the implementation of company switching in the Financial Tracker application, including best practices and guidelines for handling company IDs.

## Overview

The application supports multi-tenancy through company switching, allowing users to access multiple companies they have permissions for. The implementation ensures consistent company context throughout the application and prevents issues with hardcoded company IDs.

## Key Components

### 1. Company Configuration

The centralized company configuration is defined in `client/src/config/companyConfig.ts`:

```typescript
// Default company ID to use when no company is selected
// Using 0 as a sentinel value to indicate "no company selected"
export const DEFAULT_COMPANY_ID = 0;

// Validation and utility functions
export function isValidCompanyId(companyId: number | undefined | null): boolean {
  // A valid company ID must be a positive integer
  return typeof companyId === 'number' && companyId > 0;
}

export function getSafeCompanyId(companyId: number | undefined | null): number | undefined {
  return isValidCompanyId(companyId) ? companyId : undefined;
}
```

### 2. Context Data Hook

The `useContextData` hook provides a consistent way to access the current company context:

```typescript
// Get the current company from company context, with fallback to user.company_id
const currentCompany = company.currentCompany || null;

// Derive companyId using a consistent approach
// We don't use a default value here - if no company is selected, it should be undefined
let derivedCompanyId = currentCompany?.company_id || user?.company_id;

// Ensure we're using a valid company ID
const validatedCompanyId = getSafeCompanyId(derivedCompanyId);

const companyId = validatedCompanyId;
```

### 3. Authentication and Company Switching

The `auth.ts` module handles company switching and ensures valid company IDs:

```typescript
const switchCompany = async (companyId: number, companyName: string, setAsPrimary: boolean = false): Promise<void> => {
  // Validate the company ID
  if (!isValidCompanyId(companyId)) {
    console.warn(`Attempted to switch to invalid company ID ${companyId}. Company ID must be a positive integer.`);
    toast({
      title: "Invalid Company",
      description: "Cannot switch to the selected company. Please try again or contact support.",
      variant: "destructive",
    });
    return;
  }

  // Rest of the implementation...
}
```

## Best Practices

### 1. Never Hardcode Company IDs

❌ **Incorrect:**
```typescript
// Hardcoded company ID
const companyId = 2;
```

✅ **Correct:**
```typescript
// Use the context data hook
const { companyId } = useContextData();
```

### 2. Always Validate Company IDs

When receiving a company ID from any source (URL parameters, localStorage, etc.), always validate it:

```typescript
import { isValidCompanyId, getSafeCompanyId } from '@/config/companyConfig';

// Validate company ID
if (!isValidCompanyId(receivedCompanyId)) {
  console.warn(`Invalid company ID: ${receivedCompanyId}`);
  // Handle the invalid company ID case
  return;
}

// Use the validated company ID
const validCompanyId = receivedCompanyId;
```

### 3. Use the Context Data Hook

Always use the `useContextData` hook to access the current company context:

```typescript
import { useContextData } from '@/lib/useContextData';

function MyComponent() {
  const { companyId, companyName, switchCompany } = useContextData();

  // Use companyId for API calls
  // ...
}
```

### 4. API Calls with Company Context

When making API calls, always include the company context:

```typescript
// Fetch data with company context
const { data } = useQuery({
  queryKey: ['/api/companies', companyId, 'resource'],
  queryFn: async () => {
    const response = await apiRequest('GET', `/api/companies/${companyId}/resource`);
    return response.json();
  },
  enabled: !!companyId
});
```

## Handling No Company Selected

In a multi-tenant application, there are cases where a user might not have a company selected yet. Here's how to handle this:

1. **Show Company Selection UI**: If `companyId` is undefined, show a company selection UI to prompt the user to select a company.

2. **Disable API Calls**: Make sure API calls are disabled when no company is selected by using the `enabled` property in React Query:

```typescript
const { data } = useQuery({
  queryKey: ['/api/companies', companyId, 'resource'],
  queryFn: async () => {
    const response = await apiRequest('GET', `/api/companies/${companyId}/resource`);
    return response.json();
  },
  enabled: !!companyId // Only run the query if companyId is defined
});
```

3. **Provide Clear User Feedback**: Show a message to the user explaining that they need to select a company to proceed.

## Troubleshooting

### Common Issues

1. **API Calls Failing**: If API calls are failing with 404 or 403 errors, check if the company ID is valid and the user has permission to access it.

2. **Access Denied Errors**: If you see access denied errors, ensure the user has permission to access the requested company.

3. **Company Context Not Available**: If company context is not available, the application will not make API calls. Ensure the user has selected a company or has company associations.

## Future Improvements

1. Implement a more robust permission system for company access
2. Add company selection to the user onboarding flow
3. Improve error handling for company switching
4. Add company-specific settings and preferences
5. Implement a company selection modal for users with multiple companies
6. Add a default company preference that users can set
