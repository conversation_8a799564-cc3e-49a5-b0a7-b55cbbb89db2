import { Switch, Route } from "wouter";
import AppLayout from "@/components/layout/AppLayout";
import Dashboard from "@/pages/dashboard";
import Login from "@/pages/login";
import Register from "@/pages/register";
import ResetPassword from "@/pages/reset-password";
import NotFound from "@/pages/not-found";
import Landing from "@/pages/landing";
import Agents from "@/pages/agents";
import Collections from "@/pages/collections";
import QuickPayment from "@/pages/collections/quick-payment";
import Customers from "@/pages/customers/index";
import CreateCustomer from "@/pages/customers/create";
import Loans from "@/pages/loans";
import LoanDetail from "@/pages/loans/[id]";
import CreateLoan from "@/pages/loans/create";
import QuickLoan from "@/pages/loans/quick-create";
import EditLoan from "@/pages/loans/edit";
import LoanTypeSelection from "@/pages/loans/loan-types";
// Dynamic form builder and configurations pages removed
// import LoanConfigurations from "@/pages/loan-configurations";
import Partners from "@/pages/partners";
import Reports from "@/pages/reports";
import Reseller from "@/pages/reseller";
import Referrals from "@/pages/referrals";
import Settings from "@/pages/settings";
import Profile from "@/pages/profile";
import SelfServicePortal from "@/pages/self-service";
import PermissionRequestForm from "@/pages/self-service/request";
import ManagerDashboard from "@/pages/manager-dashboard";
import Companies from "@/pages/companies";
import CompanyDetail from "@/pages/companies/[id]";
import Subscriptions from "@/pages/subscriptions";
import UserManagement from "@/pages/user-management";
import RoleHierarchy from "@/pages/user-management/role-hierarchy";
import ApprovalWorkflows from "@/pages/user-management/approval-workflows";
import DynamicImport from "@/components/DynamicImport";
// Branch and Group pages removed as they're now managed under settings

import { TooltipProvider } from "@/components/ui/tooltip";
import { CompanyProvider } from "@/lib/companies";
import { Toaster } from "@/components/ui/toaster";
import { AuthProvider } from "@/components/auth/auth-context";
import { SettingsProvider } from "@/contexts/SettingsContext";

function App() {
  return (
    <TooltipProvider>
      <CompanyProvider>
        <AuthProvider>
          <SettingsProvider>
            <Toaster />
            <Switch>
        {/* Landing page - public route */}
        <Route path="/">
          <AppLayout requireAuth={false}>
            <Landing />
          </AppLayout>
        </Route>

        {/* Auth routes */}
        <Route path="/login">
          <AppLayout requireAuth={false}>
            <Login />
          </AppLayout>
        </Route>
        <Route path="/register">
          <AppLayout requireAuth={false}>
            <Register />
          </AppLayout>
        </Route>

        <Route path="/reset-password">
          <AppLayout requireAuth={false}>
            <ResetPassword />
          </AppLayout>
        </Route>

        {/* Main app routes */}
        <Route path="/dashboard">
          <AppLayout>
            <Dashboard />
          </AppLayout>
        </Route>

        {/* Agents routes */}
        <Route path="/agents">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <Agents />
          </AppLayout>
        </Route>

        {/* Collections routes */}
        <Route path="/collections">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <Collections />
          </AppLayout>
        </Route>
        <Route path="/collections/quick-payment">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <QuickPayment />
          </AppLayout>
        </Route>

        {/* Customers routes */}
        <Route path="/customers">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <Customers />
          </AppLayout>
        </Route>
        <Route path="/customers/create">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <CreateCustomer />
          </AppLayout>
        </Route>
        <Route path="/customers/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <DynamicImport path="/customers/[id]" />
          </AppLayout>
        </Route>
        <Route path="/customers/:id/loans">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <DynamicImport path="/customers/[id]/loans" />
          </AppLayout>
        </Route>
        <Route path="/customers/:id/edit">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <DynamicImport path="/customers/[id]/edit" />
          </AppLayout>
        </Route>

        {/* Loans routes */}
        <Route path="/loans">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <Loans />
          </AppLayout>
        </Route>
        <Route path="/loans/loan-types">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <LoanTypeSelection />
          </AppLayout>
        </Route>
        <Route path="/loans/create">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <CreateLoan />
          </AppLayout>
        </Route>
        <Route path="/loans/quick-create">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <QuickLoan />
          </AppLayout>
        </Route>
        <Route path="/loans/edit/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <EditLoan />
          </AppLayout>
        </Route>
        <Route path="/loans/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee', 'agent']}>
            <LoanDetail />
          </AppLayout>
        </Route>
        {/* Dynamic form builder route removed */}

        {/* Loan configurations route removed */}

        {/* Partners routes */}
        <Route path="/partners">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <Partners />
          </AppLayout>
        </Route>

        {/* Reports routes */}
        <Route path="/reports">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <Reports />
          </AppLayout>
        </Route>

        {/* Reseller routes */}
        <Route path="/reseller">
          <AppLayout allowedRoles={['saas_admin', 'reseller']}>
            <Reseller />
          </AppLayout>
        </Route>

        {/* Referrals routes */}
        <Route path="/referrals">
          <AppLayout allowedRoles={['saas_admin', 'reseller', 'partner']}>
            <Referrals />
          </AppLayout>
        </Route>

        {/* Settings route */}
        <Route path="/settings">
          <AppLayout>
            <Settings />
          </AppLayout>
        </Route>

        {/* Profile route */}
        <Route path="/profile">
          <AppLayout>
            <Profile />
          </AppLayout>
        </Route>

        {/* Manager Dashboard */}
        <Route path="/manager-dashboard">
          <AppLayout>
            <ManagerDashboard />
          </AppLayout>
        </Route>

        {/* Self-Service Portal routes */}
        <Route path="/self-service">
          <AppLayout>
            <SelfServicePortal />
          </AppLayout>
        </Route>

        <Route path="/self-service/request">
          <AppLayout>
            <PermissionRequestForm />
          </AppLayout>
        </Route>



        {/* Companies routes (admin only) */}
        <Route path="/companies">
          <AppLayout allowedRoles={['saas_admin']}>
            <Companies />
          </AppLayout>
        </Route>

        <Route path="/companies/:id">
          <AppLayout allowedRoles={['saas_admin']}>
            <CompanyDetail />
          </AppLayout>
        </Route>

        {/* Subscription management (admin only) */}
        <Route path="/subscriptions">
          <AppLayout allowedRoles={['saas_admin']}>
            <Subscriptions />
          </AppLayout>
        </Route>

        {/* User Management routes */}
        <Route path="/user-management">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <UserManagement />
          </AppLayout>
        </Route>

        <Route path="/user-management/roles/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <DynamicImport path="/user-management/roles/[id]" />
          </AppLayout>
        </Route>

        <Route path="/user-management/groups/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <DynamicImport path="/user-management/groups/[id]" />
          </AppLayout>
        </Route>

        <Route path="/user-management/role-hierarchy">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <RoleHierarchy />
          </AppLayout>
        </Route>

        <Route path="/user-management/approval-workflows">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <ApprovalWorkflows />
          </AppLayout>
        </Route>

        <Route path="/user-management/permissions">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <DynamicImport path="/user-management/permissions/index" />
          </AppLayout>
        </Route>

        <Route path="/user-management/permissions/user-permissions">
          <AppLayout allowedRoles={['saas_admin', 'company_admin']}>
            <DynamicImport path="/user-management/permissions/user-permissions" />
          </AppLayout>
        </Route>

        {/* Branches and Groups routes removed - now managed in Settings */}

        {/* Financial Management routes */}
        {/* Accounts routes */}
        <Route path="/financial/accounts">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/accounts/index" />
          </AppLayout>
        </Route>
        <Route path="/financial/accounts/create">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/accounts/create" />
          </AppLayout>
        </Route>
        <Route path="/financial/accounts/:id/edit">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/accounts/[id]/edit" />
          </AppLayout>
        </Route>
        <Route path="/financial/accounts/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/accounts/[id]" />
          </AppLayout>
        </Route>

        {/* Transactions routes */}
        <Route path="/financial/transactions">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/transactions/index" />
          </AppLayout>
        </Route>
        <Route path="/financial/transactions/create">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/transactions/create" />
          </AppLayout>
        </Route>
        <Route path="/financial/transactions/:id">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/transactions/[id]" />
          </AppLayout>
        </Route>
        <Route path="/financial/transactions/:id/edit">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/transactions/[id]/edit" />
          </AppLayout>
        </Route>

        {/* Expenses routes */}
        <Route path="/financial/expenses">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/expenses/index" />
          </AppLayout>
        </Route>

        {/* Financial Reports routes */}
        <Route path="/financial/reports">
          <AppLayout allowedRoles={['saas_admin', 'company_admin', 'employee']}>
            <DynamicImport path="/financial/reports/index" />
          </AppLayout>
        </Route>

        {/* Test routes - for development only */}
        <Route path="/test/trial-balance">
          <AppLayout>
            <DynamicImport path="/test/trial-balance" />
          </AppLayout>
        </Route>

        {/* Fallback to 404 */}
        <Route>
          <AppLayout requireAuth={false}>
            <NotFound />
          </AppLayout>
        </Route>
      </Switch>
          </SettingsProvider>
        </AuthProvider>
      </CompanyProvider>
    </TooltipProvider>
  );
}

export default App;
