import { useState, useEffect, useRef } from "react";
import { Building2, ChevronDown, Plus } from "lucide-react";
import { useAuth } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useMutation } from "@tanstack/react-query";
import { apiRequest } from "@/lib/queryClient";

// Define country code constant
const COUNTRY_CODE = '+91';

// Company form schema
const companyFormSchema = z.object({
  name: z.string()
    .min(1, { message: "Company name is required" })
    .min(2, { message: "Company name must be at least 2 characters" }),
  email: z.string()
    .min(1, { message: "Email is required" })
    .refine(
      (value) => {
        // Email regex pattern for validation
        const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
        return emailPattern.test(value);
      },
      { message: "Please enter a valid email address" }
    ),
  phone: z.string()
    .min(1, { message: "Phone number is required" })
    .refine(
      (value) => {
        // Accept only format with country code followed by exactly 10 digits
        const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
        const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
        return pattern.test(value);
      },
      { message: `Phone number must be exactly 10 digits with ${COUNTRY_CODE} country code` }
    ),
  address: z.string()
    .min(1, { message: "Address is required" })
    .min(5, { message: "Address must be at least 5 characters" }),
  website: z.string().optional(),
});

type CompanyFormValues = z.infer<typeof companyFormSchema>;

const CompanySelector = () => {
  const { getCurrentUser, getUserCompanies, switchCompany } = useAuth();
  const user = getCurrentUser();
  const [companyDropdownOpen, setCompanyDropdownOpen] = useState(false);
  const [createCompanyDialogOpen, setCreateCompanyDialogOpen] = useState(false);
  const [userCompanies, setUserCompanies] = useState<any[]>([]);
  const { toast } = useToast();
  const dropdownRef = useRef<HTMLDivElement>(null);

  const companyForm = useForm<CompanyFormValues>({
    resolver: zodResolver(companyFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
      website: "",
    },
  });

  const createCompanyMutation = useMutation({
    mutationFn: async (data: CompanyFormValues) => {
      const response = await apiRequest("POST", "/api/user-companies", data);
      return response.json();
    },
    onSuccess: (newCompany) => {
      toast({
        title: "Success",
        description: "Company created successfully",
      });
      setCreateCompanyDialogOpen(false);
      companyForm.reset();

      // Update the companies list
      if (user && user.id) {
        getUserCompanies(user.id).then(companies => {
          setUserCompanies(companies);
        });
      }

      // Switch to the new company
      switchCompany(newCompany.id, newCompany.name);
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message || "Failed to create company",
        variant: "destructive",
      });
    },
  });

  // Handle clicks outside of the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node) && companyDropdownOpen) {
        setCompanyDropdownOpen(false);
      }
    };

    // Add event listener when dropdown is open
    if (companyDropdownOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    // Cleanup the event listener
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [companyDropdownOpen]);

  // Fetch user companies once
  useEffect(() => {
    if (user && user.id) {
      const userId = user.id; // Create stable reference

      // Function to fetch companies
      const fetchUserCompanies = async () => {
        try {
          console.log("Fetching companies only once");
          const companies = await getUserCompanies(userId);
          setUserCompanies(companies);
        } catch (error) {
          console.error("Failed to fetch user companies:", error);
          toast({
            title: "Error",
            description: "Failed to load your companies",
            variant: "destructive",
          });
        }
      };

      // Only fetch if we don't already have companies
      if (userCompanies.length === 0) {
        fetchUserCompanies();
      }
    }
  }, [user?.id]);

  const onSubmit = (data: CompanyFormValues) => {
    // Manual validation for required fields
    let hasError = false;

    // Validate name field (required)
    if (!data.name || data.name.trim() === '') {
      companyForm.setError("name", {
        type: "manual",
        message: "Company name is required"
      });
      hasError = true;
    }

    // Validate email field (required)
    if (!data.email || data.email.trim() === '') {
      companyForm.setError("email", {
        type: "manual",
        message: "Email is required"
      });
      hasError = true;
    } else {
      const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
      if (!emailPattern.test(data.email)) {
        companyForm.setError("email", {
          type: "manual",
          message: "Please enter a valid email address"
        });
        hasError = true;
      }
    }

    // Validate phone field (required)
    if (!data.phone || data.phone.trim() === '') {
      companyForm.setError("phone", {
        type: "manual",
        message: "Phone number is required"
      });
      hasError = true;
    } else {
      const escapedCountryCode = COUNTRY_CODE.replace('+', '\\+');
      const pattern = new RegExp(`^${escapedCountryCode}\\d{10}$`);
      if (!pattern.test(data.phone)) {
        companyForm.setError("phone", {
          type: "manual",
          message: "Phone number must be exactly 10 digits with country code"
        });
        hasError = true;
      }
    }

    // Validate address field (required)
    if (!data.address || data.address.trim() === '') {
      companyForm.setError("address", {
        type: "manual",
        message: "Address is required"
      });
      hasError = true;
    }

    // If there are validation errors, don't submit
    if (hasError) {
      return;
    }

    createCompanyMutation.mutate(data);
  };

  return (
    <>
      <div className="mt-4 px-4 mb-4" ref={dropdownRef}>
        <div
          className="flex items-center justify-between p-2 bg-blue-800 rounded-md cursor-pointer hover:bg-blue-700 transition-colors"
          onClick={() => setCompanyDropdownOpen(!companyDropdownOpen)}
        >
          <div className="flex items-center">
            <Building2 className="h-5 w-5 mr-2 text-blue-300" />
            <div className="flex flex-col">
              <span className="text-sm font-medium text-white truncate max-w-[150px]">
                {user?.company_name || "Select Company"}
              </span>
              <span className="text-xs text-blue-200">Switch company</span>
            </div>
          </div>
          <ChevronDown className={`h-4 w-4 text-blue-200 transform ${companyDropdownOpen ? 'rotate-180' : ''}`} />
        </div>

        {/* Company Dropdown */}
        {companyDropdownOpen && (
          <div className="mt-1 bg-white border border-gray-200 rounded-md shadow-md py-1 z-50 absolute">
            {userCompanies.map(company => (
              <div
                key={company.id}
                className={`px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center justify-between ${company.company_id === user?.company_id ? 'bg-blue-50 font-medium' : ''}`}
              >
                <div
                  className="flex items-center truncate mr-2"
                  onClick={() => {
                    switchCompany(company.company_id, company.company?.name || company.name || `Company ${company.company_id}`);
                    setCompanyDropdownOpen(false);
                  }}
                >
                  <Building2 className="h-4 w-4 mr-2 text-primary flex-shrink-0" />
                  <span className="text-sm truncate text-gray-800">{company.company?.name || company.name || `Company ${company.company_id}`}</span>
                </div>

                {company.is_primary ? (
                  <span className="text-xs text-green-600 font-medium">Primary</span>
                ) : (
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      switchCompany(
                        company.company_id,
                        company.company?.name || company.name || `Company ${company.company_id}`,
                        true
                      );
                      setCompanyDropdownOpen(false);
                    }}
                    className="text-xs text-blue-600 hover:text-blue-800 font-medium"
                  >
                    Set Primary
                  </button>
                )}
              </div>
            ))}
            <div className="border-t border-gray-200 mt-1 pt-1">
              <div
                className="px-3 py-2 hover:bg-gray-100 cursor-pointer flex items-center text-primary"
                onClick={() => {
                  setCompanyDropdownOpen(false);
                  setCreateCompanyDialogOpen(true);
                }}
              >
                <Plus className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">Create New Company</span>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Create Company Dialog */}
      <Dialog open={createCompanyDialogOpen} onOpenChange={setCreateCompanyDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Create New Company</DialogTitle>
          </DialogHeader>
          <Form {...companyForm}>
            <form onSubmit={companyForm.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={companyForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Company Name <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Acme Inc."
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            companyForm.setError("name", {
                              type: "manual",
                              message: "Company name is required"
                            });
                          } else if (value.length < 2) {
                            companyForm.setError("name", {
                              type: "manual",
                              message: "Company name must be at least 2 characters"
                            });
                          } else {
                            companyForm.clearErrors("name");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={companyForm.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            companyForm.setError("email", {
                              type: "manual",
                              message: "Email is required"
                            });
                          } else {
                            const emailPattern = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,6}$/;
                            if (!emailPattern.test(value)) {
                              companyForm.setError("email", {
                                type: "manual",
                                message: "Please enter a valid email address"
                              });
                            } else {
                              companyForm.clearErrors("email");
                            }
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={companyForm.control}
                name="phone"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Phone Number <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <div className="flex w-full">
                        <span className="flex items-center justify-center px-3 py-2 border border-r-0 border-input bg-muted text-sm rounded-l-md whitespace-nowrap shrink-0">
                          {COUNTRY_CODE}
                        </span>
                        <Input
                          placeholder="Enter 10-digit phone number"
                          className="rounded-l-none border-l-0 flex-1 w-full min-w-0"
                          value={field.value ? field.value.replace(COUNTRY_CODE, '') : ''}
                          onChange={(e) => {
                            // Remove non-digit characters
                            const digitsOnly = e.target.value.replace(/\D/g, '');

                            // Trim to 10 digits max
                            const trimmed = digitsOnly.substring(0, 10);

                            // Update form value with country code prefix
                            field.onChange(`${COUNTRY_CODE}${trimmed}`);

                            // Real-time validation
                            if (!trimmed || trimmed.length === 0) {
                              companyForm.setError("phone", {
                                type: "manual",
                                message: "Phone number is required"
                              });
                            } else if (trimmed.length < 10) {
                              companyForm.setError("phone", {
                                type: "manual",
                                message: `Phone number must be exactly 10 digits (currently ${trimmed.length})`
                              });
                            } else if (trimmed.length === 10) {
                              companyForm.clearErrors("phone");
                            }
                          }}
                        />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={companyForm.control}
                name="address"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Address <span className="text-red-500">*</span></FormLabel>
                    <FormControl>
                      <Input
                        placeholder="123 Main St, City, State"
                        {...field}
                        onChange={(e) => {
                          field.onChange(e);
                          // Real-time validation
                          const value = e.target.value.trim();
                          if (!value) {
                            companyForm.setError("address", {
                              type: "manual",
                              message: "Address is required"
                            });
                          } else if (value.length < 5) {
                            companyForm.setError("address", {
                              type: "manual",
                              message: "Address must be at least 5 characters"
                            });
                          } else {
                            companyForm.clearErrors("address");
                          }
                        }}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={companyForm.control}
                name="website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website (Optional)</FormLabel>
                    <FormControl>
                      <Input placeholder="https://example.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setCreateCompanyDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit" disabled={createCompanyMutation.isPending}>
                  {createCompanyMutation.isPending ? "Creating..." : "Create Company"}
                </Button>
              </div>
            </form>
          </Form>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default CompanySelector;