import errorLogger from './errorLogger';

/**
 * Sets up global error handling for unhandled errors and rejections
 * This allows us to catch errors that aren't handled by components
 */
export function setupGlobalErrorHandlers(): void {
  // Handle uncaught errors
  const originalOnError = window.onerror;
  window.onerror = (message, source, lineno, colno, error) => {
    errorLogger.error(
      `Global error: ${String(message)}`,
      'global',
      { message, source, lineno, colno, error }
    );
    
    // Call original handler if it exists
    if (originalOnError) {
      return originalOnError(message, source, lineno, colno, error);
    }
    
    return false;
  };

  // Handle unhandled promise rejections
  window.addEventListener('unhandledrejection', (event: PromiseRejectionEvent) => {
    const error = event.reason;
    const message = error instanceof Error ? error.message : 'Unhandled Promise Rejection';
    
    errorLogger.error(
      `Unhandled rejection: ${message}`,
      'global',
      error
    );
  });

  // Log network errors
  const originalFetch = window.fetch;
  window.fetch = async (...args) => {
    try {
      const response = await originalFetch(...args);
      
      // Log HTTP errors (non-2xx responses)
      if (!response.ok) {
        const urlString = typeof args[0] === 'string' 
          ? args[0] 
          : args[0] instanceof Request 
            ? args[0].url 
            : String(args[0]);
            
        errorLogger.error(
          `HTTP Error ${response.status}: ${response.statusText}`,
          'network',
          { url: urlString, status: response.status, statusText: response.statusText }
        );
      }
      
      return response;
    } catch (error) {
      // Log network failures
      const urlString = typeof args[0] === 'string' 
        ? args[0] 
        : args[0] instanceof Request 
          ? args[0].url 
          : String(args[0]);
          
      errorLogger.error(
        `Network error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'network',
        { url: urlString, error }
      );
      
      throw error;
    }
  };

  errorLogger.info('Global error handlers installed', 'global');
}

export default setupGlobalErrorHandlers;