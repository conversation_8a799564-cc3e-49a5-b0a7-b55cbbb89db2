import { db } from '../db';
import {
  permissionRequests, requestApprovals, requestComments, userAccessSummary,
  type PermissionRequest, type InsertPermissionRequest,
  type RequestApproval, type InsertRequestApproval,
  type RequestComment, type InsertRequestComment,
  type UserAccessSummary, type InsertUserAccessSummary,
  type PermissionRequestContext, type AccessStatusSummary,
  users, customRoles, permissionAuditLogs, dataAccessAuditLogs, temporaryPermissions
} from '@shared/schema';
import { eq, and, desc, count, gte, lte, inArray, sql, or } from 'drizzle-orm';
import crypto from 'crypto';
import { auditService } from './auditService';

export class SelfServicePortalService {
  /**
   * Generate unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${crypto.randomBytes(6).toString('hex')}`;
  }

  /**
   * Submit a permission request
   */
  async submitPermissionRequest(context: PermissionRequestContext): Promise<PermissionRequest> {
    const requestData: InsertPermissionRequest = {
      request_id: this.generateRequestId(),
      requester_id: context.userId,
      company_id: context.companyId,
      request_type: context.requestType,
      title: context.title,
      description: context.description,
      business_justification: context.businessJustification,
      priority: context.priority || 'medium',
      urgency_reason: context.urgencyReason,
      requested_permissions: context.requestedPermissions || [],
      requested_role_id: context.requestedRoleId,
      temporary_access: context.temporaryAccess || false,
      access_start_date: context.accessStartDate,
      access_end_date: context.accessEndDate,
      manager_id: context.managerId,
      department: context.department,
      risk_assessment: this.calculateRequestRisk(context),
    };

    const [request] = await db.insert(permissionRequests).values(requestData).returning();

    // Create initial approval workflow
    await this.createApprovalWorkflow(request.id, context);

    // Log the request submission
    await auditService.logPermissionUsage({
      userId: context.userId,
      companyId: context.companyId,
      permissionCode: 'self_service_request',
      permissionName: 'Submit Permission Request',
      resourceType: 'permission_request',
      resourceId: request.request_id,
      operationType: 'create',
      result: 'success',
      metadata: {
        requestType: context.requestType,
        requestedPermissions: context.requestedPermissions,
        priority: context.priority,
      },
    });

    return request;
  }

  /**
   * Calculate risk assessment for a request
   */
  private calculateRequestRisk(context: PermissionRequestContext): string {
    let riskScore = 0;

    // Base risk by request type
    switch (context.requestType) {
      case 'permission_grant':
        riskScore += 20;
        break;
      case 'role_change':
        riskScore += 30;
        break;
      case 'temporary_access':
        riskScore += 15;
        break;
      case 'access_extension':
        riskScore += 10;
        break;
      default:
        riskScore += 5;
    }

    // Risk by priority
    switch (context.priority) {
      case 'urgent':
        riskScore += 25;
        break;
      case 'high':
        riskScore += 15;
        break;
      case 'medium':
        riskScore += 5;
        break;
      default:
        riskScore += 0;
    }

    // Risk by number of permissions requested
    if (context.requestedPermissions) {
      riskScore += Math.min(context.requestedPermissions.length * 5, 30);
    }

    // Risk by temporary access duration
    if (context.temporaryAccess && context.accessStartDate && context.accessEndDate) {
      const durationDays = Math.ceil(
        (context.accessEndDate.getTime() - context.accessStartDate.getTime()) / (1000 * 60 * 60 * 24)
      );
      if (durationDays > 90) riskScore += 20;
      else if (durationDays > 30) riskScore += 10;
      else if (durationDays > 7) riskScore += 5;
    }

    if (riskScore >= 70) return 'critical';
    if (riskScore >= 50) return 'high';
    if (riskScore >= 30) return 'medium';
    return 'low';
  }

  /**
   * Create approval workflow for a request
   */
  private async createApprovalWorkflow(requestId: number, context: PermissionRequestContext): Promise<void> {
    const approvals: InsertRequestApproval[] = [];

    // Step 1: Manager approval (if manager exists)
    if (context.managerId) {
      approvals.push({
        request_id: requestId,
        approver_id: context.managerId,
        approval_step: 1,
        approval_level: 'manager',
        due_date: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000), // 3 days
      });
    }

    // Step 2: Security/Admin approval for high-risk requests
    const riskLevel = this.calculateRequestRisk(context);
    if (['high', 'critical'].includes(riskLevel)) {
      // Find company admin for approval
      const [companyAdmin] = await db
        .select()
        .from(users)
        .where(and(
          eq(users.company_id, context.companyId),
          eq(users.role, 'company_admin')
        ))
        .limit(1);

      if (companyAdmin) {
        approvals.push({
          request_id: requestId,
          approver_id: companyAdmin.id,
          approval_step: context.managerId ? 2 : 1,
          approval_level: 'admin',
          due_date: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000), // 5 days
        });
      }
    }

    // Insert all approvals
    if (approvals.length > 0) {
      await db.insert(requestApprovals).values(approvals);

      // Set current approver to first step
      await db
        .update(permissionRequests)
        .set({ current_approver: approvals[0].approver_id })
        .where(eq(permissionRequests.id, requestId));
    }
  }

  /**
   * Get user's permission requests
   */
  async getUserRequests(userId: number, companyId: number, filters: {
    status?: string;
    requestType?: string;
    startDate?: Date;
    endDate?: Date;
    limit?: number;
    offset?: number;
  } = {}): Promise<{ requests: PermissionRequest[]; totalCount: number }> {
    try {
      const conditions = [
        eq(permissionRequests.requester_id, userId),
        eq(permissionRequests.company_id, companyId),
      ];

      if (filters.status) {
        conditions.push(eq(permissionRequests.status, filters.status as any));
      }
      if (filters.requestType) {
        conditions.push(eq(permissionRequests.request_type, filters.requestType as any));
      }
      if (filters.startDate) {
        conditions.push(gte(permissionRequests.submitted_at, filters.startDate));
      }
      if (filters.endDate) {
        conditions.push(lte(permissionRequests.submitted_at, filters.endDate));
      }

      const whereClause = and(...conditions);

      // Get total count
      const [{ count: totalCount }] = await db
        .select({ count: count() })
        .from(permissionRequests)
        .where(whereClause);

      // Get requests with pagination
      const requests = await db
        .select()
        .from(permissionRequests)
        .where(whereClause)
        .orderBy(desc(permissionRequests.submitted_at))
        .limit(filters.limit || 50)
        .offset(filters.offset || 0);

      return { requests, totalCount };
    } catch (error) {
      console.error('Error fetching user requests:', error);
      // Return empty results if table doesn't exist
      return { requests: [], totalCount: 0 };
    }
  }

  /**
   * Get user's access status summary
   */
  async getUserAccessStatus(userId: number, companyId: number): Promise<AccessStatusSummary> {
    try {
      // Try to get cached summary first (if table exists)
      try {
        const [cachedSummary] = await db
          .select()
          .from(userAccessSummary)
          .where(and(
            eq(userAccessSummary.user_id, userId),
            eq(userAccessSummary.company_id, companyId)
          ));

        if (cachedSummary &&
            cachedSummary.last_updated &&
            Date.now() - cachedSummary.last_updated.getTime() < 60 * 60 * 1000) { // 1 hour cache
          return {
            totalPermissions: cachedSummary.total_permissions,
            activePermissions: cachedSummary.active_permissions,
            temporaryPermissions: cachedSummary.temporary_permissions,
            pendingRequests: cachedSummary.pending_requests,
            recentChanges: cachedSummary.recent_access_changes,
            riskScore: cachedSummary.risk_score,
            complianceStatus: cachedSummary.compliance_status,
            lastAccessReview: cachedSummary.last_access_review || undefined,
            nextAccessReview: cachedSummary.next_access_review || undefined,
          };
        }
      } catch (tableError) {
        console.log('userAccessSummary table not found, using fallback calculation');
      }

      // Calculate fresh summary
      const summary = await this.calculateAccessSummary(userId, companyId);

      // Try to update cached summary (if table exists)
      try {
        await db
          .insert(userAccessSummary)
          .values({
            user_id: userId,
            company_id: companyId,
            total_permissions: summary.totalPermissions,
            active_permissions: summary.activePermissions,
            temporary_permissions: summary.temporaryPermissions,
            pending_requests: summary.pendingRequests,
            recent_access_changes: summary.recentChanges,
            risk_score: summary.riskScore,
            compliance_status: summary.complianceStatus,
            last_access_review: summary.lastAccessReview,
            next_access_review: summary.nextAccessReview,
            last_updated: new Date(),
          })
          .onConflictDoUpdate({
            target: [userAccessSummary.user_id, userAccessSummary.company_id],
            set: {
              total_permissions: summary.totalPermissions,
              active_permissions: summary.activePermissions,
              temporary_permissions: summary.temporaryPermissions,
              pending_requests: summary.pendingRequests,
              recent_access_changes: summary.recentChanges,
              risk_score: summary.riskScore,
              compliance_status: summary.complianceStatus,
              last_access_review: summary.lastAccessReview,
              next_access_review: summary.nextAccessReview,
              last_updated: new Date(),
            },
          });
      } catch (cacheError) {
        console.log('Could not cache access summary, table may not exist');
      }

      return summary;
    } catch (error) {
      console.error('Error fetching access status:', error);
      // Return fallback data
      return {
        totalPermissions: 0,
        activePermissions: 0,
        temporaryPermissions: 0,
        pendingRequests: 0,
        recentChanges: 0,
        riskScore: 0,
        complianceStatus: 'compliant',
        lastAccessReview: undefined,
        nextAccessReview: undefined,
      };
    }
  }

  /**
   * Calculate fresh access summary for a user
   */
  private async calculateAccessSummary(userId: number, companyId: number): Promise<AccessStatusSummary> {
    try {
      // Get user details
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        throw new Error('User not found');
      }

      let pendingRequests = 0;
      let temporaryPermissions = 0;
      let recentChanges = 0;

      // Count pending requests (if table exists)
      try {
        const [{ count }] = await db
          .select({ count: count() })
          .from(permissionRequests)
          .where(and(
            eq(permissionRequests.requester_id, userId),
            eq(permissionRequests.status, 'pending')
          ));
        pendingRequests = count;
      } catch (error) {
        console.log('permissionRequests table not found, using default value');
      }

      // Count temporary permissions (if table exists)
      try {
        const [{ count }] = await db
          .select({ count: count() })
          .from(temporaryPermissions)
          .where(and(
            eq(temporaryPermissions.user_id, userId),
            gte(temporaryPermissions.expires_at, new Date())
          ));
        temporaryPermissions = count;
      } catch (error) {
        console.log('temporaryPermissions table not found, using default value');
      }

      // Count recent access changes (last 30 days) (if table exists)
      try {
        const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const [{ count }] = await db
          .select({ count: count() })
          .from(permissionRequests)
          .where(and(
            eq(permissionRequests.requester_id, userId),
            gte(permissionRequests.submitted_at, thirtyDaysAgo),
            inArray(permissionRequests.status, ['approved', 'implemented'])
          ));
        recentChanges = count;
      } catch (error) {
        console.log('Could not count recent changes, using default value');
      }

      // Calculate basic metrics
      const totalPermissions = user.permissions ? user.permissions.length : 0;
      const activePermissions = totalPermissions; // All permissions are considered active for now

      // Calculate risk score based on user activity
      const riskScore = await this.calculateUserRiskScore(userId, companyId);

      return {
        totalPermissions,
        activePermissions,
        temporaryPermissions,
        pendingRequests,
        recentChanges,
        riskScore,
        complianceStatus: riskScore > 70 ? 'non_compliant' : 'compliant',
        lastAccessReview: undefined, // TODO: Implement access review tracking
        nextAccessReview: undefined, // TODO: Implement access review scheduling
      };
    } catch (error) {
      console.error('Error calculating access summary:', error);
      // Return fallback data
      return {
        totalPermissions: 0,
        activePermissions: 0,
        temporaryPermissions: 0,
        pendingRequests: 0,
        recentChanges: 0,
        riskScore: 0,
        complianceStatus: 'compliant',
        lastAccessReview: undefined,
        nextAccessReview: undefined,
      };
    }
  }

  /**
   * Calculate user risk score based on activity and permissions
   */
  private async calculateUserRiskScore(userId: number, companyId: number): Promise<number> {
    let riskScore = 0;

    // Get recent failed access attempts (last 7 days)
    const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
    const [{ count: failedAttempts }] = await db
      .select({ count: count() })
      .from(permissionAuditLogs)
      .where(and(
        eq(permissionAuditLogs.user_id, userId),
        eq(permissionAuditLogs.result, 'denied'),
        gte(permissionAuditLogs.timestamp, sevenDaysAgo)
      ));

    // Risk from failed attempts
    riskScore += Math.min(failedAttempts * 5, 30);

    // Get recent high-risk operations
    const [{ count: sensitiveOps }] = await db
      .select({ count: count() })
      .from(permissionAuditLogs)
      .where(and(
        eq(permissionAuditLogs.user_id, userId),
        eq(permissionAuditLogs.is_sensitive_operation, true),
        gte(permissionAuditLogs.timestamp, sevenDaysAgo)
      ));

    // Risk from sensitive operations
    riskScore += Math.min(sensitiveOps * 3, 25);

    // Get user's current permissions count
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId));

    if (user?.permissions) {
      const permissionCount = user.permissions.length;
      if (permissionCount > 20) riskScore += 20;
      else if (permissionCount > 10) riskScore += 10;
      else if (permissionCount > 5) riskScore += 5;
    }

    return Math.min(riskScore, 100);
  }

  /**
   * Cancel a pending request
   */
  async cancelRequest(requestId: number, userId: number, reason: string): Promise<PermissionRequest> {
    const [request] = await db
      .update(permissionRequests)
      .set({
        status: 'cancelled',
        cancelled_by: userId,
        cancelled_at: new Date(),
        cancellation_reason: reason,
        updated_at: new Date(),
      })
      .where(and(
        eq(permissionRequests.id, requestId),
        eq(permissionRequests.requester_id, userId),
        eq(permissionRequests.status, 'pending')
      ))
      .returning();

    if (!request) {
      throw new Error('Request not found or cannot be cancelled');
    }

    // Add comment about cancellation
    await this.addRequestComment(requestId, userId, {
      commentType: 'status_change',
      comment: `Request cancelled by requester. Reason: ${reason}`,
      isInternal: false,
    });

    return request;
  }

  /**
   * Add comment to a request
   */
  async addRequestComment(requestId: number, userId: number, commentData: {
    commentType: string;
    comment: string;
    isInternal?: boolean;
    visibility?: string;
    attachments?: any[];
  }): Promise<RequestComment> {
    const [comment] = await db
      .insert(requestComments)
      .values({
        request_id: requestId,
        user_id: userId,
        comment_type: commentData.commentType,
        comment: commentData.comment,
        is_internal: commentData.isInternal || false,
        visibility: commentData.visibility || 'all',
        attachments: commentData.attachments || [],
      })
      .returning();

    return comment;
  }

  /**
   * Get request comments
   */
  async getRequestComments(requestId: number, userId: number): Promise<RequestComment[]> {
    // Check if user can view this request
    const [request] = await db
      .select()
      .from(permissionRequests)
      .where(eq(permissionRequests.id, requestId));

    if (!request || request.requester_id !== userId) {
      throw new Error('Request not found or access denied');
    }

    return await db
      .select()
      .from(requestComments)
      .where(and(
        eq(requestComments.request_id, requestId),
        or(
          eq(requestComments.visibility, 'all'),
          eq(requestComments.is_internal, false)
        )
      ))
      .orderBy(requestComments.created_at);
  }
}

// Create singleton instance
export const selfServicePortalService = new SelfServicePortalService();
