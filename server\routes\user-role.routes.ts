import { Express, Response } from 'express';
import { storage } from '../storage';
import { authMiddleware, requireRole, requirePermission, requireAnyPermission, AuthRequest } from '../middleware/auth';
import {
  users, userRoles, insertUserRoleSchema,
  customRoles, groupUsers, groupRoles
} from '../../shared/schema';
import { eq, and } from 'drizzle-orm';
import { db } from '../db';

export function registerUserRoleRoutes(app: Express): void {
  // Get user roles
  app.get('/api/users/:id/roles', authMiddleware, requireAnyPermission(['user_view', 'role_view']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Check if user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if current user has permission to view this user's roles
      if (req.user.role !== 'saas_admin' && req.user.id !== userId && req.user.company_id !== user.company_id) {
        return res.status(403).json({ message: 'You can only view roles for yourself or users in your company' });
      }

      // Get directly assigned roles
      const directRoles = await db
        .select({
          assignment: userRoles,
          role: customRoles
        })
        .from(userRoles)
        .leftJoin(customRoles, eq(userRoles.role_id, customRoles.id))
        .where(eq(userRoles.user_id, userId));

      // Get roles from groups
      const userGroups = await db
        .select({
          group_id: groupUsers.group_id
        })
        .from(groupUsers)
        .where(eq(groupUsers.user_id, userId));

      const groupRolesData = [];
      for (const group of userGroups) {
        const roles = await db
          .select({
            assignment: groupRoles,
            role: customRoles
          })
          .from(groupRoles)
          .leftJoin(customRoles, eq(groupRoles.role_id, customRoles.id))
          .where(eq(groupRoles.group_id, group.group_id));

        groupRolesData.push(...roles);
      }

      // Combine and deduplicate roles
      const allRoles = [
        ...directRoles.map(r => ({
          id: r.role.id,
          name: r.role.name,
          description: r.role.description,
          is_system: r.role.is_system,
          source: 'direct',
          assignment_id: r.assignment.id
        })),
        ...groupRolesData.map(r => ({
          id: r.role.id,
          name: r.role.name,
          description: r.role.description,
          is_system: r.role.is_system,
          source: 'group',
          assignment_id: r.assignment.id
        }))
      ];

      // Deduplicate by role ID
      const uniqueRoles = allRoles.filter((role, index, self) =>
        index === self.findIndex(r => r.id === role.id)
      );

      return res.json(uniqueRoles);
    } catch (error) {
      console.error(`Error fetching roles for user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Assign role to user
  app.post('/api/users/:id/roles', authMiddleware, requireAnyPermission(['user_edit', 'role_assign']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);
      const roleId = parseInt(req.body.role_id);

      if (!roleId) {
        return res.status(400).json({ message: 'Role ID is required' });
      }

      // Check if user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if current user has permission to assign roles to this user
      if (req.user.role !== 'saas_admin' && req.user.company_id !== user.company_id) {
        return res.status(403).json({ message: 'You can only assign roles to users in your company' });
      }

      // Check if role exists
      const [role] = await db
        .select()
        .from(customRoles)
        .where(eq(customRoles.id, roleId));

      if (!role) {
        return res.status(404).json({ message: 'Role not found' });
      }

      // Check if role is already assigned to the user
      const [existingAssignment] = await db
        .select()
        .from(userRoles)
        .where(
          and(
            eq(userRoles.user_id, userId),
            eq(userRoles.role_id, roleId)
          )
        );

      if (existingAssignment) {
        return res.status(400).json({ message: 'Role is already assigned to this user' });
      }

      // Assign role to user
      const [assignment] = await db
        .insert(userRoles)
        .values({
          user_id: userId,
          role_id: roleId
        })
        .returning();

      return res.status(201).json(assignment);
    } catch (error) {
      console.error(`Error assigning role to user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Remove role from user
  app.delete('/api/users/:userId/roles/:roleId', authMiddleware, requireAnyPermission(['user_edit', 'role_assign']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.userId);
      const roleId = parseInt(req.params.roleId);

      // Check if user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if current user has permission to remove roles from this user
      if (req.user.role !== 'saas_admin' && req.user.company_id !== user.company_id) {
        return res.status(403).json({ message: 'You can only remove roles from users in your company' });
      }

      // Check if role is assigned to the user
      const [assignment] = await db
        .select()
        .from(userRoles)
        .where(
          and(
            eq(userRoles.user_id, userId),
            eq(userRoles.role_id, roleId)
          )
        );

      if (!assignment) {
        return res.status(404).json({ message: 'Role is not directly assigned to this user' });
      }

      // Remove role from user
      await db
        .delete(userRoles)
        .where(
          and(
            eq(userRoles.user_id, userId),
            eq(userRoles.role_id, roleId)
          )
        );

      return res.json({ message: 'Role removed from user successfully' });
    } catch (error) {
      console.error(`Error removing role from user:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get user groups
  app.get('/api/users/:id/groups', authMiddleware, requireAnyPermission(['user_view', 'group_view']), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = parseInt(req.params.id);

      // Check if user exists
      const [user] = await db
        .select()
        .from(users)
        .where(eq(users.id, userId));

      if (!user) {
        return res.status(404).json({ message: 'User not found' });
      }

      // Check if current user has permission to view this user's groups
      if (req.user.role !== 'saas_admin' && req.user.id !== userId && req.user.company_id !== user.company_id) {
        return res.status(403).json({ message: 'You can only view groups for yourself or users in your company' });
      }

      // Get user's groups
      const userGroupsData = await db
        .select({
          membership: groupUsers,
          group: groups
        })
        .from(groupUsers)
        .leftJoin(groups, eq(groupUsers.group_id, groups.id))
        .where(eq(groupUsers.user_id, userId));

      const userGroups = userGroupsData.map(g => ({
        id: g.group.id,
        name: g.group.name,
        description: g.group.description,
        company_id: g.group.company_id,
        branch_id: g.group.branch_id,
        membership_id: g.membership.id,
        joined_at: g.membership.created_at
      }));

      return res.json(userGroups);
    } catch (error) {
      console.error(`Error fetching groups for user ${req.params.id}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
