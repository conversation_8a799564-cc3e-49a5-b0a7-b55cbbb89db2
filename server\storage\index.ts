import { IStorage } from './interfaces';
import { UserStorage } from './user.storage';
import { CompanyStorage } from './company.storage';
import { UserCompanyStorage } from './user-company.storage';
import { CustomerStorage } from './customer.storage';
import { LoanStorage } from './loan.storage';
import { CollectionStorage } from './collection.storage';
import { PaymentStorage } from './payment.storage';
import { PartnerStorage } from './partner.storage';
import { AgentStorage } from './agent.storage';
import { CompanyPrefixSettingsStorage } from './company-prefix-settings.storage';
import { financialStorage } from './financial';
// Import other storage implementations

// Create a class that implements the IStorage interface by combining all domain-specific storage implementations
class Storage implements IStorage {
  private userStorage: UserStorage;
  private companyStorage: CompanyStorage;
  private userCompanyStorage: UserCompanyStorage;
  private customerStorage: CustomerStorage;
  private loanStorage: LoanStorage;
  private collectionStorage: CollectionStorage;
  private paymentStorage: PaymentStorage;
  private partnerStorage: PartnerStorage;
  private agentStorage: AgentStorage;
  private companyPrefixSettingsStorage: CompanyPrefixSettingsStorage;
  // Add other storage implementations

  constructor() {
    this.userStorage = new UserStorage();
    this.companyStorage = new CompanyStorage();
    this.userCompanyStorage = new UserCompanyStorage();
    this.customerStorage = new CustomerStorage();
    this.loanStorage = new LoanStorage();
    this.collectionStorage = new CollectionStorage();
    this.paymentStorage = new PaymentStorage();
    this.partnerStorage = new PartnerStorage();
    this.agentStorage = new AgentStorage();
    this.companyPrefixSettingsStorage = new CompanyPrefixSettingsStorage();
    // Initialize other storage implementations
  }

  // Delegate methods to the appropriate storage implementation

  // User methods
  getUser = this.userStorage.getUser.bind(this.userStorage);
  getUserByUsername = this.userStorage.getUserByUsername.bind(this.userStorage);
  getUserByEmail = this.userStorage.getUserByEmail.bind(this.userStorage);
  createUser = this.userStorage.createUser.bind(this.userStorage);
  updateUser = this.userStorage.updateUser.bind(this.userStorage);
  getUsersByCompany = this.userStorage.getUsersByCompany.bind(this.userStorage);
  updateUserPassword = this.userStorage.updateUserPassword.bind(this.userStorage);

  // Company methods
  getCompany = this.companyStorage.getCompany.bind(this.companyStorage);
  getCompanies = this.companyStorage.getCompanies.bind(this.companyStorage);
  createCompany = this.companyStorage.createCompany.bind(this.companyStorage);
  updateCompany = this.companyStorage.updateCompany.bind(this.companyStorage);
  deleteCompany = this.companyStorage.deleteCompany.bind(this.companyStorage);
  getCompanyUsers = this.companyStorage.getCompanyUsers.bind(this.companyStorage);

  // UserCompany methods
  getUserCompanies = this.userCompanyStorage.getUserCompanies.bind(this.userCompanyStorage);
  getUserCompanyByIds = this.userCompanyStorage.getUserCompanyByIds.bind(this.userCompanyStorage);
  createUserCompany = this.userCompanyStorage.createUserCompany.bind(this.userCompanyStorage);
  updateUserCompany = this.userCompanyStorage.updateUserCompany.bind(this.userCompanyStorage);
  deleteUserCompany = this.userCompanyStorage.deleteUserCompany.bind(this.userCompanyStorage);
  setUserCompanyAsPrimary = this.userCompanyStorage.setUserCompanyAsPrimary.bind(this.userCompanyStorage);
  updateUserCompanyPrimary = this.userCompanyStorage.updateUserCompanyPrimary.bind(this.userCompanyStorage);

  // Customer methods
  getCustomer = this.customerStorage.getCustomer.bind(this.customerStorage);
  getCustomersByCompany = this.customerStorage.getCustomersByCompany.bind(this.customerStorage);
  getCustomersByBranch = this.customerStorage.getCustomersByBranch.bind(this.customerStorage);
  createCustomer = this.customerStorage.createCustomer.bind(this.customerStorage);
  updateCustomer = this.customerStorage.updateCustomer.bind(this.customerStorage);
  deleteCustomer = this.customerStorage.deleteCustomer.bind(this.customerStorage);

  // Loan methods
  getLoan = this.loanStorage.getLoan.bind(this.loanStorage);
  getLoansByCustomer = this.loanStorage.getLoansByCustomer.bind(this.loanStorage);
  getLoansByCompany = this.loanStorage.getLoansByCompany.bind(this.loanStorage);
  getLoansByBranch = this.loanStorage.getLoansByBranch.bind(this.loanStorage);
  createLoan = this.loanStorage.createLoan.bind(this.loanStorage);
  updateLoan = this.loanStorage.updateLoan.bind(this.loanStorage);
  deleteLoan = this.loanStorage.deleteLoan.bind(this.loanStorage);
  deleteLoanWithCollections = this.loanStorage.deleteLoanWithCollections.bind(this.loanStorage);

  // Payment Schedule methods have been removed

  // Collection methods
  getCollection = this.collectionStorage.getCollection.bind(this.collectionStorage);
  getCollectionsByCompany = this.collectionStorage.getCollectionsByCompany.bind(this.collectionStorage);
  getCollectionsByBranch = this.collectionStorage.getCollectionsByBranch.bind(this.collectionStorage);
  getCollectionsByLoan = this.collectionStorage.getCollectionsByLoan.bind(this.collectionStorage);
  getCollectionsByAgent = this.collectionStorage.getCollectionsByAgent.bind(this.collectionStorage);
  createCollection = this.collectionStorage.createCollection.bind(this.collectionStorage);
  updateCollection = this.collectionStorage.updateCollection.bind(this.collectionStorage);
  updateCollectionStatus = this.collectionStorage.updateCollectionStatus.bind(this.collectionStorage);
  deleteCollection = this.collectionStorage.deleteCollection.bind(this.collectionStorage);
  markCollectionsAsCompleted = this.collectionStorage.markCollectionsAsCompleted.bind(this.collectionStorage);
  getPendingCollections = this.collectionStorage.getPendingCollections.bind(this.collectionStorage);
  getOverdueCollections = this.collectionStorage.getOverdueCollections.bind(this.collectionStorage);
  getPaginatedCollections = this.collectionStorage.getPaginatedCollections.bind(this.collectionStorage);

  // Payment methods
  getPayment = this.paymentStorage.getPayment.bind(this.paymentStorage);
  getPaymentsByCompany = this.paymentStorage.getPaymentsByCompany.bind(this.paymentStorage);
  getPaymentsByLoan = this.paymentStorage.getPaymentsByLoan.bind(this.paymentStorage);
  getPaymentsByCustomer = this.paymentStorage.getPaymentsByCustomer.bind(this.paymentStorage);
  getPaymentsByCollection = this.paymentStorage.getPaymentsByCollection.bind(this.paymentStorage);
  createPayment = this.paymentStorage.createPayment.bind(this.paymentStorage);
  updatePayment = this.paymentStorage.updatePayment.bind(this.paymentStorage);
  deletePayment = this.paymentStorage.deletePayment.bind(this.paymentStorage);
  getPaymentReceipt = this.paymentStorage.getPaymentReceipt.bind(this.paymentStorage);

  // Partner methods with error handling
  getPartner = async (id: number) => {
    try {
      return await this.partnerStorage.getPartner(id);
    } catch (error) {
      console.error('Error in Storage.getPartner:', error);
      throw error;
    }
  };

  getPartnersByCompany = async (companyId: number) => {
    try {
      console.log(`Storage.getPartnersByCompany called with companyId=${companyId}`);
      const result = await this.partnerStorage.getPartnersByCompany(companyId);
      console.log(`Storage.getPartnersByCompany result:`, result);
      return result;
    } catch (error) {
      console.error('Error in Storage.getPartnersByCompany:', error);
      return [];
    }
  };

  createPartner = async (partner: any) => {
    try {
      return await this.partnerStorage.createPartner(partner);
    } catch (error) {
      console.error('Error in Storage.createPartner:', error);
      throw error;
    }
  };

  updatePartner = async (id: number, companyId: number, partner: any) => {
    try {
      return await this.partnerStorage.updatePartner(id, companyId, partner);
    } catch (error) {
      console.error('Error in Storage.updatePartner:', error);
      throw error;
    }
  };

  deletePartner = async (id: number, companyId: number) => {
    try {
      return await this.partnerStorage.deletePartner(id, companyId);
    } catch (error) {
      console.error('Error in Storage.deletePartner:', error);
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  };

  // Agent methods
  getAgent = this.agentStorage.getAgent.bind(this.agentStorage);
  getAgentsByCompany = this.agentStorage.getAgentsByCompany.bind(this.agentStorage);
  getAgentByUserAndCompany = this.agentStorage.getAgentByUserAndCompany.bind(this.agentStorage);
  createAgent = this.agentStorage.createAgent.bind(this.agentStorage);
  updateAgent = this.agentStorage.updateAgent.bind(this.agentStorage);
  deleteAgent = this.agentStorage.deleteAgent.bind(this.agentStorage);
  getHighestAgentSerial = this.agentStorage.getHighestAgentSerial.bind(this.agentStorage);

  // Company Prefix Settings methods
  getCompanyPrefixSettings = this.companyPrefixSettingsStorage.getCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
  createCompanyPrefixSettings = this.companyPrefixSettingsStorage.createCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
  updateCompanyPrefixSettings = this.companyPrefixSettingsStorage.updateCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);
  deleteCompanyPrefixSettings = this.companyPrefixSettingsStorage.deleteCompanyPrefixSettings.bind(this.companyPrefixSettingsStorage);

  // Account methods
  getAccount = financialStorage.getAccount;
  getAccountsByCompany = financialStorage.getAccountsByCompany;
  createAccount = financialStorage.createAccount;
  updateAccount = financialStorage.updateAccount;
  deleteAccount = financialStorage.deleteAccount;
  getAccountBalance = financialStorage.getAccountBalance;
  updateAccountBalance = financialStorage.updateAccountBalance;

  // Transaction methods
  getTransaction = financialStorage.getTransaction;
  getTransactionsByCompany = financialStorage.getTransactionsByCompany;
  getTransactionsByAccount = financialStorage.getTransactionsByAccount;
  createTransaction = financialStorage.createTransaction;
  updateTransaction = financialStorage.updateTransaction;
  deleteTransaction = financialStorage.deleteTransaction;
  getTransactionSummary = financialStorage.getTransactionSummary;

  // Report methods
  getProfitLossReport = financialStorage.getProfitLossReport;
  getCashFlowReport = financialStorage.getCashFlowReport;
  getCollectionReport = financialStorage.getCollectionReport;

  // Add other delegated methods for remaining interfaces
}

// Export a singleton instance of the Storage class
export const storage = new Storage();
