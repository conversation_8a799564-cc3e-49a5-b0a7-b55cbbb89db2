import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { apiRequest } from '@/lib/queryClient';
import { useToast } from '@/hooks/use-toast';
import { Loader2 } from 'lucide-react';

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';

// Define the schema for prefix settings
const prefixSettingsSchema = z.object({
  loan_prefix: z.string().max(5, 'Prefix must be at most 5 characters').min(1, 'Prefix is required'),
  loan_start_number: z.coerce.number().int().min(1, 'Start number must be at least 1'),
  collection_prefix: z.string().max(5, 'Prefix must be at most 5 characters').min(1, 'Prefix is required'),
  collection_start_number: z.coerce.number().int().min(1, 'Start number must be at least 1'),
  customer_prefix: z.string().max(5, 'Prefix must be at most 5 characters').min(1, 'Prefix is required'),
  customer_start_number: z.coerce.number().int().min(1, 'Start number must be at least 1'),
  partner_prefix: z.string().max(5, 'Prefix must be at most 5 characters').min(1, 'Prefix is required'),
  partner_start_number: z.coerce.number().int().min(1, 'Start number must be at least 1'),
  agent_prefix: z.string().max(5, 'Prefix must be at most 5 characters').min(1, 'Prefix is required'),
  agent_start_number: z.coerce.number().int().min(1, 'Start number must be at least 1'),
});

type PrefixSettingsFormValues = z.infer<typeof prefixSettingsSchema>;

interface PrefixSetupFormProps {
  companyId: number;
  onComplete?: () => void;
  initialValues?: any;
}

export function PrefixSetupForm({ companyId, onComplete, initialValues }: PrefixSetupFormProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  // Function to directly show the error toast with the example data
  const showErrorToast = () => {
    // Create the error response object
    const errorResponse = {
      data: null,
      message: "Cannot update prefix settings because there is already data in the system. This would break existing reference codes.",
      code: "PREFIX_SETTINGS_UPDATE_DENIED",
      details: {
        collections: 30,
        loans: 1,
        partners: 0,
        agents: 0,
        customers: 2
      }
    };

    // Format the details into a readable string
    const { collections, loans, partners, agents, customers } = errorResponse.details;
    const detailsText = [
      collections > 0 ? `${collections} collections` : null,
      loans > 0 ? `${loans} loans` : null,
      partners > 0 ? `${partners} partners` : null,
      agents > 0 ? `${agents} agents` : null,
      customers > 0 ? `${customers} customers` : null
    ].filter(Boolean).join(', ');

    // Show the toast with the error message
    toast({
      variant: 'destructive',
      title: 'Cannot Update Prefix Settings',
      description: (
        <div className="space-y-2">
          <p>{errorResponse.message}</p>
          <p className="font-medium">Found: {detailsText}</p>
        </div>
      ),
      duration: 5000, // Show for 5 seconds
    });
  };

  // Set up form with default or initial values
  const form = useForm<PrefixSettingsFormValues>({
    resolver: zodResolver(prefixSettingsSchema),
    defaultValues: initialValues ? {
      loan_prefix: initialValues.loan_prefix || 'AB',
      loan_start_number: initialValues.loan_start_number || 1,
      collection_prefix: initialValues.collection_prefix || 'AB',
      collection_start_number: initialValues.collection_start_number || 1,
      customer_prefix: initialValues.customer_prefix || 'AB',
      customer_start_number: initialValues.customer_start_number || 1,
      partner_prefix: initialValues.partner_prefix || 'AB',
      partner_start_number: initialValues.partner_start_number || 1,
      agent_prefix: initialValues.agent_prefix || 'AB',
      agent_start_number: initialValues.agent_start_number || 1,
    } : {
      loan_prefix: 'AB',
      loan_start_number: 1,
      collection_prefix: 'AB',
      collection_start_number: 1,
      customer_prefix: 'AB',
      customer_start_number: 1,
      partner_prefix: 'AB',
      partner_start_number: 1,
      agent_prefix: 'AB',
      agent_start_number: 1,
    },
  });

  // Create or update prefix settings mutation
  const mutation = useMutation({
    mutationFn: async (data: PrefixSettingsFormValues) => {
      // Determine if we're creating or updating based on initialValues
      const method = initialValues ? 'PUT' : 'POST';
      const response = await apiRequest(
        method,
        `/api/companies/${companyId}/prefix-settings`,
        {
          ...data,
          company_id: companyId,
        }
      );

      const responseData = await response.json();

      // If the response is not successful, throw an error with the response data
      if (!response.ok) {
        const error = new Error(responseData.message || 'Failed to save prefix settings');
        (error as any).response = responseData;
        throw error;
      }

      return responseData;
    },
    onSuccess: (data) => {
      // Invalidate the prefix settings query to refetch the latest data
      queryClient.invalidateQueries({ queryKey: [`/api/companies/${companyId}/prefix-settings`] });

      toast({
        title: 'Success',
        description: data.message || (initialValues
          ? 'Prefix settings updated successfully'
          : 'Prefix settings created successfully'),
      });

      if (onComplete) {
        onComplete();
      }
    },
    onError: (error: any, variables, context) => {
      console.error('Error saving prefix settings:', error);

      // Try to parse the error response
      let errorMessage = 'Failed to save prefix settings';
      let errorDetails = null;
      let recordCounts = null;

      try {
        if (error.response) {
          const responseData = error.response;
          if (responseData.message) {
            errorMessage = responseData.message;
          }
          
          // Check both details and recordCounts fields (API might use either)
          if (responseData.recordCounts) {
            recordCounts = responseData.recordCounts;
          } else if (responseData.details) {
            recordCounts = responseData.details;
          }
        } else if (error.message) {
          errorMessage = error.message;
        }
      } catch (e) {
        console.error('Error parsing error response:', e);
      }

      // If we have record counts, format them nicely
      if (recordCounts) {
        const { collections = 0, loans = 0, partners = 0, agents = 0, customers = 0 } = recordCounts;
        const detailsText = [
          collections > 0 ? `${collections} collections` : null,
          loans > 0 ? `${loans} loans` : null,
          partners > 0 ? `${partners} partners` : null,
          agents > 0 ? `${agents} agents` : null,
          customers > 0 ? `${customers} customers` : null
        ].filter(Boolean).join(', ');

        // Show a more detailed toast with formatted record counts
        toast({
          variant: 'destructive',
          title: 'Cannot Update Prefix Settings',
          description: (
            <div className="space-y-2">
              <p>{errorMessage}</p>
              {detailsText && <p className="font-medium">Found: {detailsText}</p>}
            </div>
          ),
          duration: 5000, // Show for 5 seconds
        });
      } else {
        // Simple error toast for other types of errors
        toast({
          variant: 'destructive',
          title: 'Error',
          description: errorMessage,
        });
      }
    }
  });

  // Handle form submission
  const savePrefixSettings = (data: PrefixSettingsFormValues) => {
    mutation.mutate(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Setting up Profile</CardTitle>
        <CardDescription>
          Configure reference code prefixes and starting numbers for your entities
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(savePrefixSettings)} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Loan Prefix */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Loans Prefix</h3>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="loan_prefix"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} maxLength={5} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <span>+</span>
                  <FormField
                    control={form.control}
                    name="loan_start_number"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} type="number" min={1} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Collection Prefix */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Collection Prefix</h3>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="collection_prefix"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} maxLength={5} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <span>+</span>
                  <FormField
                    control={form.control}
                    name="collection_start_number"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} type="number" min={1} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Customer Prefix */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Customer Prefix</h3>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="customer_prefix"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} maxLength={5} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <span>+</span>
                  <FormField
                    control={form.control}
                    name="customer_start_number"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} type="number" min={1} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Partner Prefix */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Partner Prefix</h3>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="partner_prefix"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} maxLength={5} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <span>+</span>
                  <FormField
                    control={form.control}
                    name="partner_start_number"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} type="number" min={1} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Agent Prefix */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Agent Prefix</h3>
                <div className="flex items-center gap-2">
                  <FormField
                    control={form.control}
                    name="agent_prefix"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} maxLength={5} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <span>+</span>
                  <FormField
                    control={form.control}
                    name="agent_start_number"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input {...field} type="number" min={1} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2">
              <Button type="submit" disabled={mutation.isPending}>
                {mutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
                Save
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
      <CardFooter className="text-sm text-muted-foreground">
        Once saved, you should not be able to change this information.
      </CardFooter>
    </Card>
  );
}
