import { 
  users, companies, customers, agents, loans, collections, userCompanies,
  resellers, resellerClients, resellerCommissions, referrals,
  subscriptionPlans, subscriptions, partners, formTemplates, formFields, formSubmissions,
  loanConfigurations, paymentSchedules, payments, expenses, fines,
  // Financial management tables
  accounts, transactions, accountBalances, accountingPeriods,
  shareholders, shareholdings, investmentTransactions, profitDistributions,
  balanceSheets, balanceSheetItems, fixedAssets, depreciationSchedules,
  // User types
  type User, type InsertUser, type Company, type InsertCompany,
  type Customer, type InsertCustomer, type Agent, type InsertAgent,
  type Loan, type InsertLoan, type Collection, type InsertCollection,
  type Reseller, type InsertReseller, type ResellerClient, type InsertResellerClient,
  type ResellerCommission, type InsertResellerCommission, type Referral, type InsertReferral,
  type SubscriptionPlan, type InsertSubscriptionPlan, type Subscription, type InsertSubscription,
  type UserCompany, type InsertUserCompany, type Partner, type InsertPartner,
  type FormTemplate, type InsertFormTemplate, type FormField, type InsertFormField,
  type FormSubmission, type InsertFormSubmission, type LoanConfiguration, type InsertLoanConfiguration,
  type PaymentSchedule, type InsertPaymentSchedule, type Payment, type InsertPayment,
  type Expense, type InsertExpense, type Fine, type InsertFine,
  // Financial management types
  type Account, type InsertAccount, type Transaction, type InsertTransaction,
  type AccountBalance, type InsertAccountBalance, type AccountingPeriod, type InsertAccountingPeriod,
  type Shareholder, type InsertShareholder, type Shareholding, type InsertShareholding,
  type InvestmentTransaction, type InsertInvestmentTransaction, 
  type ProfitDistribution, type InsertProfitDistribution,
  type BalanceSheet, type InsertBalanceSheet, type BalanceSheetItem, type InsertBalanceSheetItem,
  type FixedAsset, type InsertFixedAsset, 
  // Report interfaces
  type DailyCollectionReport, type DailyGroupedData, type DailyCollectionItem, 
  type DaySheetReport, type CustomerReport,
  type AgentReport, type ProfitLossReport, type AccountStatement, type AccountBalanceReport,
  type BalanceSheetReport, type BalanceSheetDetail, type CashFlowReport, type ShareholderReport
} from "@shared/schema";

// Import finance management functions
import * as financialManagement from './financialManagement';

// Import other dependencies
import { db } from './db';
import { eq, and, desc, sql, count, sum } from 'drizzle-orm';
import bcrypt from 'bcrypt';
import errorLogger from './utils/errorLogger';

/**
 * Storage interface that defines all data access operations for the system
 */
export interface IStorage {
  // ... (all other existing interface methods)
  
  // Report methods
  getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport>;
  
  getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]>;
}

/**
 * DatabaseStorage class that implements all required methods from IStorage
 */
export class DatabaseStorage implements IStorage {
  // ... (all other existing methods)
  
  /**
   * Creates a daily collections report for the company
   */
  async getDailyCollectionsReport(
    companyId: number,
    startDate: string,
    endDate: string,
    status?: string,
    agentId?: number,
    branchId?: number,
    paymentMethod?: string
  ): Promise<DailyCollectionReport> {
    try {
      const startDateObj = new Date(startDate);
      const endDateObj = new Date(endDate);
      
      errorLogger.logDebug('Generating daily collections report with params', 'storage', {
        companyId, startDate, endDate, status, agentId, branchId, paymentMethod
      });
      
      // Create the base query
      const baseQuery = db.select({
        id: collections.id,
        loan_id: collections.loan_id,
        customer_id: collections.customer_id,
        agent_id: collections.agent_id,
        amount: collections.amount,
        scheduled_date: collections.scheduled_date,
        collection_date: collections.collection_date,
        status: collections.status,
        payment_method: collections.payment_method,
        receipt_id: collections.receipt_id,
      })
      .from(collections)
      .where(eq(collections.company_id, companyId));
      
      // Add filters one by one
      const whereConditions = [];
      
      // Date range filters using SQL directly
      whereConditions.push(sql`${collections.scheduled_date} >= ${startDateObj}`);
      whereConditions.push(sql`${collections.scheduled_date} <= ${endDateObj}`);
      
      // Optional filters
      if (status) {
        whereConditions.push(eq(collections.status, status));
      }
      
      if (agentId) {
        whereConditions.push(eq(collections.agent_id, agentId));
      }
      
      // Branch filter might not be supported
      // if (branchId) {
      //   whereConditions.push(eq(collections.branch_id, branchId));
      // }
      
      if (paymentMethod) {
        whereConditions.push(eq(collections.payment_method, paymentMethod));
      }
      
      // Execute query with all conditions
      const collectionResults = await baseQuery.where(and(...whereConditions));
      
      errorLogger.logDebug('Fetched collection results', 'storage', {
        count: collectionResults.length
      });
      
      // Get customer and agent names for display
      const collectionItems: DailyCollectionItem[] = await Promise.all(
        collectionResults.map(async (collection) => {
          let customerName = 'Unknown Customer';
          let agentName = null;
          
          // Get customer name
          if (collection.customer_id) {
            const customer = await this.getCustomer(collection.customer_id);
            if (customer) {
              customerName = customer.full_name;
            }
          }
          
          // Get agent name if applicable
          if (collection.agent_id) {
            const agent = await this.getAgent(collection.agent_id);
            if (agent) {
              agentName = agent.full_name;
            }
          }
          
          return {
            ...collection,
            customerName,
            agentName,
            amount: collection.amount.toString()
          };
        })
      );
      
      // Group by date
      const groupedByDate: Record<string, DailyGroupedData> = {};
      
      // Process each collection item
      for (const item of collectionItems) {
        const dateKey = new Date(item.scheduled_date).toISOString().split('T')[0];
        
        if (!groupedByDate[dateKey]) {
          groupedByDate[dateKey] = {
            date: dateKey,
            collections: [],
            totalAmount: 0,
            completedAmount: 0,
            pendingAmount: 0
          };
        }
        
        groupedByDate[dateKey].collections.push(item);
        
        const amount = parseFloat(item.amount);
        groupedByDate[dateKey].totalAmount += amount;
        
        if (item.status === 'completed') {
          groupedByDate[dateKey].completedAmount += amount;
        } else {
          groupedByDate[dateKey].pendingAmount += amount;
        }
      }
      
      // Convert to array and sort by date
      const dailyData = Object.values(groupedByDate).sort((a, b) => 
        new Date(a.date).getTime() - new Date(b.date).getTime()
      );
      
      // Calculate totals
      let totalCollected = 0;
      let totalPending = 0;
      
      for (const day of dailyData) {
        totalCollected += day.completedAmount;
        totalPending += day.pendingAmount;
      }
      
      return {
        startDate,
        endDate,
        totalCollected,
        totalPending,
        dailyData,
        rawData: collectionItems
      };
    } catch (error) {
      errorLogger.error('Error generating daily collections report', error);
      // Return empty report structure on error
      return {
        startDate,
        endDate,
        totalCollected: 0,
        totalPending: 0,
        dailyData: [],
        rawData: []
      };
    }
  }
  
  /**
   * Get active loan configurations for a company
   */
  async getActiveLoanConfigurations(companyId: number): Promise<LoanConfiguration[]> {
    try {
      // Fetch active loan configurations with their associated templates
      const configs = await db.select()
        .from(loanConfigurations)
        .where(
          and(
            eq(loanConfigurations.company_id, companyId),
            eq(loanConfigurations.is_active, true)
          )
        );
      
      // For each configuration, fetch its template
      const configsWithTemplates = await Promise.all(
        configs.map(async (config) => {
          const [template] = await db.select()
            .from(formTemplates)
            .where(eq(formTemplates.id, config.template_id));
          
          return {
            ...config,
            template
          };
        })
      );
      
      return configsWithTemplates;
    } catch (error) {
      errorLogger.error('Error fetching active loan configurations', error);
      return [];
    }
  }
}

// Export an instance of the DatabaseStorage class
export const storage = new DatabaseStorage();