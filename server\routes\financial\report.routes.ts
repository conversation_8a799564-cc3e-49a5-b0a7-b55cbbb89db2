import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';

export function registerReportRoutes(app: Express): void {
  // Get profit/loss report
  app.get('/api/companies/:companyId/reports/profit-loss', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters
      const startDate = req.query.start_date as string || new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 90 days ago
      const endDate = req.query.end_date as string || new Date().toISOString().split('T')[0]; // Default to today
      
      console.log(`Generating profit/loss report for company ${companyId} from ${startDate} to ${endDate}`);
      
      const report = await storage.getProfitLossReport(companyId, { startDate, endDate });
      return res.json(report);
    } catch (error) {
      console.error(`Error generating profit/loss report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get cash flow report
  app.get('/api/companies/:companyId/reports/cash-flow', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters
      const startDate = req.query.start_date as string || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 30 days ago
      const endDate = req.query.end_date as string || new Date().toISOString().split('T')[0]; // Default to today
      
      console.log(`Generating cash flow report for company ${companyId} from ${startDate} to ${endDate}`);
      
      const report = await storage.getCashFlowReport(companyId, { startDate, endDate });
      return res.json(report);
    } catch (error) {
      console.error(`Error generating cash flow report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get collection report
  app.get('/api/companies/:companyId/reports/collections', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);
      
      // Parse query parameters
      const startDate = req.query.start_date as string || new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]; // Default to 30 days ago
      const endDate = req.query.end_date as string || new Date().toISOString().split('T')[0]; // Default to today
      
      console.log(`Generating collection report for company ${companyId} from ${startDate} to ${endDate}`);
      
      const report = await storage.getCollectionReport(companyId, { startDate, endDate });
      return res.json(report);
    } catch (error) {
      console.error(`Error generating collection report for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
