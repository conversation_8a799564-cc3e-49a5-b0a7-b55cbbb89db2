import { Express, Response } from 'express';
import { AuthRequest } from '../middleware/auth';
import { db } from '../db';
import { permissions, customRoles, rolePermissions, userRoles } from '../../shared/schema';
import { eq, inArray, and, sql } from 'drizzle-orm';

export function registerPermissionsDashboardRoutes(app: Express): void {

  // Get permission categories with grouped permissions - no auth required for demo
  app.get('/api/dashboard/permissions/categories', async (req: AuthRequest, res: Response) => {
    try {
      // Return demo data since database might not have all the data
      const demoCategories = [
        {
          category: 'user_management',
          metadata: {
            name: 'User Management',
            description: 'Permissions for managing users and access',
            icon: 'UserCheck'
          },
          permissions: [
            { id: 1, code: 'user_view', name: 'View Users', description: 'View user information', category: 'user_management' },
            { id: 2, code: 'user_create', name: 'Create Users', description: 'Create new users', category: 'user_management' },
            { id: 3, code: 'user_edit', name: 'Edit Users', description: 'Edit user information', category: 'user_management' },
            { id: 4, code: 'user_delete', name: 'Delete Users', description: 'Delete users', category: 'user_management' }
          ]
        },
        {
          category: 'loan_management',
          metadata: {
            name: 'Loan Management',
            description: 'Permissions related to loan creation, approval, and management',
            icon: 'CreditCard'
          },
          permissions: [
            { id: 5, code: 'loan_create_basic', name: 'Create Basic Loans', description: 'Create basic loan applications', category: 'loan_management' },
            { id: 6, code: 'loan_approve_basic', name: 'Approve Basic Loans', description: 'Approve basic loan applications', category: 'loan_management' },
            { id: 7, code: 'loan_view', name: 'View Loans', description: 'View loan information', category: 'loan_management' },
            { id: 8, code: 'loan_edit', name: 'Edit Loans', description: 'Edit loan information', category: 'loan_management' }
          ]
        },
        {
          category: 'financial_management',
          metadata: {
            name: 'Financial Management',
            description: 'Permissions for payment processing and financial operations',
            icon: 'DollarSign'
          },
          permissions: [
            { id: 9, code: 'payment_process', name: 'Process Payments', description: 'Process loan payments', category: 'financial_management' },
            { id: 10, code: 'payment_view', name: 'View Payments', description: 'View payment information', category: 'financial_management' },
            { id: 11, code: 'financial_reports', name: 'Financial Reports', description: 'Access financial reports', category: 'financial_management' }
          ]
        },
        {
          category: 'role_management',
          metadata: {
            name: 'Role Management',
            description: 'Permissions for managing roles and permissions',
            icon: 'Shield'
          },
          permissions: [
            { id: 12, code: 'role_view', name: 'View Roles', description: 'View role information', category: 'role_management' },
            { id: 13, code: 'role_create', name: 'Create Roles', description: 'Create new roles', category: 'role_management' },
            { id: 14, code: 'role_edit', name: 'Edit Roles', description: 'Edit role information', category: 'role_management' },
            { id: 15, code: 'permission_assign', name: 'Assign Permissions', description: 'Assign permissions to roles', category: 'role_management' }
          ]
        }
      ];

      return res.json(demoCategories);
    } catch (error) {
      console.error('Error fetching permission categories:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get permission usage analytics - no auth required for demo
  app.get('/api/dashboard/permissions/analytics', async (req: AuthRequest, res: Response) => {
    try {
      // Return demo analytics data
      const demoAnalytics = {
        permission_distribution: [
          {
            category: 'user_management',
            code: 'user_view',
            name: 'View Users',
            role_count: 3,
            user_count: 5
          },
          {
            category: 'user_management',
            code: 'user_create',
            name: 'Create Users',
            role_count: 2,
            user_count: 2
          },
          {
            category: 'loan_management',
            code: 'loan_create_basic',
            name: 'Create Basic Loans',
            role_count: 4,
            user_count: 8
          },
          {
            category: 'financial_management',
            code: 'payment_process',
            name: 'Process Payments',
            role_count: 2,
            user_count: 3
          }
        ],
        role_statistics: [
          {
            role_name: 'Company Admin',
            description: 'Full company administration access',
            permission_count: 15,
            user_count: 2
          },
          {
            role_name: 'Loan Officer',
            description: 'Loan management and processing',
            permission_count: 8,
            user_count: 5
          },
          {
            role_name: 'Financial Manager',
            description: 'Financial operations and reporting',
            permission_count: 6,
            user_count: 3
          },
          {
            role_name: 'Employee',
            description: 'Basic access permissions',
            permission_count: 4,
            user_count: 12
          }
        ],
        category_statistics: [
          {
            category: 'user_management',
            total_permissions: 6,
            assigned_permissions: 4,
            users_with_category_permissions: 7
          },
          {
            category: 'loan_management',
            total_permissions: 12,
            assigned_permissions: 8,
            users_with_category_permissions: 15
          },
          {
            category: 'financial_management',
            total_permissions: 8,
            assigned_permissions: 5,
            users_with_category_permissions: 8
          },
          {
            category: 'role_management',
            total_permissions: 10,
            assigned_permissions: 6,
            users_with_category_permissions: 3
          }
        ],
        generated_at: new Date().toISOString(),
        note: 'Demo data - authentication required for real analytics'
      };

      return res.json(demoAnalytics);
    } catch (error) {
      console.error('Error fetching permission analytics:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Bulk assign permissions to roles - simplified for demo
  app.post('/api/dashboard/permissions/bulk-assign', async (req: AuthRequest, res: Response) => {
    try {
      const { assignments } = req.body;

      if (!Array.isArray(assignments)) {
        return res.status(400).json({ message: 'Assignments must be an array' });
      }

      // For demo purposes, just return success
      return res.json({
        message: 'Bulk permission assignment completed (demo mode)',
        successful: assignments.length,
        failed: 0,
        results: assignments.map((assignment, index) => ({
          role_id: assignment.role_id,
          action: assignment.action,
          permission_count: assignment.permission_ids?.length || 0,
          demo: true
        })),
        note: 'Demo mode - no actual database changes made'
      });
    } catch (error) {
      console.error('Error in bulk permission assignment:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Note: Role-specific endpoints (/api/roles/:id/permissions, /api/roles/:id/effective-permissions,
  // /api/roles/:id/temporary-permissions) are handled by role.routes.ts to avoid conflicts

  // Test endpoint
  app.get('/api/dashboard/permissions/test', (req: any, res: Response) => {
    res.json({
      message: 'Permissions dashboard test endpoint working',
      timestamp: new Date().toISOString(),
      note: 'No authentication required for demo'
    });
  });
}
