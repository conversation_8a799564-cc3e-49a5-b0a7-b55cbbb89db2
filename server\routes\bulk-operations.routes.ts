import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { bulkOperationsService } from '../services/bulkOperationsService';
import { z } from 'zod';
import multer from 'multer';
import { insertUserTemplateSchema } from '@shared/schema';

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype === 'text/csv' || file.originalname.endsWith('.csv')) {
      cb(null, true);
    } else {
      cb(new Error('Only CSV files are allowed'));
    }
  }
});

// Validation schemas
const bulkRoleAssignmentSchema = z.object({
  user_ids: z.array(z.number()).min(1, 'At least one user ID is required'),
  role_ids: z.array(z.number()).min(1, 'At least one role ID is required'),
  action: z.enum(['assign', 'remove']),
  justification: z.string().optional(),
});

const userTemplateUpdateSchema = z.object({
  name: z.string().min(1).optional(),
  description: z.string().optional(),
  template_config: z.any().optional(),
  default_role: z.enum(['saas_admin', 'reseller', 'company_admin', 'employee', 'agent', 'customer', 'partner']).optional(),
  default_permissions: z.array(z.string()).optional(),
  default_roles: z.array(z.number()).optional(),
  default_branch_id: z.number().nullable().optional(),
  default_department_id: z.number().nullable().optional(),
  is_active: z.boolean().optional(),
});

export function registerBulkOperationsRoutes(app: Express): void {

  // User Templates Routes

  // Get user templates for a company
  app.get('/api/bulk-operations/user-templates', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const templates = await bulkOperationsService.getUserTemplates(companyId);
      return res.json(templates);
    } catch (error: any) {
      console.error('Error fetching user templates:', error);
      return res.status(500).json({ message: error.message || 'Failed to fetch user templates' });
    }
  });

  // Create user template
  app.post('/api/bulk-operations/user-templates', authMiddleware, requirePermission('user_create'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = insertUserTemplateSchema.safeParse({
        ...req.body,
        company_id: req.user.company_id,
        created_by: req.user.id
      });

      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const template = await bulkOperationsService.createUserTemplate(result.data);
      return res.status(201).json(template);
    } catch (error: any) {
      console.error('Error creating user template:', error);
      return res.status(400).json({ message: error.message || 'Failed to create user template' });
    }
  });

  // Update user template
  app.put('/api/bulk-operations/user-templates/:id', authMiddleware, requirePermission('user_edit'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const templateId = parseInt(req.params.id);
      const companyId = req.user.company_id;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const result = userTemplateUpdateSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const template = await bulkOperationsService.updateUserTemplate(templateId, companyId, result.data);
      if (!template) {
        return res.status(404).json({ message: 'User template not found' });
      }

      return res.json(template);
    } catch (error: any) {
      console.error('Error updating user template:', error);
      return res.status(400).json({ message: error.message || 'Failed to update user template' });
    }
  });

  // Delete user template
  app.delete('/api/bulk-operations/user-templates/:id', authMiddleware, requirePermission('user_delete'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const templateId = parseInt(req.params.id);
      const companyId = req.user.company_id;

      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const deleted = await bulkOperationsService.deleteUserTemplate(templateId, companyId);
      if (!deleted) {
        return res.status(404).json({ message: 'User template not found' });
      }

      return res.json({ message: 'User template deleted successfully' });
    } catch (error: any) {
      console.error('Error deleting user template:', error);
      return res.status(400).json({ message: error.message || 'Failed to delete user template' });
    }
  });

  // Bulk Operations Routes

  // Import users from CSV
  app.post('/api/bulk-operations/import-users', authMiddleware, requirePermission('user_create'), upload.single('csvFile'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      if (!req.file) {
        return res.status(400).json({ message: 'CSV file is required' });
      }

      const csvContent = req.file.buffer.toString('utf-8');
      const result = await bulkOperationsService.importUsersFromCsv(csvContent, companyId, req.user.id);

      return res.json(result);
    } catch (error: any) {
      console.error('Error importing users:', error);
      return res.status(400).json({ message: error.message || 'Failed to import users' });
    }
  });

  // Export users to CSV
  app.get('/api/bulk-operations/export-users', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const csvContent = await bulkOperationsService.exportUsersToCsv(companyId, req.user.id);

      const filename = `users_export_${new Date().toISOString().split('T')[0]}.csv`;

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      return res.send(csvContent);
    } catch (error: any) {
      console.error('Error exporting users:', error);
      return res.status(500).json({ message: error.message || 'Failed to export users' });
    }
  });

  // Bulk role assignment
  app.post('/api/bulk-operations/bulk-role-assignment', authMiddleware, requirePermission('role_assign'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = bulkRoleAssignmentSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const bulkResult = await bulkOperationsService.bulkRoleAssignment(
        result.data,
        companyId,
        req.user.id
      );
      return res.json(bulkResult);
    } catch (error: any) {
      console.error('Error performing bulk role assignment:', error);
      return res.status(400).json({ message: error.message || 'Failed to perform bulk role assignment' });
    }
  });

  // Get bulk operation logs
  app.get('/api/bulk-operations/logs', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const companyId = req.user.company_id;
      if (!companyId) {
        return res.status(400).json({ message: 'Company ID is required' });
      }

      const logs = await bulkOperationsService.getBulkOperationLogs(companyId);
      return res.json(logs);
    } catch (error: any) {
      console.error('Error fetching bulk operation logs:', error);
      return res.status(500).json({ message: error.message || 'Failed to fetch bulk operation logs' });
    }
  });

  // Download CSV template
  app.get('/api/bulk-operations/csv-template', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      const csvTemplate = [
        'Full Name,Username,Email,Password,Role,Phone,Branch ID,Department ID,Manager ID,Template ID',
        'John Doe,johndoe,<EMAIL>,password123,employee,+919876543210,1,1,2,',
        'Jane Smith,janesmith,<EMAIL>,,agent,+919876543211,1,2,2,1'
      ].join('\n');

      res.setHeader('Content-Type', 'text/csv');
      res.setHeader('Content-Disposition', 'attachment; filename="user_import_template.csv"');

      return res.send(csvTemplate);
    } catch (error: any) {
      console.error('Error generating CSV template:', error);
      return res.status(500).json({ message: 'Failed to generate CSV template' });
    }
  });
}
