import { useState, useEffect } from 'react';
import { use<PERSON>ara<PERSON>, useLocation, Link } from 'wouter';
import { useQuery, useMutation } from '@tanstack/react-query';
import { queryClient, apiRequest } from '@/lib/queryClient';
import { useAuth } from '@/lib/auth';
import { useCompany } from '@/lib/companies';
import { useToast } from '@/hooks/use-toast';
import { useIsMobile } from '@/hooks/use-mobile';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Separator } from '@/components/ui/separator';
import { Users, Plus, Edit, Trash2, Calendar, Clock, MapPin, User, ArrowLeft, Building } from 'lucide-react';
import { cn } from '@/lib/utils';
import { Badge } from '@/components/ui/badge';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink, BreadcrumbList, BreadcrumbSeparator } from "@/components/ui/breadcrumb";

// Define the validation schema for groups
const groupFormSchema = z.object({
  company_id: z.number(),
  branch_id: z.number(),
  name: z.string().min(1, 'Group name is required'),
  description: z.string().optional().or(z.literal('')),
  leader_name: z.string().optional().or(z.literal('')),
  meeting_day: z.string().optional().or(z.literal('')),
  meeting_time: z.string().optional().or(z.literal('')),
  location: z.string().optional().or(z.literal('')),
  status: z.string().default('active')
});

type GroupFormData = z.infer<typeof groupFormSchema>;

export default function BranchGroupsPage() {
  const params = useParams();
  const [, navigate] = useLocation();
  const { toast } = useToast();
  const isMobile = useIsMobile();
  const { user } = useAuth();
  const { currentCompany } = useCompany();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedGroup, setSelectedGroup] = useState<any>(null);
  const branchId = Number(params.branchId);

  // Fetch branch details
  const {
    data: branch,
    isLoading: isBranchLoading,
    isError: isBranchError,
  } = useQuery({
    queryKey: ['/api/branches', branchId],
    queryFn: async () => {
      if (!branchId) return null;
      const res = await apiRequest('GET', `/api/branches/${branchId}`);
      return res.json();
    },
    enabled: !!branchId,
  });

  // Fetch groups for this branch
  const {
    data: groups,
    isLoading: isGroupsLoading,
    isError: isGroupsError,
  } = useQuery({
    queryKey: ['/api/branches', branchId, 'groups'],
    queryFn: async () => {
      if (!branchId) return [];
      const res = await apiRequest('GET', `/api/branches/${branchId}/groups`);
      return res.json();
    },
    enabled: !!branchId,
  });

  // Create group form
  const createForm = useForm<GroupFormData>({
    resolver: zodResolver(groupFormSchema),
    defaultValues: {
      company_id: currentCompany?.id,
      branch_id: branchId,
      name: '',
      description: '',
      leader_name: '',
      meeting_day: '',
      meeting_time: '',
      location: '',
      status: 'active'
    },
  });

  // Update form values when branch or company changes
  useEffect(() => {
    if (currentCompany?.id && branchId) {
      createForm.setValue('company_id', currentCompany.id);
      createForm.setValue('branch_id', branchId);
    }
  }, [currentCompany?.id, branchId, createForm]);

  // Edit group form
  const editForm = useForm<GroupFormData>({
    resolver: zodResolver(groupFormSchema),
    defaultValues: {
      company_id: currentCompany?.id,
      branch_id: branchId,
      name: '',
      description: '',
      leader_name: '',
      meeting_day: '',
      meeting_time: '',
      location: '',
      status: 'active'
    },
  });

  // Create group mutation
  const createGroupMutation = useMutation({
    mutationFn: async (data: GroupFormData) => {
      const res = await apiRequest('POST', '/api/groups', data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/branches', branchId, 'groups'] });
      setIsCreateDialogOpen(false);
      createForm.reset({
        company_id: currentCompany?.id,
        branch_id: branchId,
        name: '',
        description: '',
        leader_name: '',
        meeting_day: '',
        meeting_time: '',
        location: '',
        status: 'active'
      });
      toast({
        title: 'Group created',
        description: 'Group has been created successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to create group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Update group mutation
  const updateGroupMutation = useMutation({
    mutationFn: async (data: GroupFormData) => {
      const res = await apiRequest('PATCH', `/api/groups/${selectedGroup.id}`, data);
      return res.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/branches', branchId, 'groups'] });
      setIsEditDialogOpen(false);
      editForm.reset();
      toast({
        title: 'Group updated',
        description: 'Group has been updated successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to update group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Delete group mutation
  const deleteGroupMutation = useMutation({
    mutationFn: async (id: number) => {
      const res = await apiRequest('DELETE', `/api/groups/${id}`);
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.message || 'Failed to delete group');
      }
      return true;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['/api/branches', branchId, 'groups'] });
      setIsDeleteDialogOpen(false);
      setSelectedGroup(null);
      toast({
        title: 'Group deleted',
        description: 'Group has been deleted successfully.',
      });
    },
    onError: (error: any) => {
      toast({
        title: 'Error',
        description: error.message || 'Failed to delete group. Please try again.',
        variant: 'destructive',
      });
    },
  });

  // Handle form submission - create
  const handleCreateFormSubmit = createForm.handleSubmit((data) => {
    createGroupMutation.mutate({
      ...data,
      company_id: currentCompany?.id as number,
      branch_id: branchId
    });
  });

  // Handle form submission - edit
  const handleEditFormSubmit = editForm.handleSubmit((data) => {
    updateGroupMutation.mutate({
      ...data,
      company_id: currentCompany?.id as number,
      branch_id: branchId
    });
  });

  // Handle edit group
  const handleEditGroup = (group: any) => {
    setSelectedGroup(group);
    editForm.reset({
      company_id: currentCompany?.id,
      branch_id: branchId,
      name: group.name,
      description: group.description || '',
      leader_name: group.leader_name || '',
      meeting_day: group.meeting_day || '',
      meeting_time: group.meeting_time || '',
      location: group.location || '',
      status: group.status
    });
    setIsEditDialogOpen(true);
  };

  // Handle delete group
  const handleDeleteGroup = (group: any) => {
    setSelectedGroup(group);
    setIsDeleteDialogOpen(true);
  };

  const isLoading = isBranchLoading || isGroupsLoading;
  const isError = isBranchError || isGroupsError;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (isError || !branch) {
    return (
      <div className="container mx-auto p-4">
        <h1 className="text-xl font-bold mb-4">Branch Groups</h1>
        <div className="bg-red-50 border border-red-200 text-red-800 p-4 rounded">
          Error loading branch details. The branch may not exist or you don't have access.
        </div>
        <div className="mt-4">
          <Button variant="outline" onClick={() => navigate('/branches')}>
            <ArrowLeft className="mr-2 h-4 w-4" /> Back to Branches
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <Breadcrumb className="mb-6">
        <BreadcrumbList>
          <BreadcrumbItem>
            <BreadcrumbLink href="/branches">
              <Building className="h-4 w-4 mr-1 inline" /> Branches
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>
              {branch.name}
            </BreadcrumbLink>
          </BreadcrumbItem>
          <BreadcrumbSeparator />
          <BreadcrumbItem>
            <BreadcrumbLink>
              Groups
            </BreadcrumbLink>
          </BreadcrumbItem>
        </BreadcrumbList>
      </Breadcrumb>

      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-bold">{branch.name} - Groups</h1>
          <p className="text-gray-500">Manage groups in this branch</p>
        </div>
        <Button onClick={() => setIsCreateDialogOpen(true)}>
          <Plus className="mr-2 h-4 w-4" /> Add Group
        </Button>
      </div>

      {branch.manager_name || branch.phone || branch.email || branch.address ? (
        <Card className="mb-6">
          <CardHeader className="pb-2">
            <CardTitle className="text-md">Branch Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {branch.manager_name && (
                <div>
                  <span className="font-medium">Manager:</span> {branch.manager_name}
                </div>
              )}
              {branch.phone && (
                <div>
                  <span className="font-medium">Phone:</span> {branch.phone}
                </div>
              )}
              {branch.email && (
                <div>
                  <span className="font-medium">Email:</span> {branch.email}
                </div>
              )}
              {branch.address && (
                <div>
                  <span className="font-medium">Address:</span> {branch.address}
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      ) : null}

      {groups?.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center p-6">
            <Users className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium">No groups in this branch yet</h3>
            <p className="text-gray-500 text-center mb-4">
              Create groups to organize teams in this branch
            </p>
            <Button onClick={() => setIsCreateDialogOpen(true)}>
              <Plus className="mr-2 h-4 w-4" /> Add First Group
            </Button>
          </CardContent>
        </Card>
      ) : isMobile ? (
        <div className="grid grid-cols-1 gap-4">
          {groups.map((group: any) => (
            <Card key={group.id} className="overflow-hidden">
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">{group.name}</CardTitle>
                  <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                    {group.status === 'active' ? 'Active' : 'Inactive'}
                  </Badge>
                </div>
              </CardHeader>
              <CardContent className="pb-2">
                <div className="space-y-2 text-sm">
                  {group.description && (
                    <div className="text-gray-600 mb-2">{group.description}</div>
                  )}
                  {group.leader_name && (
                    <div className="flex items-center gap-2">
                      <User className="h-4 w-4 text-gray-500" />
                      <span>{group.leader_name}</span>
                    </div>
                  )}
                  {group.meeting_day && (
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4 text-gray-500" />
                      <span>{group.meeting_day}</span>
                      {group.meeting_time && (
                        <><Clock className="h-4 w-4 ml-2 text-gray-500" /> {group.meeting_time}</>
                      )}
                    </div>
                  )}
                  {group.location && (
                    <div className="flex items-center gap-2">
                      <MapPin className="h-4 w-4 text-gray-500" />
                      <span>{group.location}</span>
                    </div>
                  )}
                </div>
              </CardContent>
              <CardFooter className="flex justify-end pt-2">
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleEditGroup(group)}
                  >
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteGroup(group)}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      ) : (
        <div className="bg-white rounded-md shadow">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Leader</TableHead>
                <TableHead>Meeting Schedule</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {groups.map((group: any) => (
                <TableRow key={group.id}>
                  <TableCell className="font-medium">
                    <div>
                      {group.name}
                      {group.description && (
                        <div className="text-xs text-gray-500 mt-1">{group.description}</div>
                      )}
                    </div>
                  </TableCell>
                  <TableCell>{group.leader_name || '-'}</TableCell>
                  <TableCell>
                    {group.meeting_day ? (
                      <div>
                        <div>{group.meeting_day}</div>
                        {group.meeting_time && (
                          <div className="text-xs text-gray-500">{group.meeting_time}</div>
                        )}
                        {group.location && (
                          <div className="text-xs text-gray-500 flex items-center">
                            <MapPin className="h-3 w-3 mr-1" /> {group.location}
                          </div>
                        )}
                      </div>
                    ) : (
                      '-'
                    )}
                  </TableCell>
                  <TableCell>
                    <Badge variant={group.status === 'active' ? 'default' : 'secondary'}>
                      {group.status === 'active' ? 'Active' : 'Inactive'}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <span className="sr-only">Actions</span>
                            <svg
                              width="15"
                              height="15"
                              viewBox="0 0 15 15"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                              className="h-4 w-4"
                            >
                              <path
                                d="M3.625 7.5C3.625 8.12132 3.12132 8.625 2.5 8.625C1.87868 8.625 1.375 8.12132 1.375 7.5C1.375 6.87868 1.87868 6.375 2.5 6.375C3.12132 6.375 3.625 6.87868 3.625 7.5ZM8.625 7.5C8.625 8.12132 8.12132 8.625 7.5 8.625C6.87868 8.625 6.375 8.12132 6.375 7.5C6.375 6.87868 6.87868 6.375 7.5 6.375C8.12132 6.375 8.625 6.87868 8.625 7.5ZM13.625 7.5C13.625 8.12132 13.1213 8.625 12.5 8.625C11.8787 8.625 11.375 8.12132 11.375 7.5C11.375 6.87868 11.8787 6.375 12.5 6.375C13.1213 6.375 13.625 6.87868 13.625 7.5Z"
                                fill="currentColor"
                                fillRule="evenodd"
                                clipRule="evenodd"
                              ></path>
                            </svg>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleEditGroup(group)}>
                            <Edit className="mr-2 h-4 w-4" /> Edit
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            className="text-red-600"
                            onClick={() => handleDeleteGroup(group)}
                          >
                            <Trash2 className="mr-2 h-4 w-4" /> Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}

      <div className="mt-6">
        <Button variant="outline" onClick={() => navigate('/branches')}>
          <ArrowLeft className="mr-2 h-4 w-4" /> Back to Branches
        </Button>
      </div>

      {/* Create Group Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Add New Group</DialogTitle>
            <DialogDescription>
              Create a new group for the {branch.name} branch.
            </DialogDescription>
          </DialogHeader>
          <Form {...createForm}>
            <form onSubmit={handleCreateFormSubmit} className="space-y-4">
              <FormField
                control={createForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="Sales Team" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Brief description of the group" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={createForm.control}
                name="leader_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Leader</FormLabel>
                    <FormControl>
                      <Input placeholder="Leader name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={createForm.control}
                  name="meeting_day"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Day</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value || ""}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Monday">Monday</SelectItem>
                            <SelectItem value="Tuesday">Tuesday</SelectItem>
                            <SelectItem value="Wednesday">Wednesday</SelectItem>
                            <SelectItem value="Thursday">Thursday</SelectItem>
                            <SelectItem value="Friday">Friday</SelectItem>
                            <SelectItem value="Saturday">Saturday</SelectItem>
                            <SelectItem value="Sunday">Sunday</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={createForm.control}
                  name="meeting_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={createForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Meeting room or location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsCreateDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={createGroupMutation.isPending}>
                  {createGroupMutation.isPending ? (
                    <>
                      <span className="animate-spin mr-2">⟳</span> Creating...
                    </>
                  ) : (
                    "Create Group"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Edit Group Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[500px]">
          <DialogHeader>
            <DialogTitle>Edit Group</DialogTitle>
            <DialogDescription>
              Update the group details.
            </DialogDescription>
          </DialogHeader>
          <Form {...editForm}>
            <form onSubmit={handleEditFormSubmit} className="space-y-4">
              <FormField
                control={editForm.control}
                name="name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Name*</FormLabel>
                    <FormControl>
                      <Input placeholder="Sales Team" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Description</FormLabel>
                    <FormControl>
                      <Textarea placeholder="Brief description of the group" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="leader_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Group Leader</FormLabel>
                    <FormControl>
                      <Input placeholder="Leader name" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={editForm.control}
                  name="meeting_day"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Day</FormLabel>
                      <FormControl>
                        <Select
                          value={field.value || ""}
                          onValueChange={field.onChange}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select day" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="none">None</SelectItem>
                            <SelectItem value="Monday">Monday</SelectItem>
                            <SelectItem value="Tuesday">Tuesday</SelectItem>
                            <SelectItem value="Wednesday">Wednesday</SelectItem>
                            <SelectItem value="Thursday">Thursday</SelectItem>
                            <SelectItem value="Friday">Friday</SelectItem>
                            <SelectItem value="Saturday">Saturday</SelectItem>
                            <SelectItem value="Sunday">Sunday</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <FormField
                  control={editForm.control}
                  name="meeting_time"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Meeting Time</FormLabel>
                      <FormControl>
                        <Input type="time" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              <FormField
                control={editForm.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Meeting Location</FormLabel>
                    <FormControl>
                      <Input placeholder="Meeting room or location" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={editForm.control}
                name="status"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Status</FormLabel>
                    <FormControl>
                      <Select
                        value={field.value}
                        onValueChange={field.onChange}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="active">Active</SelectItem>
                          <SelectItem value="inactive">Inactive</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <DialogFooter>
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setIsEditDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button type="submit" disabled={updateGroupMutation.isPending}>
                  {updateGroupMutation.isPending ? (
                    <>
                      <span className="animate-spin mr-2">⟳</span> Updating...
                    </>
                  ) : (
                    "Update Group"
                  )}
                </Button>
              </DialogFooter>
            </form>
          </Form>
        </DialogContent>
      </Dialog>

      {/* Delete Group Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="sm:max-w-[425px]">
          <DialogHeader>
            <DialogTitle>Delete Group</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this group? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            {selectedGroup && (
              <div className="bg-gray-50 p-4 rounded-md">
                <p><strong>Group:</strong> {selectedGroup.name}</p>
                {selectedGroup.leader_name && (
                  <p><strong>Leader:</strong> {selectedGroup.leader_name}</p>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsDeleteDialogOpen(false)}
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={() => selectedGroup && deleteGroupMutation.mutate(selectedGroup.id)}
              disabled={deleteGroupMutation.isPending}
            >
              {deleteGroupMutation.isPending ? (
                <>
                  <span className="animate-spin mr-2">⟳</span> Deleting...
                </>
              ) : (
                "Delete Group"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}