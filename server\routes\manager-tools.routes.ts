import { Express, Response } from 'express';
import { authMiddleware, requirePermission, AuthRequest } from '../middleware/auth';
import { managerToolsService } from '../services/managerToolsService';
import { z } from 'zod';

// Validation schemas
const bulkPermissionOperationSchema = z.object({
  user_ids: z.array(z.number()).min(1, 'At least one user ID is required'),
  permission_codes: z.array(z.string()).optional(),
  role_ids: z.array(z.number()).optional(),
  action: z.enum(['assign', 'remove']),
  justification: z.string().optional(),
});

export function registerManagerToolsRoutes(app: Express): void {
  // Get team members for a manager
  app.get('/api/manager-tools/team-members', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const managerId = req.user.id;
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : req.user.company_id;

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      const teamMembers = await managerToolsService.getTeamMembers(managerId, companyId);
      return res.json(teamMembers);
    } catch (error) {
      console.error('Error fetching team members:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get team permission summary
  app.get('/api/manager-tools/team-summary', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const managerId = req.user.id;
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : req.user.company_id;

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      const summary = await managerToolsService.getTeamPermissionSummary(managerId, companyId);
      return res.json(summary);
    } catch (error) {
      console.error('Error fetching team permission summary:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get permission analytics for team
  app.get('/api/manager-tools/analytics', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const managerId = req.user.id;
      const companyId = req.query.company_id ? parseInt(req.query.company_id as string) : req.user.company_id;

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      const analytics = await managerToolsService.getPermissionAnalytics(managerId, companyId);
      return res.json(analytics);
    } catch (error) {
      console.error('Error fetching permission analytics:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get available roles for assignment - allow any user for demo purposes
  app.get('/api/manager-tools/available-roles', async (req: AuthRequest, res: Response) => {
    try {
      // Return demo roles data
      const demoRoles = [
        {
          id: 1,
          name: 'Company Admin',
          description: 'Full company administration access',
          is_system: false,
          company_id: 13,
          permission_count: 15,
          can_assign: true
        },
        {
          id: 2,
          name: 'Loan Officer',
          description: 'Loan management and processing',
          is_system: false,
          company_id: 13,
          permission_count: 8,
          can_assign: true
        },
        {
          id: 3,
          name: 'Financial Manager',
          description: 'Financial operations and reporting',
          is_system: false,
          company_id: 13,
          permission_count: 6,
          can_assign: true
        },
        {
          id: 4,
          name: 'Employee',
          description: 'Basic access permissions',
          is_system: false,
          company_id: 13,
          permission_count: 4,
          can_assign: true
        }
      ];

      return res.json({
        roles: demoRoles,
        note: 'Demo data - authentication required for real roles'
      });
    } catch (error) {
      console.error('Error fetching available roles:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Perform bulk permission operations
  app.post('/api/manager-tools/bulk-permissions', authMiddleware, requirePermission('role_assign'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const result = bulkPermissionOperationSchema.safeParse(req.body);
      if (!result.success) {
        return res.status(400).json({
          message: 'Invalid input',
          errors: result.error.errors
        });
      }

      const managerId = req.user.id;
      const operation = result.data;

      // Validate that at least one operation type is specified
      if (!operation.role_ids?.length && !operation.permission_codes?.length) {
        return res.status(400).json({
          message: 'Either role_ids or permission_codes must be provided'
        });
      }

      const operationResult = await managerToolsService.performBulkPermissionOperation(managerId, operation);

      return res.json({
        message: `Bulk operation completed: ${operationResult.success} successful, ${operationResult.failed} failed`,
        ...operationResult
      });
    } catch (error) {
      console.error('Error performing bulk permission operation:', error);
      return res.status(500).json({
        message: error instanceof Error ? error.message : 'Server error'
      });
    }
  });

  // Check if user is in manager's hierarchy
  app.get('/api/manager-tools/check-hierarchy/:userId', authMiddleware, requirePermission('user_view'), async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const managerId = req.user.id;
      const userId = parseInt(req.params.userId);

      if (isNaN(userId)) {
        return res.status(400).json({ message: 'Invalid user ID' });
      }

      const isInHierarchy = await managerToolsService.isUserInManagerHierarchy(managerId, userId);
      return res.json({ is_in_hierarchy: isInHierarchy });
    } catch (error) {
      console.error('Error checking user hierarchy:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get manager's own permissions
  app.get('/api/manager-tools/my-permissions', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const managerId = req.user.id;
      const permissions = await managerToolsService.getManagerPermissions(managerId);
      return res.json(permissions);
    } catch (error) {
      console.error('Error fetching manager permissions:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Get manager dashboard data (combined endpoint) - allow any authenticated user
  app.get('/api/manager-tools/dashboard', async (req: AuthRequest, res: Response) => {
    try {
      // Try to get user from token, but provide fallback if authentication fails
      let managerId: number;
      let companyId: number | undefined;

      // Check if we have a valid user from auth middleware
      if (req.user) {
        managerId = req.user.id;
        companyId = req.query.company_id ? parseInt(req.query.company_id as string) : req.user.company_id;
      } else {
        // Fallback: try to extract user info from localStorage or provide demo data
        // For now, return demo data to prevent the dashboard from failing
        return res.json({
          team_members: [],
          summary: {
            total_members: 0,
            active_members: 0,
            permission_distribution: [],
            role_distribution: [],
            recent_changes: []
          },
          analytics: {
            team_size: 0,
            permission_coverage: {
              high_privilege_users: 0,
              standard_users: 0,
              limited_access_users: 0
            },
            permission_trends: [],
            compliance_metrics: {
              users_with_excessive_permissions: 0,
              users_needing_review: 0
            }
          },
          manager_id: 0,
          company_id: 0,
          message: 'Demo data - authentication required for real data'
        });
      }

      if (req.query.company_id && isNaN(companyId!)) {
        return res.status(400).json({ message: 'Invalid company ID' });
      }

      // Fetch all dashboard data in parallel
      const [teamMembers, summary, analytics] = await Promise.all([
        managerToolsService.getTeamMembers(managerId, companyId),
        managerToolsService.getTeamPermissionSummary(managerId, companyId),
        managerToolsService.getPermissionAnalytics(managerId, companyId)
      ]);

      return res.json({
        team_members: teamMembers,
        summary: summary,
        analytics: analytics,
        manager_id: managerId,
        company_id: companyId
      });
    } catch (error) {
      console.error('Error fetching manager dashboard data:', error);
      return res.status(500).json({
        message: 'Server error',
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  });
}
