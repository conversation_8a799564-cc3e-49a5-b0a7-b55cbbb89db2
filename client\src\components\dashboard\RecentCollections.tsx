import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>le,
  CardDescription,
} from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Avatar, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { Button } from "@/components/ui/button";
import { formatCurrency } from "@/lib/utils";

// Define type for collection data
interface Customer {
  id: number;
  full_name: string;
  email: string;
  phone: string;
}

interface Agent {
  id: number;
  user: {
    id: number;
    full_name: string;
  };
}

export interface Collection {
  id: number;
  amount: number;
  status: string;
  created_at: string;
  customer?: Customer;
  agent?: Agent;
  collection_date: string | null;
  scheduled_date: string;
  company_collection_string?: string | null;
}

interface RecentCollectionsProps {
  collections: Collection[];
  isLoading: boolean;
  onViewAll: () => void;
}

export default function RecentCollections({
  collections,
  isLoading,
  onViewAll,
}: RecentCollectionsProps) {
  // Function to get initials from name
  const getInitials = (name: string) => {
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase()
      .substring(0, 2);
  };

  // Function to format date and time
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-IN", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString("en-IN", {
      hour: "numeric",
      minute: "2-digit",
      hour12: true,
    });
  };

  // Helper function to capitalize status
  const capitalizeStatus = (status?: string) => {
    if (!status) return '';
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  // Function to get badge variant based on status
  const getStatusBadge = (status: string): "destructive" | "secondary" | "default" | "outline" => {
    switch (status.toLowerCase()) {
      case "completed":
        return "secondary"; // Using secondary for success
      case "pending":
        return "outline"; // Using outline for warning
      case "overdue":
        return "destructive";
      case "cancelled":
        return "secondary";
      default:
        return "default";
    }
  };

  const renderSkeletonRows = () => {
    return Array(5)
      .fill(0)
      .map((_, index) => (
        <TableRow key={`skeleton-${index}`}>
          <TableCell>
            <div className="flex items-center">
              <Skeleton className="h-10 w-10 rounded-full" />
              <div className="ml-4">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-3 w-16 mt-1" />
              </div>
            </div>
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-16" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-4 w-20" />
            <Skeleton className="h-3 w-12 mt-1" />
          </TableCell>
          <TableCell>
            <Skeleton className="h-5 w-16 rounded-full" />
          </TableCell>
        </TableRow>
      ));
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between border-b border-border">
        <div>
          <CardTitle>Recent Collections</CardTitle>
          <CardDescription>Latest 5 collection transactions</CardDescription>
        </div>
        <Button
          variant="link"
          onClick={onViewAll}
          className="text-primary font-medium"
        >
          View All
        </Button>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Customer</TableHead>
                <TableHead>Amount</TableHead>
                <TableHead>Date</TableHead>
                <TableHead>Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading
                ? renderSkeletonRows()
                : collections.map((collection) => (
                    <TableRow key={collection.id}>
                      <TableCell>
                        <div className="flex items-center">
                          <Avatar className="h-10 w-10">
                            <AvatarFallback className="bg-gray-200 text-gray-500">
                              {collection.customer?.full_name ? getInitials(collection.customer.full_name) : 'NA'}
                            </AvatarFallback>
                          </Avatar>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">
                              {collection.customer?.full_name || 'Unknown Customer'}
                            </div>
                            <div className="text-sm text-gray-500">
                              {collection.customer?.id ? `CUST-${collection.customer.id}` : `COLL-${collection.id}`}
                            </div>
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm font-medium text-gray-900 font-mono">
                          {formatCurrency(collection.amount, 'INR', 'en-IN')}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="text-sm text-gray-900">
                          {formatDate(collection.collection_date || collection.scheduled_date)}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatTime(collection.collection_date || collection.scheduled_date)}
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant={getStatusBadge(collection.status)}>
                          {capitalizeStatus(collection.status)}
                        </Badge>
                      </TableCell>
                    </TableRow>
                  ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
}
