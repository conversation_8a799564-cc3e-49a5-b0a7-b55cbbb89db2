import { z } from 'zod';
import { BulkUserImportData } from '@shared/schema';

export interface CsvParseResult<T> {
  data: T[];
  errors: Array<{
    row: number;
    field?: string;
    message: string;
    rawData: any;
  }>;
  totalRows: number;
  validRows: number;
}

export interface CsvColumn {
  key: string;
  header: string;
  required?: boolean;
  validator?: (value: string) => boolean;
  transformer?: (value: string) => any;
}

/**
 * Parse CSV content into structured data
 */
export function parseCsv(csvContent: string): string[][] {
  const lines = csvContent.trim().split('\n');
  const result: string[][] = [];
  
  for (const line of lines) {
    if (line.trim() === '') continue;
    
    const row: string[] = [];
    let current = '';
    let inQuotes = false;
    
    for (let i = 0; i < line.length; i++) {
      const char = line[i];
      
      if (char === '"') {
        if (inQuotes && line[i + 1] === '"') {
          // Escaped quote
          current += '"';
          i++; // Skip next quote
        } else {
          // Toggle quote state
          inQuotes = !inQuotes;
        }
      } else if (char === ',' && !inQuotes) {
        // End of field
        row.push(current.trim());
        current = '';
      } else {
        current += char;
      }
    }
    
    // Add the last field
    row.push(current.trim());
    result.push(row);
  }
  
  return result;
}

/**
 * Convert CSV rows to objects based on column mapping
 */
export function csvToObjects<T>(
  rows: string[][],
  columns: CsvColumn[]
): CsvParseResult<T> {
  if (rows.length === 0) {
    return {
      data: [],
      errors: [],
      totalRows: 0,
      validRows: 0
    };
  }
  
  const headers = rows[0];
  const dataRows = rows.slice(1);
  const result: T[] = [];
  const errors: CsvParseResult<T>['errors'] = [];
  
  // Create header mapping
  const headerMap = new Map<string, number>();
  headers.forEach((header, index) => {
    headerMap.set(header.toLowerCase().trim(), index);
  });
  
  // Process each data row
  dataRows.forEach((row, rowIndex) => {
    const obj: any = {};
    let hasErrors = false;
    
    columns.forEach(column => {
      const headerIndex = headerMap.get(column.header.toLowerCase());
      
      if (headerIndex === undefined) {
        if (column.required) {
          errors.push({
            row: rowIndex + 2, // +2 because we skip header and use 1-based indexing
            field: column.key,
            message: `Required column '${column.header}' not found`,
            rawData: row
          });
          hasErrors = true;
        }
        return;
      }
      
      const value = row[headerIndex]?.trim() || '';
      
      // Check if required field is empty
      if (column.required && !value) {
        errors.push({
          row: rowIndex + 2,
          field: column.key,
          message: `Required field '${column.header}' is empty`,
          rawData: row
        });
        hasErrors = true;
        return;
      }
      
      // Validate field if validator provided
      if (value && column.validator && !column.validator(value)) {
        errors.push({
          row: rowIndex + 2,
          field: column.key,
          message: `Invalid value for '${column.header}': ${value}`,
          rawData: row
        });
        hasErrors = true;
        return;
      }
      
      // Transform value if transformer provided
      if (column.transformer) {
        try {
          obj[column.key] = column.transformer(value);
        } catch (error) {
          errors.push({
            row: rowIndex + 2,
            field: column.key,
            message: `Transformation error for '${column.header}': ${error}`,
            rawData: row
          });
          hasErrors = true;
        }
      } else {
        obj[column.key] = value || undefined;
      }
    });
    
    if (!hasErrors) {
      result.push(obj as T);
    }
  });
  
  return {
    data: result,
    errors,
    totalRows: dataRows.length,
    validRows: result.length
  };
}

/**
 * User import CSV column definitions
 */
export const userImportColumns: CsvColumn[] = [
  {
    key: 'full_name',
    header: 'Full Name',
    required: true,
    validator: (value) => value.length >= 2
  },
  {
    key: 'username',
    header: 'Username',
    required: true,
    validator: (value) => value.length >= 3 && /^[a-zA-Z0-9_]+$/.test(value)
  },
  {
    key: 'email',
    header: 'Email',
    required: true,
    validator: (value) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)
  },
  {
    key: 'password',
    header: 'Password',
    required: false,
    validator: (value) => !value || value.length >= 8
  },
  {
    key: 'role',
    header: 'Role',
    required: true,
    validator: (value) => ['saas_admin', 'reseller', 'company_admin', 'employee', 'agent', 'customer', 'partner'].includes(value)
  },
  {
    key: 'phone',
    header: 'Phone',
    required: false,
    validator: (value) => !value || /^\+91\d{10}$/.test(value)
  },
  {
    key: 'branch_id',
    header: 'Branch ID',
    required: false,
    transformer: (value) => value ? parseInt(value) : undefined
  },
  {
    key: 'department_id',
    header: 'Department ID',
    required: false,
    transformer: (value) => value ? parseInt(value) : undefined
  },
  {
    key: 'manager_id',
    header: 'Manager ID',
    required: false,
    transformer: (value) => value ? parseInt(value) : undefined
  },
  {
    key: 'template_id',
    header: 'Template ID',
    required: false,
    transformer: (value) => value ? parseInt(value) : undefined
  }
];

/**
 * Parse user import CSV
 */
export function parseUserImportCsv(csvContent: string): CsvParseResult<BulkUserImportData> {
  const rows = parseCsv(csvContent);
  return csvToObjects<BulkUserImportData>(rows, userImportColumns);
}

/**
 * Generate CSV content from data
 */
export function generateCsv<T extends Record<string, any>>(
  data: T[],
  columns: { key: keyof T; header: string }[]
): string {
  if (data.length === 0) {
    return columns.map(col => `"${col.header}"`).join(',');
  }
  
  // Create header row
  const headers = columns.map(col => `"${col.header}"`).join(',');
  
  // Create data rows
  const rows = data.map(item => {
    return columns.map(col => {
      const value = item[col.key];
      const stringValue = value !== undefined && value !== null ? String(value) : '';
      // Escape quotes and wrap in quotes if contains comma or quote
      if (stringValue.includes(',') || stringValue.includes('"') || stringValue.includes('\n')) {
        return `"${stringValue.replace(/"/g, '""')}"`;
      }
      return stringValue;
    }).join(',');
  });
  
  return [headers, ...rows].join('\n');
}

/**
 * User export CSV column definitions
 */
export const userExportColumns = [
  { key: 'id' as const, header: 'User ID' },
  { key: 'full_name' as const, header: 'Full Name' },
  { key: 'username' as const, header: 'Username' },
  { key: 'email' as const, header: 'Email' },
  { key: 'role' as const, header: 'Role' },
  { key: 'phone' as const, header: 'Phone' },
  { key: 'branch_id' as const, header: 'Branch ID' },
  { key: 'department_id' as const, header: 'Department ID' },
  { key: 'manager_id' as const, header: 'Manager ID' },
  { key: 'created_at' as const, header: 'Created At' },
  { key: 'updated_at' as const, header: 'Updated At' }
];
