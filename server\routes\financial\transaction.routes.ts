import { Express, Response } from 'express';
import { storage } from '../../storage';
import { authMiddleware, requireCompanyAccess, AuthRequest } from '../../middleware/auth';
import { insertTransactionSchema } from '@shared/schema';
import { ZodError } from 'zod';

// Format Zod error for consistent API response
function formatZodError(error: ZodError) {
  return error.errors.map(err => ({
    path: err.path.join('.'),
    message: err.message
  }));
}

// Helper to ensure user and company IDs are available
function ensureUserAuth(req: AuthRequest): { userId: number, companyId: number } {
  if (!req.user) {
    throw new Error('Authentication required');
  }

  if (req.user.company_id === null || req.user.company_id === undefined) {
    throw new Error('Company context required');
  }

  return {
    userId: req.user.id,
    companyId: req.user.company_id
  };
}

export function registerTransactionRoutes(app: Express): void {
  // Note: Main transaction routes are defined in routes.ts to avoid duplication
  // This file contains only specialized transaction routes

  // Note: Account-specific transaction routes are handled in routes.ts
  // Note: Individual transaction routes are handled in routes.ts

  // Transaction summary route (specialized route not in main routes.ts)
  app.get('/api/companies/:companyId/transactions/summary', authMiddleware, requireCompanyAccess, async (req: AuthRequest, res: Response) => {
    try {
      const companyId = parseInt(req.params.companyId);

      // Parse query parameters
      const startDate = req.query.start_date as string;
      const endDate = req.query.end_date as string;

      if (!startDate || !endDate) {
        return res.status(400).json({ message: 'Start date and end date are required' });
      }

      const summary = await storage.getTransactionSummary(companyId, { startDate, endDate });
      return res.json(summary);
    } catch (error) {
      console.error(`Error fetching transaction summary for company ${req.params.companyId}:`, error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Note: All other transaction routes (CRUD operations) are handled in routes.ts
  // to maintain consistency with the loan workflow pattern
}
