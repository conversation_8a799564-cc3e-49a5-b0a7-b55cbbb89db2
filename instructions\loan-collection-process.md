# Loan and Collection Process Documentation

## Overview

This document explains the end-to-end process of loan creation, payment schedule generation, collection record creation, and accounting entries in the FinancialTracker application.

## Loan Creation Process

When a loan is created through the `/api/loans` endpoint, the following sequence of operations occurs:

### 1. Loan Data Validation

- The API receives loan data in the request body
- The system validates the input against the `insertLoanSchema` schema
- The system verifies that the user has access to the specified company
- The system checks if the customer exists and belongs to the specified company

### 2. Loan Record Creation

- The validated loan data is passed to the storage layer
- A new loan record is created in the `loans` table with the following key fields:
  - `company_id`: The company the loan belongs to
  - `customer_id`: The customer receiving the loan
  - `amount`: The principal loan amount
  - `interest_rate`: The interest rate percentage
  - `interest_type`: The type of interest (flat, reducing, etc.)
  - `loan_type`: The type of loan (personal, business, etc.)
  - `term`: The number of payment periods
  - `terms_frequency`: The frequency of terms (monthly, weekly, etc.)
  - `start_date`: When the loan begins
  - `end_date`: When the loan is scheduled to end
  - `status`: The loan status (active, pending, completed, etc.)
  - `payment_frequency`: How often payments are made
  - Additional fields for notes, disbursement details, etc.

### 3. Payment Schedule Generation

After the loan is created, the system automatically generates payment schedules:

- The system calls `generatePaymentSchedules()` for the newly created loan
- For each payment period (based on the loan term):
  - A due date is calculated based on the payment frequency
  - The payment amount is calculated based on the loan amount, term, and interest type
  - For flat interest loans, the payment amount is (principal + interest) / term
  - For reducing balance loans, an amortization calculation is performed
- Payment schedule records are created in the `payment_schedules` table with:
  - `loan_id`: Reference to the loan
  - `company_id`: The company ID
  - `customer_id`: The customer ID
  - `payment_number`: The sequential payment number (1, 2, 3...)
  - `amount`: The payment amount
  - `due_date`: When the payment is due
  - `status`: Initially set to 'pending'
  - Additional fields for tracking

### 4. Collection Record Creation

For each payment schedule, the system automatically creates collection records:

- The system generates a company-specific collection ID with the format:
  - For multi-word company names: First letter of first word + first letter of second word + hyphen + sequential number (e.g., "CS-001")
  - For single-word company names: First letter + last letter + hyphen + sequential number (e.g., "GI-001")
- The system queries the database for the highest existing serial number for the company prefix
- Collection records are created in the `collections` table with:
  - `company_id`: The company ID
  - `loan_id`: Reference to the loan
  - `customer_id`: The customer ID
  - `amount`: The collection amount (same as payment schedule amount)
  - `scheduled_date`: When the collection is scheduled (same as payment schedule due date)
  - `status`: Initially set to 'pending'
  - `notes`: Auto-generated note indicating the payment number
  - `emi_number`: The payment number
  - `company_collection_string`: The company-specific collection ID (e.g., "CS-001")
  - Additional fields for tracking

### 5. Linking Payment Schedules to Collections

- Each payment schedule is linked to its corresponding collection record
- The `collection_id` field in the payment schedule is updated with the ID of the collection record
 
### 6. Accounting Journal Entry Creation

The system creates accounting entries for the loan disbursement:

- The system looks for the required accounts:
  - Cash/Bank account (code '1000')
  - Loan Receivable account (code '1200')
- A journal entry is created with:
  - Credit to Cash/Bank account (asset decreases)
  - Debit to Loan Receivable account (asset increases)
  - The entry amount is the loan amount
  - Reference type is 'loan'
  - Reference ID is the loan ID
  - Description includes the loan ID
- The journal entry is recorded in the `transactions` table with:
  - Two transaction records (one debit, one credit)
  - Each transaction includes company ID, account ID, amount, transaction type, etc.

## Collection Processing

When a collection is processed (marked as completed):

### 1. Collection Status Update

- The collection status is updated to 'completed'
- The collection date is recorded

### 2. Payment Record Creation

- A payment record is created in the `payments` table
- A unique receipt number is generated

### 3. Accounting Journal Entry Creation

- A journal entry is created with:
  - Debit to Cash/Bank account (asset increases)
  - Credit to Loan Receivable account (asset decreases) for the principal component
  - Credit to Interest Income account (income increases) for any interest/fine component
  - Reference type is 'collection'
  - Reference ID is the collection ID

## Technical Implementation Details

### Loan Amount Calculation

For flat interest loans:
- Total interest = Principal × Interest Rate × (Term / 12 for monthly terms)
- Total repayable = Principal + Total interest
- Installment amount = Total repayable / Term

For reducing balance loans:
- Uses standard amortization formula
- Each payment includes both principal and interest components
- Interest component decreases over time as principal is paid down

### Collection ID Generation

The collection ID generation process:
1. Get the company name from the database
2. Extract the appropriate prefix based on company name format
3. Query for the highest existing serial number for this company and prefix
4. Increment the serial number for each new collection
5. Format as "[PREFIX]-[SERIAL]" where SERIAL is a 3-digit zero-padded number

### Journal Entry Creation

The journal entry creation process enforces double-entry accounting principles:
1. Validate all required fields (company ID, description, reference type, etc.)
2. Extract debit and credit entries from the entries object
3. Verify that total debits equal total credits
4. Create transaction records for each debit and credit entry
5. Return the created transaction records

## Database Tables Involved

- `loans`: Stores loan information
- `payment_schedules`: Stores payment schedule information
- `collections`: Stores collection information
- `payments`: Stores payment information
- `transactions`: Stores accounting transactions
- `accounts`: Stores chart of accounts
- `companies`: Stores company information
- `customers`: Stores customer information

## API Response

After successful loan creation, the API returns:
- The created loan object with all fields
- HTTP status code 201 (Created)
