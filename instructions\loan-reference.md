# Loan Reference Code Implementation

This document provides step-by-step instructions for implementing company-specific loan reference codes in the FinancialTracker system, based on our actual implementation process.

## Overview

Loan reference codes are unique identifiers for loans within a company, following a similar pattern to collection IDs and agent reference codes. The format is:

```
[COMPANY_PREFIX]-[SEQUENTIAL_NUMBER]
```

For example:
- "GS-001" for the first loan in "GOVINDARAJI S" company
- "CS-001" for the first loan in "Cloud Stier" company

Our implementation followed these steps:
1. Adding a new column to the database through a migration
2. Setting a default empty value for existing records
3. Referencing the agent-reference.md file for the company prefix generation logic
4. Implementing the sequential number generation
5. Updating the loan creation and update processes to use the company-specific reference codes

## Implementation Details

### 1. Database Schema Changes

#### Step 1: Create SQL Migration
We created a migration file `migrations/006_add_loan_reference_code.sql` with the following SQL:

```sql
-- Migration to add loan_reference_code column to loans table
-- This column will store company-specific loan identifiers

-- Add loan_reference_code column to loans table
ALTER TABLE "loans"
  ADD COLUMN IF NOT EXISTS "loan_reference_code" TEXT;

-- Create an index for faster lookups by loan_reference_code
CREATE INDEX IF NOT EXISTS idx_loans_reference_code ON loans(loan_reference_code);

-- Comment on the column to document its purpose
COMMENT ON COLUMN loans.loan_reference_code IS 'Company-specific loan identifier string (e.g., GS-001) that is unique within each company';
```

#### Step 2: Create Migration Runner Script
We created a script `run-loan-reference-migration.js` to run the migration:

```javascript
// Script to run the loan reference code migration
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function runMigration() {
  console.log('Starting loan reference code migration...');

  try {
    // Read the migration SQL file
    const migrationPath = path.join(__dirname, 'migrations', '006_add_loan_reference_code.sql');
    console.log(`Reading migration file from: ${migrationPath}`);

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Execute the migration
    console.log('Executing migration...');
    await pool.query(migrationSQL);

    // Verify the migration
    const columnCheck = await pool.query(`
      SELECT column_name
      FROM information_schema.columns
      WHERE table_name = 'loans' AND column_name = 'loan_reference_code'
    `);

    if (columnCheck.rows.length > 0) {
      console.log('✅ Migration verified successfully!');
      console.log('✅ loan_reference_code column added to loans table');
    } else {
      console.log('❌ Migration verification failed: loan_reference_code column not found');
    }

    console.log('Migration completed successfully!');
  } catch (error) {
    console.error('Migration failed:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the migration
runMigration();
```

#### Step 3: Update Schema Definition
We updated the loans table definition in `shared/schema.ts`:

```typescript
// Loans
export const loans = pgTable('loans', {
  id: serial('id').primaryKey(),
  company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
  customer_id: integer('customer_id').references(() => customers.id, { onDelete: 'cascade' }).notNull(),
  // ... other fields ...
  loan_reference_code: text('loan_reference_code'),
  notes: text('notes'),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});
```

#### Step 4: Update Migration Runner
We updated the `run-neon-migrations.js` file to include our new migration:

```javascript
// Define migrations to run in order
const migrations = [
  // ... existing migrations ...
  {
    name: '006_add_loan_reference_code',
    path: path.join(__dirname, 'migrations', '006_add_loan_reference_code.sql'),
    verification: async (pool) => {
      const result = await pool.query(`
        SELECT column_name
        FROM information_schema.columns
        WHERE table_name = 'loans' AND column_name = 'loan_reference_code'
      `);
      return result.rows.length > 0;
    }
  }
];
```

### 2. Setting Default Value for Existing Records

After adding the column, we needed to set a default empty value for all existing loan records. We created a script to update all existing loans:

#### Step 5: Create Script to Update Existing Records
We created a script `update-loan-reference-codes.js` to update all existing loans with an empty value:

```javascript
// Script to update all existing loans to have an empty value in the loan_reference_code column
import { Pool, neonConfig } from '@neondatabase/serverless';
import dotenv from 'dotenv';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';
import ws from 'ws';

// Configure Neon to use WebSockets
neonConfig.webSocketConstructor = ws;

// Get the current directory in ESM context
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Load environment variables
const envPath = path.resolve('.env');
console.log('Checking for .env file at:', envPath);
console.log('File exists:', fs.existsSync(envPath));

if (fs.existsSync(envPath)) {
  const envConfig = dotenv.parse(fs.readFileSync(envPath));
  for (const k in envConfig) {
    process.env[k] = envConfig[k];
  }
  console.log('Loaded DATABASE_URL:', process.env.DATABASE_URL ? 'Yes (value hidden)' : 'No');
}

// Create a connection pool
const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  max: 5
});

async function updateLoanReferenceCodes() {
  console.log('Starting update of loan_reference_code values...');

  try {
    // First, check if there are any loans in the database
    const countResult = await pool.query(`
      SELECT COUNT(*) FROM loans
    `);

    const loanCount = parseInt(countResult.rows[0].count);
    console.log(`Found ${loanCount} loans in the database`);

    if (loanCount === 0) {
      console.log('No loans to update. Exiting.');
      return;
    }

    // Update all loans to have an empty value in the loan_reference_code column
    const updateResult = await pool.query(`
      UPDATE loans
      SET loan_reference_code = ''
      WHERE loan_reference_code IS NULL
      RETURNING id
    `);

    const updatedCount = updateResult.rows.length;
    console.log(`Updated ${updatedCount} loans with empty loan_reference_code`);

    // Verify the update
    const verifyResult = await pool.query(`
      SELECT COUNT(*) FROM loans WHERE loan_reference_code = ''
    `);

    const verifiedCount = parseInt(verifyResult.rows[0].count);
    console.log(`Verified ${verifiedCount} loans now have empty loan_reference_code`);

    console.log('Update completed successfully!');
  } catch (error) {
    console.error('Error updating loan reference codes:', error);
    console.error(error.stack);
  } finally {
    // Close the database connection
    await pool.end();
    process.exit(0);
  }
}

// Run the update
updateLoanReferenceCodes();
```

#### Step 6: Add API Endpoint for Updating Reference Codes
We added an API endpoint to update loan reference codes programmatically:

```javascript
// Update all loan reference codes to company-specific format
app.post('/api/loans/update-reference-codes', authMiddleware, requireRole(['saas_admin', 'company_admin']), async (req: AuthRequest, res: Response) => {
  try {
    // Ensure user is authenticated
    if (!req.user) {
      return res.status(401).json({ message: 'Authentication required' });
    }

    // Get company ID from request or user context
    const companyId = req.body.company_id || req.user.company_id;

    if (!companyId) {
      return res.status(400).json({ message: 'Company ID is required' });
    }

    // Ensure user has access to this company
    if (req.user.role !== 'saas_admin' && req.user.company_id !== companyId) {
      const userCompanies = await storage.getUserCompanies(req.user.id);
      const hasAccess = userCompanies.some(uc =>
        uc.company_id === companyId ||
        (uc.company && uc.company.id === companyId)
      );

      if (!hasAccess) {
        console.log(`Access denied: User ${req.user.id} has no association with company ${companyId}`);
        return res.status(403).json({ message: 'Access denied to this company' });
      }
    }

    // Get all loans for the company
    const companyLoans = await storage.getLoansByCompany(companyId);
    console.log(`Found ${companyLoans.length} loans for company ${companyId}`);

    if (companyLoans.length === 0) {
      return res.status(200).json({
        message: 'No loans found for this company',
        updated: 0
      });
    }

    // Update each loan with a company-specific reference code
    let updatedCount = 0;
    const errors = [];

    for (const loan of companyLoans) {
      try {
        // Only update loans that don't already have a reference code or have an empty reference code
        if (!loan.loan_reference_code || loan.loan_reference_code.trim() === '') {
          await storage.updateLoan(
            loan.id,
            companyId,
            { loan_reference_code: '' }
          );

          updatedCount++;
        }
      } catch (error) {
        console.error(`Error updating loan ID ${loan.id}:`, error);
        errors.push({
          loanId: loan.id,
          error: error instanceof Error ? error.message : String(error)
        });
      }
    }

    return res.status(200).json({
      message: `Updated ${updatedCount} loans with company-specific reference codes`,
      totalLoans: companyLoans.length,
      updatedLoans: updatedCount,
      errors: errors.length > 0 ? errors : undefined
    });
  } catch (error) {
    console.error('Error updating loan reference codes:', error);
    return res.status(500).json({
      message: 'Server error',
      error: error instanceof Error ? error.message : String(error)
    });
  }
});
```

### 3. Company Prefix Generation

After setting the default value, we implemented the company-specific prefix generation. We referenced the existing `getCompanyName` function from the agent-reference.md file, which was already implemented in `server/routes.ts`:

```typescript
// Helper to get company name from company_id
async function getCompanyName(companyId: number): Promise<string> {
  try {
    // Query the companies table to get the company name
    const [company] = await db.select({ name: companies.name })
      .from(companies)
      .where(eq(companies.id, companyId));

    // Get the company name or use a default
    const fullName = company?.name || `Company_${companyId}`;
    console.log(`Generating prefix for company name: "${fullName}"`);

    // Split the name into words
    const words = fullName.split(' ').filter(word => word.length > 0);

    let prefix = '';
    if (words.length === 0) {
      prefix = `C${companyId}`;
    } else if (words.length === 1) {
      // If only one word, use first and last letter of that word
      const word = words[0];
      prefix = word.length > 1
        ? (word.charAt(0) + word.charAt(word.length - 1)).toUpperCase()
        : word.toUpperCase() + companyId;
    } else {
      // If multiple words, use first letter of first word and first letter of last word
      // This handles cases like "GOVINDARAJI S" correctly
      prefix = (words[0].charAt(0) + words[words.length - 1].charAt(0)).toUpperCase();
    }

    console.log(`Generated prefix: "${prefix}" for company name: "${fullName}"`);
    return prefix;
  } catch (error) {
    console.error(`Error fetching company name for ID ${companyId}:`, error);
    // Return a fallback value in case of error
    return `C${companyId}`;
  }
}
```

### 4. Sequential Number Generation

#### Step 7: Implement getHighestLoanSerial Method
We added the `getHighestLoanSerial` method to the `LoanStorage` class in `server/storage/loan.storage.ts`:

```typescript
/**
 * Get the highest serial number for a given company and prefix (e.g., 'GS-')
 * Returns the highest serial as a number, or 0 if none found.
 * Only considers loans from the specific company.
 */
async getHighestLoanSerial(companyId: number, prefix: string): Promise<number> {
  try {
    // Find the max serial for this company and prefix
    // loan_reference_code is like 'GS-001', 'GS-002', ...
    const result = await db.select({ maxString: sql`MAX(${loans.loan_reference_code})` })
      .from(loans)
      .where(
        and(
          eq(loans.company_id, companyId),
          like(loans.loan_reference_code, `${prefix}%`)
        )
      );
    const maxString = result[0]?.maxString as string | undefined;
    if (!maxString) return 0;

    // Extract the serial part (e.g., 'GS-012' => 12)
    const match = maxString.match(/^(.*-)(\d{3})$/);
    if (match) {
      return parseInt(match[2], 10);
    }
    return 0;
  } catch (error) {
    errorLogger.logError(`Error getting highest loan serial for company ${companyId} and prefix ${prefix}`, 'loan-serial', error as Error);
    return 0;
  }
}
```

We also added the necessary imports at the top of the file:

```typescript
import { db } from '../db';
import { eq, and, like, sql } from 'drizzle-orm';
import { loans, collections, transactions } from '@shared/schema';
import { Loan, InsertLoan } from '@shared/schema';
import errorLogger from '../utils/errorLogger';
import { ILoanStorage } from './interfaces';
```

### 5. Loan Creation Process

#### Step 8: Update Loan Creation Endpoint
We updated the loan creation endpoint in `server/routes.ts` to generate and use company-specific prefixes:

```typescript
// Generate company-specific loan reference code
const companyId = result.data.company_id;

// Get company name for loan reference code prefix
const companyPrefix = await getCompanyName(companyId);
console.log(`Generated company prefix for loan reference code: ${companyPrefix}`);

// Get the highest existing loan reference code for this company
const loanStorage = new LoanStorage();
const highestSerial = await loanStorage.getHighestLoanSerial(companyId, companyPrefix);
const nextSerial = highestSerial + 1;
const serialString = nextSerial.toString().padStart(3, '0');
const loanReferenceCode = `${companyPrefix}-${serialString}`;

console.log(`Generated loan reference code: ${loanReferenceCode} for company ${companyId}`);

// Add the reference code to the loan data
const loanDataWithReferenceCode = {
  ...result.data,
  loan_reference_code: loanReferenceCode
};

console.log(`Creating loan with reference code "${loanReferenceCode}"`);
const loan = await storage.createLoan(loanDataWithReferenceCode);
console.log(`Loan created with ID: ${loan.id} for company ${loan.company_id} and reference code: ${loan.loan_reference_code}`);
```

#### Step 9: Add Import for LoanStorage
We added the import for the LoanStorage class at the top of the routes.ts file:

```typescript
import { LoanStorage } from "./storage/loan.storage";
```

#### Step 10: Update Loan Update Endpoint
We also updated the loan update endpoint to maintain the reference code format:

```typescript
// If loan_reference_code is not provided in the update, check if the existing loan has one
if (!updateData.loan_reference_code) {
  // Check if the existing loan has a reference code
  if (!existingLoan.loan_reference_code || existingLoan.loan_reference_code.trim() === '') {
    // If not, generate a company-specific reference code
    const companyPrefix = await getCompanyName(companyId);
    const loanStorage = new LoanStorage();
    const highestSerial = await loanStorage.getHighestLoanSerial(companyId, companyPrefix);
    const nextSerial = highestSerial + 1;
    const serialString = nextSerial.toString().padStart(3, '0');
    const loanReferenceCode = `${companyPrefix}-${serialString}`;

    updateData.loan_reference_code = loanReferenceCode;
    console.log(`Setting loan_reference_code to "${loanReferenceCode}" for loan without a reference code`);
  }
}
```

### 6. Update Reference Codes Endpoint

#### Step 11: Update the Reference Codes Endpoint
We updated the update-reference-codes endpoint to use company-specific prefixes:

```typescript
// Get company prefix for loan reference codes
const companyPrefix = await getCompanyName(companyId);
console.log(`Generated company prefix for loan reference codes: ${companyPrefix}`);

// Create a LoanStorage instance to use getHighestLoanSerial
const loanStorage = new LoanStorage();

// Update each loan with a company-specific reference code
let updatedCount = 0;
const errors = [];
let highestSerial = await loanStorage.getHighestLoanSerial(companyId, companyPrefix);

for (const loan of companyLoans) {
  try {
    // Only update loans that don't already have a reference code or have an empty reference code
    if (!loan.loan_reference_code || loan.loan_reference_code.trim() === '') {
      // Generate the next sequential reference code
      highestSerial++;
      const serialString = highestSerial.toString().padStart(3, '0');
      const loanReferenceCode = `${companyPrefix}-${serialString}`;

      console.log(`Generating reference code ${loanReferenceCode} for loan ID ${loan.id}`);

      await storage.updateLoan(
        loan.id,
        companyId,
        { loan_reference_code: loanReferenceCode }
      );

      updatedCount++;
    }
  } catch (error) {
    console.error(`Error updating loan ID ${loan.id}:`, error);
    errors.push({
      loanId: loan.id,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

return res.status(200).json({
  message: `Updated ${updatedCount} loans with company-specific reference codes`,
  totalLoans: companyLoans.length,
  updatedLoans: updatedCount,
  companyPrefix: companyPrefix,
  errors: errors.length > 0 ? errors : undefined
});
```

## Implementation Summary

Our implementation followed these steps:

1. **Database Schema Changes**:
   - Created a migration file to add the `loan_reference_code` TEXT column to the loans table
   - Created a script to run the migration
   - Updated the schema definition in `shared/schema.ts`
   - Updated the migration runner to include our new migration

2. **Setting Default Value for Existing Records**:
   - Created a script to update all existing loans with an empty value as the default
   - Added an API endpoint to update loan reference codes programmatically

3. **Company Prefix Generation**:
   - Reused the existing `getCompanyName` function from the agent-reference.md implementation
   - This function generates prefixes like "GS" for "GOVINDARAJI S" company

4. **Sequential Number Generation**:
   - Added the `getHighestLoanSerial` method to the `LoanStorage` class
   - This method finds the highest existing serial number for a company and prefix

5. **Loan Creation and Update Process**:
   - Updated the loan creation endpoint to generate and use company-specific prefixes
   - Updated the loan update endpoint to maintain the reference code format
   - Updated the update-reference-codes endpoint to use company-specific prefixes

## Testing

To test the implementation:

1. **Create a new loan**:
   - It should automatically get a company-specific reference code (e.g., "GS-001")
   - Creating another loan for the same company should increment the serial (e.g., "GS-002")
   - Creating a loan for a different company should start with 001 (e.g., "CS-001")

2. **Update an existing loan**:
   - If it doesn't have a reference code, it should get a company-specific one
   - If it already has a reference code, it should keep it

3. **Use the update-reference-codes endpoint**:
   - It should update all loans without reference codes for a specific company
   - The reference codes should follow the company-specific format and sequential numbering

## Troubleshooting

If you encounter issues:

1. **Database migration fails**:
   - Check the database connection settings in the .env file
   - Verify the SQL syntax in the migration file
   - Try running the SQL directly against the database

2. **Reference codes not generating correctly**:
   - Check the console logs for the generated prefix and reference code
   - Verify the company name is being retrieved correctly
   - Ensure the highest serial number is being calculated properly

3. **Duplicate reference codes**:
   - Check if the `getHighestLoanSerial` method is working correctly
   - Verify that the company ID filter is being applied correctly
   - Check for race conditions when creating multiple loans simultaneously
