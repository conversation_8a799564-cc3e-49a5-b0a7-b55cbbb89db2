# TrackFina Reporting System - Implementation Plan

## Overview

This document outlines the implementation plan for a comprehensive reporting system in the TrackFina application. The reporting system will provide detailed financial and operational insights with the ability to export reports in PDF and CSV formats.

## Current System Analysis

### Existing Components
- **PDF Generation**: The application has PDF generation functionality in `server/utils/pdfGenerator.ts` for payment receipts.
- **Reports Page**: A basic reports page exists at `client/src/pages/reports/index.tsx` with placeholder data.
- **CSV Export**: Limited CSV export functionality exists in the `AmortizationSchedule` component.
- **Dashboard Metrics**: Basic dashboard metrics API exists for high-level company statistics.
- **Collection Trends**: API endpoint for collection trends with period filtering.

### Data Structure
- Collections, payments, loans, customers, and agents tables are well-defined in the schema.
- No expense tracking system currently exists, which will be needed for P&L reporting.

## Required Reports

### 1. Daily Collections Report
- Shows all collections made on a specific day or date range.
- Filters by status (pending, completed, overdue).
- Groups by agent, branch, or payment method.
- Includes total amounts collected.

### 2. Day Sheet
- Provides a summary of all financial activities for a specific day.
- Shows opening balance, collections, disbursements, and closing balance.
- Groups transactions by type (collection, loan disbursement, expense).
- Includes variance analysis (planned vs. actual collections).

### 3. Customer-wise Report
- Detailed loan and payment history for specific customers.
- Shows outstanding balances, payment histories, and upcoming payments.
- Includes loan performance metrics (on-time payment rate, average days late).
- Enables customer segmentation by payment behavior.

### 4. Agent-wise Report
- Performance metrics for collection agents.
- Shows collection targets vs. actual collections.
- Includes collection efficiency percentages.
- Trend analysis of agent performance over time.

### 5. P&L Statement
- Complete profit and loss report for the company.
- Income from interest, fees, penalties.
- Expenses (to be implemented).
- Net profit calculations with period comparison.

### 6. Shareholder Reports
- Customizable reports for shareholders showing key business metrics.
- Investment returns and company growth statistics.
- Market share and competitive analysis (if data available).

## Implementation Plan

### Phase 1: Database Schema Updates

1. **Add Expense Management Schema**
   - Create expenses table in `shared/schema.ts`:
   ```typescript
   export const expenseTypeEnum = pgEnum('expense_type', [
     'rent',
     'salary',
     'utilities',
     'office_supplies',
     'marketing',
     'transport',
     'loan_disbursement',
     'other'
   ]);

   export const expenses = pgTable('expenses', {
     id: serial('id').primaryKey(),
     company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
     branch_id: integer('branch_id').references(() => branches.id, { onDelete: 'set null' }),
     amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
     description: text('description').notNull(),
     expense_date: timestamp('expense_date').defaultNow().notNull(),
     expense_type: expenseTypeEnum('expense_type').notNull(),
     created_by: integer('created_by').references(() => users.id),
     reference_number: text('reference_number'),
     payment_method: paymentMethodEnum('payment_method').notNull(),
     notes: text('notes'),
     created_at: timestamp('created_at').defaultNow().notNull(),
     updated_at: timestamp('updated_at').defaultNow().notNull(),
   });

   export const expensesRelations = relations(expenses, ({ one }) => ({
     company: one(companies, {
       fields: [expenses.company_id],
       references: [companies.id],
     }),
     branch: one(branches, {
       fields: [expenses.branch_id],
       references: [branches.id],
     }),
     createdBy: one(users, {
       fields: [expenses.created_by],
       references: [users.id],
     }),
   }));
   ```

2. **Add Fine Management (for P&L revenue breakdown)**
   - Create fines table for tracking penalty income separately:
   ```typescript
   export const fines = pgTable('fines', {
     id: serial('id').primaryKey(),
     company_id: integer('company_id').references(() => companies.id, { onDelete: 'cascade' }).notNull(),
     loan_id: integer('loan_id').references(() => loans.id, { onDelete: 'cascade' }).notNull(),
     payment_id: integer('payment_id').references(() => payments.id, { onDelete: 'cascade' }),
     amount: numeric('amount', { precision: 10, scale: 2 }).notNull(),
     fine_date: timestamp('fine_date').defaultNow().notNull(),
     fine_type: fineTypeEnum('fine_type').notNull(),
     is_paid: boolean('is_paid').default(false).notNull(),
     created_at: timestamp('created_at').defaultNow().notNull(),
     updated_at: timestamp('updated_at').defaultNow().notNull(),
   });
   ```

3. **Add necessary schema types and Zod validation**
   - Add insert schemas and types for new tables

### Phase 2: Backend API Endpoints

1. **Daily Collections Report API**
   ```typescript
   // Add to server/routes.ts
   app.get('/api/companies/:companyId/reports/daily-collections', authMiddleware, async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const { startDate, endDate, status, agentId, branchId, paymentMethod } = req.query;
       
       // Add access control checks
       
       const dailyCollections = await storage.getDailyCollectionsReport(
         companyId,
         startDate as string,
         endDate as string,
         status as string,
         agentId ? parseInt(agentId as string, 10) : undefined,
         branchId ? parseInt(branchId as string, 10) : undefined,
         paymentMethod as string,
       );
       
       res.json(dailyCollections);
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

2. **Day Sheet API**
   ```typescript
   app.get('/api/companies/:companyId/reports/day-sheet', authMiddleware, async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const { date, branchId } = req.query;
       
       // Add access control checks
       
       const daySheet = await storage.getDaySheetReport(
         companyId,
         date as string,
         branchId ? parseInt(branchId as string, 10) : undefined
       );
       
       res.json(daySheet);
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

3. **Customer Report API**
   ```typescript
   app.get('/api/companies/:companyId/reports/customer/:customerId', authMiddleware, async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const customerId = parseInt(req.params.customerId, 10);
       const { startDate, endDate } = req.query;
       
       // Add access control checks
       
       const customerReport = await storage.getCustomerReport(
         companyId,
         customerId,
         startDate as string,
         endDate as string
       );
       
       res.json(customerReport);
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

4. **Agent Performance API**
   ```typescript
   app.get('/api/companies/:companyId/reports/agent/:agentId', authMiddleware, async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const agentId = parseInt(req.params.agentId, 10);
       const { startDate, endDate } = req.query;
       
       // Add access control checks
       
       const agentReport = await storage.getAgentReport(
         companyId,
         agentId,
         startDate as string,
         endDate as string
       );
       
       res.json(agentReport);
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

5. **P&L Statement API**
   ```typescript
   app.get('/api/companies/:companyId/reports/profit-loss', authMiddleware, requireRole(['company_admin', 'saas_admin']), async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const { startDate, endDate, branchId } = req.query;
       
       // Add access control checks
       
       const plReport = await storage.getProfitLossReport(
         companyId,
         startDate as string,
         endDate as string,
         branchId ? parseInt(branchId as string, 10) : undefined
       );
       
       res.json(plReport);
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

6. **Report Export APIs**
   ```typescript
   app.get('/api/companies/:companyId/reports/export/:reportType', authMiddleware, async (req: AuthRequest, res: Response) => {
     try {
       const companyId = parseInt(req.params.companyId, 10);
       const reportType = req.params.reportType;
       const { format, ...params } = req.query;
       
       // Add access control checks
       
       if (format === 'pdf') {
         const pdfBuffer = await reportExportService.generatePdfReport(
           companyId,
           reportType,
           params
         );
         
         res.setHeader('Content-Type', 'application/pdf');
         res.setHeader('Content-Disposition', `attachment; filename=${reportType}_${new Date().toISOString().split('T')[0]}.pdf`);
         res.send(pdfBuffer);
       } else if (format === 'csv') {
         const csvContent = await reportExportService.generateCsvReport(
           companyId,
           reportType,
           params
         );
         
         res.setHeader('Content-Type', 'text/csv');
         res.setHeader('Content-Disposition', `attachment; filename=${reportType}_${new Date().toISOString().split('T')[0]}.csv`);
         res.send(csvContent);
       } else {
         throw new Error('Unsupported export format');
       }
     } catch (error) {
       handleApiError(res, error as Error);
     }
   });
   ```

### Phase 3: Storage Implementation

1. **Update IStorage Interface**

   Add these methods to `server/storage.ts`:
   ```typescript
   interface IStorage {
     // ... existing methods

     // Report methods
     getDailyCollectionsReport(
       companyId: number,
       startDate: string,
       endDate: string,
       status?: string,
       agentId?: number,
       branchId?: number,
       paymentMethod?: string
     ): Promise<DailyCollectionReport>;
     
     getDaySheetReport(
       companyId: number,
       date: string,
       branchId?: number
     ): Promise<DaySheetReport>;
     
     getCustomerReport(
       companyId: number,
       customerId: number,
       startDate?: string,
       endDate?: string
     ): Promise<CustomerReport>;
     
     getAgentReport(
       companyId: number,
       agentId: number,
       startDate?: string,
       endDate?: string
     ): Promise<AgentReport>;
     
     getProfitLossReport(
       companyId: number,
       startDate: string,
       endDate: string,
       branchId?: number
     ): Promise<ProfitLossReport>;
     
     // Expense management
     createExpense(expense: InsertExpense): Promise<Expense>;
     getExpenses(companyId: number, filters?: ExpenseFilters): Promise<Expense[]>;
     getExpenseById(id: number): Promise<Expense | undefined>;
     updateExpense(id: number, expense: Partial<Expense>): Promise<Expense>;
     deleteExpense(id: number): Promise<void>;
   }
   ```

2. **Implement Report Query Methods**

   Add these implementations to the DatabaseStorage class:
   ```typescript
   async getDailyCollectionsReport(
     companyId: number,
     startDate: string,
     endDate: string,
     status?: string,
     agentId?: number,
     branchId?: number,
     paymentMethod?: string
   ): Promise<DailyCollectionReport> {
     let query = db
       .select({
         id: collections.id,
         loan_id: collections.loan_id,
         customer_id: collections.customer_id,
         agent_id: collections.agent_id,
         amount: collections.amount,
         scheduled_date: collections.scheduled_date,
         collection_date: collections.collection_date,
         status: collections.status,
         payment_method: collections.payment_method,
         customerName: customers.full_name,
         agentName: agents.full_name,
         receipt_id: collections.receipt_id,
       })
       .from(collections)
       .leftJoin(customers, eq(collections.customer_id, customers.id))
       .leftJoin(agents, eq(collections.agent_id, agents.id))
       .leftJoin(loans, eq(collections.loan_id, loans.id))
       .where(
         and(
           eq(collections.company_id, companyId),
           gte(collections.scheduled_date, new Date(startDate)),
           lte(collections.scheduled_date, new Date(endDate))
         )
       );
       
     if (status) {
       query = query.where(eq(collections.status, status as any));
     }
     
     if (agentId) {
       query = query.where(eq(collections.agent_id, agentId));
     }
     
     if (branchId && loans.branch_id) {
       query = query.where(eq(loans.branch_id, branchId));
     }
     
     if (paymentMethod) {
       query = query.where(eq(collections.payment_method, paymentMethod as any));
     }
     
     const collectionsData = await query.orderBy(desc(collections.scheduled_date));
     
     // Group by date and calculate totals
     const groupedByDate = collectionsData.reduce((acc, curr) => {
       const date = new Date(curr.scheduled_date).toISOString().split('T')[0];
       if (!acc[date]) {
         acc[date] = {
           date,
           collections: [],
           totalAmount: 0,
           completedAmount: 0,
           pendingAmount: 0,
         };
       }
       
       acc[date].collections.push(curr);
       acc[date].totalAmount += parseFloat(curr.amount.toString());
       
       if (curr.status === 'completed') {
         acc[date].completedAmount += parseFloat(curr.amount.toString());
       } else if (curr.status === 'pending') {
         acc[date].pendingAmount += parseFloat(curr.amount.toString());
       }
       
       return acc;
     }, {} as Record<string, any>);
     
     return {
       startDate,
       endDate,
       totalCollected: collectionsData
         .filter(c => c.status === 'completed')
         .reduce((sum, c) => sum + parseFloat(c.amount.toString()), 0),
       totalPending: collectionsData
         .filter(c => c.status === 'pending')
         .reduce((sum, c) => sum + parseFloat(c.amount.toString()), 0),
       dailyData: Object.values(groupedByDate),
       rawData: collectionsData,
     };
   }
   
   // Implement other report methods similarly
   ```

### Phase 4: Frontend Implementation

1. **Enhance Reports Page**
   - Update `client/src/pages/reports/index.tsx` to include a more comprehensive report selection UI
   - Add date range pickers and filtering options
   - Implement tabbed interface for different report types

2. **Daily Collections Report Component**
   ```tsx
   // client/src/components/reports/DailyCollectionsReport.tsx
   import { useState } from "react";
   import { useQuery } from "@tanstack/react-query";
   import { DateRangePicker } from "@/components/ui/date-range-picker";
   import { Button } from "@/components/ui/button";
   import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
   import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
   import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
   import { Download, Printer } from "lucide-react";
   import { formatCurrency, formatDate } from "@/lib/utils";
   
   export function DailyCollectionsReport() {
     const [dateRange, setDateRange] = useState({
       from: new Date(new Date().setDate(new Date().getDate() - 7)),
       to: new Date(),
     });
     const [status, setStatus] = useState<string | undefined>(undefined);
     const [agentId, setAgentId] = useState<string | undefined>(undefined);
     const [exportFormat, setExportFormat] = useState<string>("pdf");
     
     const { data, isLoading } = useQuery({
       queryKey: [
         '/api/companies/current/reports/daily-collections',
         {
           startDate: dateRange.from.toISOString().split('T')[0],
           endDate: dateRange.to.toISOString().split('T')[0],
           status,
           agentId,
         }
       ],
     });
     
     // Handle export functions
     const handleExport = () => {
       const params = new URLSearchParams({
         startDate: dateRange.from.toISOString().split('T')[0],
         endDate: dateRange.to.toISOString().split('T')[0],
         format: exportFormat,
       });
       
       if (status) params.append('status', status);
       if (agentId) params.append('agentId', agentId);
       
       window.open(`/api/companies/current/reports/export/daily-collections?${params.toString()}`, '_blank');
     };
     
     // Render daily collections report table and charts
     // Include summary statistics at the top
     // ...
   }
   ```

3. **P&L Statement Component**
   ```tsx
   // client/src/components/reports/ProfitLossReport.tsx
   import { useState } from "react";
   import { useQuery } from "@tanstack/react-query";
   import { DateRangePicker } from "@/components/ui/date-range-picker";
   import { Button } from "@/components/ui/button";
   import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
   import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
   import { Download, Printer } from "lucide-react";
   import { formatCurrency } from "@/lib/utils";
   
   export function ProfitLossReport() {
     const [dateRange, setDateRange] = useState({
       from: new Date(new Date().setDate(new Date().getDate() - 30)),
       to: new Date(),
     });
     const [exportFormat, setExportFormat] = useState<string>("pdf");
     
     const { data, isLoading } = useQuery({
       queryKey: [
         '/api/companies/current/reports/profit-loss',
         {
           startDate: dateRange.from.toISOString().split('T')[0],
           endDate: dateRange.to.toISOString().split('T')[0],
         }
       ],
     });
     
     // Handle export function
     // ...
     
     return (
       <div className="space-y-6">
         {/* Date range and export controls */}
         
         {/* P&L statement table */}
         <Card>
           <CardHeader>
             <CardTitle>Profit & Loss Statement</CardTitle>
             <CardDescription>
               Financial performance from {dateRange.from.toLocaleDateString()} to {dateRange.to.toLocaleDateString()}
             </CardDescription>
           </CardHeader>
           <CardContent>
             {isLoading ? (
               <div>Loading...</div>
             ) : (
               <Table>
                 <TableHeader>
                   <TableRow>
                     <TableHead className="w-[300px]">Category</TableHead>
                     <TableHead className="text-right">Amount (₹)</TableHead>
                   </TableRow>
                 </TableHeader>
                 <TableBody>
                   {/* Income section */}
                   <TableRow className="font-medium bg-muted/50">
                     <TableCell colSpan={2}>Income</TableCell>
                   </TableRow>
                   <TableRow>
                     <TableCell className="pl-6">Interest Income</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.incomeBreakdown.interestIncome, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                   <TableRow>
                     <TableCell className="pl-6">Fine/Penalty Income</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.incomeBreakdown.fineIncome, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                   <TableRow>
                     <TableCell className="pl-6">Processing Fee Income</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.incomeBreakdown.processingFeeIncome, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                   <TableRow className="font-medium">
                     <TableCell>Total Income</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.totalIncome, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                   
                   {/* Expense section */}
                   <TableRow className="font-medium bg-muted/50">
                     <TableCell colSpan={2}>Expenses</TableCell>
                   </TableRow>
                   {data?.expenseBreakdown.map((expense, i) => (
                     <TableRow key={i}>
                       <TableCell className="pl-6">{expense.category}</TableCell>
                       <TableCell className="text-right">{formatCurrency(expense.amount, 'INR', 'en-IN')}</TableCell>
                     </TableRow>
                   ))}
                   <TableRow className="font-medium">
                     <TableCell>Total Expenses</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.totalExpenses, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                   
                   {/* Net Profit/Loss */}
                   <TableRow className="font-bold text-lg">
                     <TableCell>Net Profit/Loss</TableCell>
                     <TableCell className="text-right">{formatCurrency(data?.netProfit, 'INR', 'en-IN')}</TableCell>
                   </TableRow>
                 </TableBody>
               </Table>
             )}
           </CardContent>
         </Card>
       </div>
     );
   }
   ```

4. **Enhance Export Functionality**

   Create a utility for handling CSV exports:
   ```typescript
   // client/src/lib/exportUtils.ts
   export function generateCsv(data: any[], columns: { key: string, header: string }[]): string {
     // Add headers
     let csvContent = columns.map(col => `"${col.header}"`).join(',') + '\n';
     
     // Add rows
     csvContent += data.map(row => {
       return columns.map(col => {
         const value = row[col.key];
         return `"${value !== undefined && value !== null ? value : ''}"`;
       }).join(',');
     }).join('\n');
     
     return csvContent;
   }
   
   export function downloadCsv(csvContent: string, filename: string): void {
     const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
     const link = document.createElement('a');
     const url = URL.createObjectURL(blob);
     
     link.setAttribute('href', url);
     link.setAttribute('download', filename);
     link.style.visibility = 'hidden';
     
     document.body.appendChild(link);
     link.click();
     document.body.removeChild(link);
   }
   ```

### Phase 5: PDF Export Enhancement

1. **Extend PDF Generator**

   Update `server/utils/pdfGenerator.ts`:
   ```typescript
   import PDFDocument from 'pdfkit';
   import { Collection, Payment, Customer, DailyCollectionReport, ProfitLossReport } from '@shared/schema';
   import * as fs from 'fs';
   import errorLogger from './errorLogger';

   export class PDFGenerator {
     // Existing methods...

     static async generateDailyCollectionsReport(
       report: DailyCollectionReport,
       companyName: string
     ): Promise<Buffer> {
       return new Promise((resolve, reject) => {
         try {
           const doc = new PDFDocument({
             size: 'A4',
             margins: { top: 50, bottom: 50, left: 50, right: 50 }
           });
           
           const chunks: Buffer[] = [];
           doc.on('data', chunk => chunks.push(chunk));
           doc.on('end', () => resolve(Buffer.concat(chunks)));
           
           // Report header
           doc.fontSize(20).font('Helvetica-Bold').text(`Daily Collections Report`, { align: 'center' });
           doc.fontSize(14).font('Helvetica').text(companyName, { align: 'center' });
           doc.fontSize(12).text(`Period: ${report.startDate} to ${report.endDate}`, { align: 'center' });
           doc.moveDown();
           
           // Summary section
           doc.fontSize(14).font('Helvetica-Bold').text('Summary', { underline: true });
           doc.moveDown(0.5);
           doc.fontSize(12).font('Helvetica');
           doc.text(`Total Collections: ₹${report.totalCollected.toLocaleString()}`);
           doc.text(`Total Pending: ₹${report.totalPending.toLocaleString()}`);
           doc.moveDown();
           
           // Daily breakdown
           doc.fontSize(14).font('Helvetica-Bold').text('Daily Breakdown', { underline: true });
           doc.moveDown(0.5);
           
           // Table header
           const tableTop = doc.y;
           const colWidths = [100, 130, 130, 130];
           const colPositions = [
             doc.page.margins.left,
             doc.page.margins.left + colWidths[0],
             doc.page.margins.left + colWidths[0] + colWidths[1],
             doc.page.margins.left + colWidths[0] + colWidths[1] + colWidths[2],
           ];
           
           // Draw table header
           doc.font('Helvetica-Bold').fontSize(10);
           doc.text('Date', colPositions[0], tableTop);
           doc.text('Total Amount', colPositions[1], tableTop);
           doc.text('Completed Amount', colPositions[2], tableTop);
           doc.text('Pending Amount', colPositions[3], tableTop);
           
           doc.moveTo(doc.page.margins.left, tableTop + 15)
             .lineTo(doc.page.width - doc.page.margins.right, tableTop + 15)
             .stroke();
           
           let y = tableTop + 20;
           
           // Draw table rows
           report.dailyData.forEach((dayData, index) => {
             // Check if we need a new page
             if (y > doc.page.height - 100) {
               doc.addPage();
               y = doc.page.margins.top;
             }
             
             doc.font('Helvetica').fontSize(10);
             doc.text(dayData.date, colPositions[0], y);
             doc.text(`₹${dayData.totalAmount.toLocaleString()}`, colPositions[1], y);
             doc.text(`₹${dayData.completedAmount.toLocaleString()}`, colPositions[2], y);
             doc.text(`₹${dayData.pendingAmount.toLocaleString()}`, colPositions[3], y);
             
             y += 20;
             
             // Draw a light line between rows
             if (index < report.dailyData.length - 1) {
               doc.moveTo(doc.page.margins.left, y - 5)
                 .lineTo(doc.page.width - doc.page.margins.right, y - 5)
                 .lineWidth(0.5)
                 .stroke();
             }
           });
           
           // Collection details (if space allows)
           if (report.rawData && report.rawData.length > 0) {
             doc.addPage();
             
             doc.fontSize(14).font('Helvetica-Bold').text('Collection Details', { underline: true });
             doc.moveDown(0.5);
             
             // Collection details table
             // ... (similar table implementation for individual collections)
           }
           
           // Footer
           doc.fontSize(10).text(`Generated on: ${new Date().toLocaleDateString()}`, { align: 'center' });
           
           doc.end();
         } catch (error) {
           errorLogger.logError('Failed to generate PDF collections report', 'pdf-generator', error as Error);
           reject(error);
         }
       });
     }
     
     static async generateProfitLossReport(
       report: ProfitLossReport,
       companyName: string
     ): Promise<Buffer> {
       // Similar implementation for P&L report
       // ...
     }

     // Add more report generators as needed
   }
   ```

2. **Create Report Export Service**

   Create a new service in `server/utils/reportExportService.ts`:
   ```typescript
   import { PDFGenerator } from './pdfGenerator';
   import { storage } from '../storage';
   import errorLogger from './errorLogger';

   export class ReportExportService {
     static async generatePdfReport(
       companyId: number,
       reportType: string,
       params: any
     ): Promise<Buffer> {
       try {
         const company = await storage.getCompany(companyId);
         
         if (!company) {
           throw new Error('Company not found');
         }
         
         switch(reportType) {
           case 'daily-collections':
             const dailyCollectionsReport = await storage.getDailyCollectionsReport(
               companyId,
               params.startDate,
               params.endDate,
               params.status,
               params.agentId ? parseInt(params.agentId, 10) : undefined,
               params.branchId ? parseInt(params.branchId, 10) : undefined,
               params.paymentMethod
             );
             return await PDFGenerator.generateDailyCollectionsReport(dailyCollectionsReport, company.name);
             
           case 'profit-loss':
             const plReport = await storage.getProfitLossReport(
               companyId,
               params.startDate,
               params.endDate,
               params.branchId ? parseInt(params.branchId, 10) : undefined
             );
             return await PDFGenerator.generateProfitLossReport(plReport, company.name);
             
           // Add cases for other report types
             
           default:
             throw new Error('Unknown report type');
         }
       } catch (error) {
         errorLogger.logError(`Failed to generate PDF report: ${reportType}`, 'report-export-service', error as Error);
         throw error;
       }
     }
     
     static async generateCsvReport(
       companyId: number,
       reportType: string,
       params: any
     ): Promise<string> {
       try {
         // Similar switch statement for CSV generation
         // For each report type, fetch data and format as CSV
         // Return CSV string
       } catch (error) {
         errorLogger.logError(`Failed to generate CSV report: ${reportType}`, 'report-export-service', error as Error);
         throw error;
       }
     }
   }
   ```

## Implementation Timeline

### Week 1: Database Schema and API Endpoints
- Day 1-2: Create expense and fine management schemas
- Day 3-4: Implement backend API endpoints
- Day 5: Update storage interface and implement core report methods

### Week 2: Report Frontend Components
- Day 1-2: Implement daily collections and day sheet reports
- Day 3-4: Implement customer and agent reports
- Day 5: Implement P&L statement report

### Week 3: Export Functionality and Testing
- Day 1-2: Enhance PDF generation for all report types
- Day 3: Implement CSV export functionality
- Day 4-5: Testing and bug fixing

## Future Enhancements

1. **Expense Management Module**
   - A complete UI for adding, editing, and managing expenses
   - Expense approval workflows
   - Expense categorization and tagging

2. **Advanced Analytics**
   - Trend analysis and forecasting
   - AI-powered insights on loan performance
   - Risk assessment metrics

3. **Shareholder Portal**
   - Dedicated portal for shareholders to view company performance
   - Automatic report generation and delivery
   - Investment tracking and returns calculation

4. **Multi-branch Consolidated Reports**
   - Roll-up reports for companies with multiple branches
   - Branch comparison capabilities

5. **Tax Reports**
   - Generate tax-related reports
   - GST/VAT compliance reports
   - Year-end financial statements

## Technical Considerations

1. **Performance**
   - Index key columns for report queries
   - Implement pagination for large datasets
   - Consider caching frequently requested reports

2. **Security**
   - Enforce strict role-based access for financial reports
   - Log all report access for audit purposes
   - Encrypt sensitive financial data in exports

3. **Scalability**
   - Design report generation to work with large datasets
   - Consider background processing for complex reports
   - Implement report result caching

4. **UI/UX**
   - Ensure a consistent design across all report pages
   - Provide interactive elements (sorting, filtering) in report views
   - Implement responsive design for mobile devices