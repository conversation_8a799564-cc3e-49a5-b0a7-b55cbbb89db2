import { Express, Request, Response } from 'express';
import { storage } from '../storage';
import { generateToken } from '../utils/jwt';
import bcrypt from 'bcrypt';
import { loginSchema, registerSchema, insertCompanySchema } from '../../shared/schema';
import { AuthRequest, authMiddleware } from '../middleware/auth';
import { initializeSystemAccounts } from '../financialManagement';

export function registerAuthRoutes(app: Express): void {
  // Login route
  app.post('/api/auth/login', async (req: Request, res: Response) => {
    try {
      const result = loginSchema.safeParse(req.body);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      const { email, password } = result.data;
      const user = await storage.getUserByEmail(email);

      if (!user) {
        return res.status(401).json({ message: 'Invalid email or password' });
      }

      const isPasswordValid = await bcrypt.compare(password, user.password);

      if (!isPasswordValid) {
        return res.status(401).json({ message: 'Invalid email or password' });
      }

      // Check if user has a primary company in user_companies table
      let primaryCompanyId = null;
      let company = null;
      let company_name = null;

      try {
        // Get all companies for the user with a single query
        const userCompanies = await storage.getUserCompanies(user.id);
        console.log(`Found ${userCompanies.length} companies for user ${user.id}`);

        if (userCompanies.length > 0) {
          // First try to find the primary company
          const primaryUserCompany = userCompanies.find(uc => uc.is_primary);

          if (primaryUserCompany) {
            // Use the primary company from user_companies table
            primaryCompanyId = primaryUserCompany.company_id;
            company = primaryUserCompany.company;
            company_name = company ? company.name : null;

            console.log('Found primary company for user:', {
              user_id: user.id,
              company_id: primaryCompanyId,
              company_name,
              is_primary: true
            });
          } else {
            // If no primary company is set, use the first company in the list
            const firstUserCompany = userCompanies[0];
            primaryCompanyId = firstUserCompany.company_id;
            company = firstUserCompany.company;
            company_name = company ? company.name : null;

            console.log('No primary company found, using first company:', {
              user_id: user.id,
              company_id: primaryCompanyId,
              company_name,
              is_primary: false
            });

            // Automatically set this company as primary for better user experience
            try {
              await storage.setUserCompanyAsPrimary(firstUserCompany.id, user.id);
              console.log(`Set company ${primaryCompanyId} as primary for user ${user.id}`);
            } catch (setPrimaryError) {
              console.error('Error setting first company as primary:', setPrimaryError);
            }
          }
        } else if (user.company_id) {
          // Fallback to user.company_id if no companies in user_companies table
          primaryCompanyId = user.company_id;
          company = await storage.getCompany(user.company_id);
          company_name = company ? company.name : null;

          console.log('No companies in user_companies table, using user.company_id:', {
            user_id: user.id,
            company_id: primaryCompanyId,
            company_name
          });

          // Create user-company association with is_primary=true
          if (company) {
            try {
              const userCompany = await storage.createUserCompany({
                user_id: user.id,
                company_id: primaryCompanyId,
                is_primary: true,
                role: user.role
              });
              console.log(`Created user-company association for user ${user.id} and company ${primaryCompanyId}:`, userCompany);
            } catch (createError) {
              console.error('Error creating user-company association:', createError);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching company details:', error);
        // Fallback to user.company_id if there's an error
        if (user.company_id) {
          primaryCompanyId = user.company_id;
          try {
            company = await storage.getCompany(user.company_id);
            company_name = company ? company.name : null;
          } catch (companyError) {
            console.error('Error fetching fallback company details:', companyError);
          }
        }
      }

      // Generate token with the primary company ID
      const token = generateToken({
        userId: user.id,
        role: user.role,
        companyId: primaryCompanyId
      });

      // Create user data with explicit company name handling
      const userData = {
        id: user.id,
        email: user.email,
        full_name: user.full_name,
        role: user.role,
        company_id: primaryCompanyId,
        company_name: company_name || (primaryCompanyId ? `Company ${primaryCompanyId}` : null)
      };

      // Log the final user data being returned
      console.log('Login successful, returning user data:', {
        id: userData.id,
        email: userData.email,
        company_id: userData.company_id,
        company_name: userData.company_name,
        role: userData.role
      });

      // Set token as HTTP-only cookie
      res.cookie('auth_token', token, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });

      return res.json({
        token,
        user: userData
      });
    } catch (error) {
      console.error('Login error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Refresh token with new company context
  app.post('/api/auth/refresh-token', authMiddleware, async (req: AuthRequest, res: Response) => {
    try {
      const { company_id } = req.body;

      console.log('Token refresh requested for company ID:', company_id);

      if (!req.user) {
        return res.status(401).json({ message: 'Authentication required' });
      }

      const userId = req.user.id;
      const userRole = req.user.role;

      // Validate that the user has access to this company
      if (company_id && company_id !== req.user.company_id) {
        // If not a saas_admin, verify the user is associated with this company
        if (userRole !== 'saas_admin') {
          const userCompanies = await storage.getUserCompanies(userId);
          const hasCompanyAccess = userCompanies.some(uc =>
            uc.company_id === company_id ||
            (uc.company && uc.company.id === company_id)
          );

          if (!hasCompanyAccess) {
            console.log(`User ${userId} does not have access to company ${company_id}`);
            return res.status(403).json({ message: 'Access denied to this company' });
          }
        }
      }

      // Generate a new token with the updated company ID
      const newToken = generateToken({
        userId,
        role: userRole,
        companyId: company_id || req.user.company_id
      });

      // Set the new token as a cookie
      res.cookie('auth_token', newToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        maxAge: 24 * 60 * 60 * 1000, // 1 day
        path: '/'
      });

      console.log(`Token refreshed for user ${userId} with company ${company_id}`);

      return res.json({
        message: 'Token refreshed',
        token: newToken
      });
    } catch (error) {
      console.error('Token refresh error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });

  // Register route (partial implementation - will need to be completed)
  app.post('/api/auth/register', async (req: Request, res: Response) => {
    try {
      // Extract company data if present
      const { company, subscriptionPlanId, ...userData } = req.body;

      // Validate user data
      const result = registerSchema.safeParse(userData);

      if (!result.success) {
        return res.status(400).json({ message: 'Invalid input', errors: result.error.errors });
      }

      // Check if email already exists
      const existingUser = await storage.getUserByEmail(result.data.email);
      if (existingUser) {
        return res.status(400).json({ message: 'Email already exists' });
      }

      // Check if email already exists in the same company
      if (result.data.company_id) {
        const existingEmailInCompany = await storage.getUserByEmailAndCompany(result.data.email, result.data.company_id);
        if (existingEmailInCompany) {
          return res.status(400).json({
            message: 'Email already exists in this company',
            error: 'This email is already registered with another user in this company. Please use a different email address.',
            field: 'email'
          });
        }
      }

      let companyId = result.data.company_id;

      // If company data is provided and user role is company_admin, create a new company
      if (company && result.data.role === 'company_admin' && !companyId) {
        try {
          // Validate company data
          const companyResult = insertCompanySchema.safeParse(company);

          if (!companyResult.success) {
            return res.status(400).json({ message: 'Invalid company data', errors: companyResult.error.errors });
          }

          // Create company
          const newCompany = await storage.createCompany(companyResult.data);
          companyId = newCompany.id;

          console.log('Created new company during registration:', newCompany);

          // Initialize the Chart of Accounts system for this company
          try {
            await initializeSystemAccounts(companyId);
            console.log(`Initialized system accounts for new company ${companyId}`);
          } catch (accountsError) {
            console.error(`Failed to initialize system accounts for company ${companyId}:`, accountsError);
            // We don't fail the whole request if account initialization fails
          }

          // Note: Subscription handling code would go here
        } catch (error) {
          console.error('Error creating company during registration:', error);
          return res.status(500).json({ message: 'Error creating company' });
        }
      }

      // Create user with the company_id if it was created
      const { confirmPassword, ...finalUserData } = result.data;
      const user = await storage.createUser({
        ...finalUserData,
        company_id: companyId || finalUserData.company_id
      });

      // Note: User-company association code would go here

      // Generate token and return response
      const token = generateToken({
        userId: user.id,
        role: user.role,
        companyId: user.company_id
      });

      return res.status(201).json({
        message: 'User registered successfully',
        token,
        user: {
          id: user.id,
          email: user.email,
          full_name: user.full_name,
          role: user.role,
          company_id: user.company_id
        }
      });
    } catch (error) {
      console.error('Registration error:', error);
      return res.status(500).json({ message: 'Server error' });
    }
  });
}
