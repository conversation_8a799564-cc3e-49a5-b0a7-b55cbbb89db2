import React, { useState } from 'react';
import { UserPermissionViewer } from '@/components/permissions';
import { useLocation } from 'wouter';

export default function UserPermissionsPage() {
  const [location] = useLocation();
  const searchParams = new URLSearchParams(location.split('?')[1] || '');
  const [selectedUserId, setSelectedUserId] = useState<number | undefined>(
    searchParams.get('userId') ? parseInt(searchParams.get('userId')!) : undefined
  );

  return (
    <div className="container mx-auto py-6">
      <UserPermissionViewer
        userId={selectedUserId}
        onUserSelect={setSelectedUserId}
      />
    </div>
  );
}
